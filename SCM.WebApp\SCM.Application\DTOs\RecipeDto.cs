namespace SCM.Application.DTOs;

public class RecipeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public decimal Yield { get; set; } = 1;
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public string? Instructions { get; set; }
    public string? Notes { get; set; }
    public decimal? Cost { get; set; }
    public bool IsSubRecipe { get; set; } = false;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public ICollection<RecipeIngredientDto> Ingredients { get; set; } = new List<RecipeIngredientDto>();
}

public class RecipeIngredientDto
{
    public int Id { get; set; }
    public int RecipeId { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public string? Notes { get; set; }
    public int Sequence { get; set; }
    public decimal? Cost { get; set; }
    public decimal? TotalCost { get; set; }
    public decimal? WastagePercentage { get; set; }
}

public class CreateRecipeDto
{
    public string Name { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public decimal Yield { get; set; } = 1;
    public int? UnitId { get; set; }
    public string? Instructions { get; set; }
    public string? Notes { get; set; }
    public bool IsSubRecipe { get; set; } = false;
    public bool IsActive { get; set; } = true;
    public ICollection<CreateRecipeIngredientDto> Ingredients { get; set; } = new List<CreateRecipeIngredientDto>();
}

public class CreateRecipeIngredientDto
{
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public int? UnitId { get; set; }
    public string? Notes { get; set; }
    public int Sequence { get; set; }
    public decimal? WastagePercentage { get; set; }
}

public class UpdateRecipeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public decimal Yield { get; set; } = 1;
    public int? UnitId { get; set; }
    public string? Instructions { get; set; }
    public string? Notes { get; set; }
    public bool IsSubRecipe { get; set; } = false;
    public bool IsActive { get; set; } = true;
}

public class UpdateRecipeIngredientDto
{
    public int Id { get; set; }
    public int RecipeId { get; set; }
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public int? UnitId { get; set; }
    public string? Notes { get; set; }
    public int Sequence { get; set; }
    public decimal? WastagePercentage { get; set; }
}

public class RecipeListDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public decimal Yield { get; set; } = 1;
    public string? UnitName { get; set; }
    public decimal? Cost { get; set; }
    public int IngredientCount { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
}
