import { Injectable } from '@angular/core';
import { Observable, of, forkJoin } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { ApiService } from './api.service';
import { CompanyService } from './company.service';
import { LocationService } from './location.service';
import { StoreService } from './store.service';
import { CostCenterService } from './cost-center.service';
import { Company } from '../models/company.model';
import { Location } from '../models/location.model';
import { Store } from '../models/store.model';
import { CostCenter, CostCenterList } from '../models/cost-center.model';

export interface BusinessUnit {
  id: number;
  name: string;
  type: string;
  isActive: boolean;
  children?: BusinessLocation[];
}

export interface BusinessLocation {
  id: number;
  name: string;
  type: string;
  isActive: boolean;
  businessUnitId: number;
  children?: BusinessStore[];
}

export interface BusinessStore {
  id: number;
  name: string;
  type: string;
  isActive: boolean;
  locationId: number;
  children?: BusinessCostCenter[];
}

export interface BusinessCostCenter {
  id: number;
  name: string;
  type: string;
  isActive: boolean;
  storeId: number;
}

@Injectable({
  providedIn: 'root'
})
export class BusinessStructureService {
  constructor(
    private apiService: ApiService,
    private companyService: CompanyService,
    private locationService: LocationService,
    private storeService: StoreService,
    private costCenterService: CostCenterService
  ) { }

  // Get the full business structure using real API calls
  getBusinessStructure(): Observable<BusinessUnit[]> {
    return this.companyService.getAll().pipe(
      map(companies => {
        // Create a static "Business Unit" parent node
        const businessUnit: BusinessUnit = {
          id: 0,
          name: 'Business Unit',
          type: 'root',
          isActive: true,
          children: companies.map(company => ({
            id: company.id,
            name: company.name,
            type: 'company',
            isActive: company.isActive,
            businessUnitId: 0,
            children: []
          }))
        };

        return [businessUnit];
      }),
      catchError(error => {
        console.error('Error fetching business structure:', error);
        // Return a minimal structure in case of error
        return of([{
          id: 0,
          name: 'Business Unit',
          type: 'root',
          isActive: true,
          children: []
        }]);
      })
    );
  }

  // Get a fully loaded business structure with all children
  // Warning: This can be resource-intensive for large structures
  getFullBusinessStructure(): Observable<BusinessUnit[]> {
    return this.companyService.getAll().pipe(
      switchMap(companies => {
        // Create a static "Business Unit" parent node
        const businessUnit: BusinessUnit = {
          id: 0,
          name: 'Business Unit',
          type: 'root',
          isActive: true,
          children: []
        };

        // Load all locations, stores, and cost centers in parallel
        const observables = companies.map(company =>
          this.locationService.getByCompany(company.id).pipe(
            map(locations => {
              const companyNode: BusinessLocation = {
                id: company.id,
                name: company.name,
                type: 'company',
                isActive: company.isActive,
                businessUnitId: 0,
                children: locations.map(location => ({
                  id: location.id,
                  name: location.name,
                  type: 'location',
                  isActive: location.isActive,
                  locationId: company.id,
                  children: []
                }))
              };

              return companyNode;
            }),
            catchError(error => {
              console.error(`Error loading locations for company ${company.id}:`, error);
              return of({
                id: company.id,
                name: company.name,
                type: 'company',
                isActive: company.isActive,
                businessUnitId: 0,
                children: []
              });
            })
          )
        );

        if (observables.length === 0) {
          return of([businessUnit]);
        }

        return forkJoin(observables).pipe(
          map(companyNodes => {
            businessUnit.children = companyNodes;
            return [businessUnit];
          }),
          catchError(error => {
            console.error('Error loading full business structure:', error);
            return of([businessUnit]);
          })
        );
      }),
      catchError(error => {
        console.error('Error fetching companies:', error);
        return of([{
          id: 0,
          name: 'Business Unit',
          type: 'root',
          isActive: true,
          children: []
        }]);
      })
    );
  }

  // Get companies (first level)
  getCompanies(): Observable<BusinessLocation[]> {
    return this.companyService.getAll().pipe(
      map(companies => companies.map(company => ({
        id: company.id,
        name: company.name,
        type: 'company',
        isActive: company.isActive,
        businessUnitId: 0,
        children: []
      }))),
      catchError(error => {
        console.error('Error fetching companies:', error);
        return of([]);
      })
    );
  }

  // Get locations by company (second level)
  getLocationsByCompany(companyId: number): Observable<BusinessStore[]> {
    return this.locationService.getByCompany(companyId).pipe(
      map(locations => locations.map(location => ({
        id: location.id,
        name: location.name,
        type: 'location',
        isActive: location.isActive,
        locationId: companyId,
        children: []
      }))),
      catchError(error => {
        console.error(`Error fetching locations for company ${companyId}:`, error);
        return of([]);
      })
    );
  }

  // Get stores by location (third level)
  getStoresByLocation(locationId: number): Observable<BusinessStore[]> {
    return this.storeService.getByLocation(locationId).pipe(
      map(stores => stores.map(store => ({
        id: store.id,
        name: store.name,
        type: 'store',
        isActive: store.isActive,
        locationId: locationId,
        children: []
      }))),
      catchError(error => {
        console.error(`Error fetching stores for location ${locationId}:`, error);
        return of([]);
      })
    );
  }

  // Get cost centers by store (fourth level)
  getCostCentersByStore(storeId: number): Observable<BusinessCostCenter[]> {
    return this.costCenterService.getByStore(storeId).pipe(
      map(costCenters => costCenters.map(costCenter => ({
        id: costCenter.id,
        name: costCenter.name,
        type: 'costCenter',
        isActive: costCenter.isActive,
        storeId: storeId
      }))),
      catchError(error => {
        console.error(`Error fetching cost centers for store ${storeId}:`, error);
        return of([]);
      })
    );
  }
}
