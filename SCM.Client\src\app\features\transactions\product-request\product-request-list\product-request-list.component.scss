.page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;

  mat-form-field {
    flex: 1;
    min-width: 200px;
  }
}

.table-container {
  position: relative;
  min-height: 200px;
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;

  p {
    margin-top: 16px;
    color: rgba(0, 0, 0, 0.54);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: rgba(0, 0, 0, 0.24);
    margin-bottom: 16px;
  }

  p {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.54);
  }
}

table {
  width: 100%;
}

.clickable-row {
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.status-chip {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-draft {
  background-color: #e0e0e0;
  color: #616161;
}

.status-submitted {
  background-color: #bbdefb;
  color: #1976d2;
}

.status-approved {
  background-color: #c8e6c9;
  color: #388e3c;
}

.status-rejected {
  background-color: #ffcdd2;
  color: #d32f2f;
}

.status-completed {
  background-color: #d1c4e9;
  color: #512da8;
}

.status-cancelled {
  background-color: #ffe0b2;
  color: #f57c00;
}

.mat-column-actions {
  width: 60px;
  text-align: center;
}

.mat-column-status {
  width: 120px;
}

.mat-column-referenceNumber {
  width: 150px;
}
