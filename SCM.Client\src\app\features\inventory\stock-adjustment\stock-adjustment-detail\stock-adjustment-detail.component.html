<div class="container">
  <div class="header">
    <h1>{{ isEditMode ? 'Edit Stock Adjustment' : 'New Stock Adjustment' }}</h1>
    <div class="header-actions">
      <button mat-button color="warn" (click)="cancel()">
        <mat-icon>close</mat-icon> Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveStockAdjustment()" [disabled]="adjustmentForm.invalid || isLoading">
        <mat-icon>save</mat-icon> Save
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockAdjustment?.status === 'Draft'" (click)="completeStockAdjustment()" [disabled]="isLoading">
        <mat-icon>check_circle</mat-icon> Complete Adjustment
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && stockAdjustment?.status === 'Draft'" (click)="cancelStockAdjustment()" [disabled]="isLoading">
        <mat-icon>cancel</mat-icon> Cancel Adjustment
      </button>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <form [formGroup]="adjustmentForm" *ngIf="!isLoading">
    <mat-card class="form-card">
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Adjustment Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="adjustmentDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="adjustmentForm.get('adjustmentDate')?.hasError('required')">
              Adjustment date is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Cost Center</mat-label>
            <mat-select formControlName="costCenterId">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{ costCenter.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="adjustmentForm.get('costCenterId')?.hasError('required')">
              Cost center is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes</mat-label>
            <textarea matInput formControlName="notes" rows="3"></textarea>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <div class="section-header">
      <h2>Adjustment Items</h2>
      <button mat-mini-fab color="primary" (click)="addDetail()" [disabled]="adjustmentForm.disabled">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div formArrayName="details">
      <mat-card class="form-card" *ngFor="let detailForm of detailsFormArray.controls; let i = index" [formGroupName]="i">
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product</mat-label>
              <mat-select formControlName="productId" (selectionChange)="checkStockAvailability($event.value, i)">
                <mat-option *ngFor="let product of products" [value]="product.id">
                  {{ product.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="detailForm.get('productId')?.hasError('required')">
                Product is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Current Quantity</mat-label>
              <input matInput type="number" formControlName="currentQuantity" readonly>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Adjustment</mat-label>
              <input matInput type="number" formControlName="adjustmentQuantity" step="0.01">
              <mat-error *ngIf="detailForm.get('adjustmentQuantity')?.hasError('required')">
                Adjustment quantity is required
              </mat-error>
              <mat-error *ngIf="detailForm.get('adjustmentQuantity')?.hasError('negativeStock')">
                New quantity cannot be negative
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>New Quantity</mat-label>
              <input matInput type="number" formControlName="newQuantity" readonly>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Unit</mat-label>
              <mat-select formControlName="unitId">
                <mat-option *ngFor="let unit of units" [value]="unit.id">
                  {{ unit.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Reason</mat-label>
              <mat-select formControlName="reason">
                <mat-option *ngFor="let reason of adjustmentReasons" [value]="reason">
                  {{ reason }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="detailForm.get('reason')?.hasError('required')">
                Reason is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <input matInput formControlName="notes">
            </mat-form-field>
          </div>

          <div class="detail-actions" *ngIf="!adjustmentForm.disabled">
            <button mat-icon-button color="warn" (click)="removeDetail(i)" matTooltip="Remove Item">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <div class="no-items" *ngIf="detailsFormArray.length === 0">
        <p>No items added to this adjustment. Click the + button to add items.</p>
      </div>
    </div>

    <div class="form-actions">
      <button mat-button color="warn" (click)="cancel()">
        Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveStockAdjustment()" [disabled]="adjustmentForm.invalid || isLoading">
        Save
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockAdjustment?.status === 'Draft'" (click)="completeStockAdjustment()" [disabled]="isLoading">
        Complete Adjustment
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && stockAdjustment?.status === 'Draft'" (click)="cancelStockAdjustment()" [disabled]="isLoading">
        Cancel Adjustment
      </button>
    </div>
  </form>

  <div class="status-info" *ngIf="isEditMode && stockAdjustment">
    <div class="status-badge" [ngClass]="'status-' + stockAdjustment.status.toLowerCase()">
      {{ stockAdjustment.status }}
    </div>
    <div class="status-details" *ngIf="stockAdjustment.status !== 'Draft'">
      <p *ngIf="stockAdjustment.status === 'Completed'">
        Completed by {{ stockAdjustment.completedByName || 'Unknown' }} on {{ stockAdjustment.completedAt | date:'medium' }}
      </p>
      <p *ngIf="stockAdjustment.status === 'Cancelled'">
        Cancelled on {{ stockAdjustment.updatedAt | date:'medium' }}
      </p>
    </div>
  </div>
</div>
