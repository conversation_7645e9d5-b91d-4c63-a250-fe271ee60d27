using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IStoreService
{
    Task<IEnumerable<StoreDto>> GetAllStoresAsync();
    Task<StoreDto?> GetStoreByIdAsync(int id);
    Task<IEnumerable<StoreDto>> GetStoresByLocationIdAsync(int locationId);
    Task<IEnumerable<StoreDto>> GetStoresByCompanyIdAsync(int companyId);
    Task<StoreDto?> GetStoreWithLocationsAsync(int id);
    Task<StoreDto?> GetStoreWithCostCentersAsync(int id);
    Task<StoreDto> CreateStoreAsync(CreateStoreDto createStoreDto);
    Task UpdateStoreAsync(UpdateStoreDto updateStoreDto);
    Task DeleteStoreAsync(int id);
}
