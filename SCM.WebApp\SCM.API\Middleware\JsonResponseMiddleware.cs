using System.Text;
using System.Text.Json;

namespace SCM.API.Middleware;

public class JsonResponseMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<JsonResponseMiddleware> _logger;

    public JsonResponseMiddleware(RequestDelegate next, ILogger<JsonResponseMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Store the original body stream
        var originalBodyStream = context.Response.Body;

        try
        {
            // Create a new memory stream to capture the response
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            // Continue down the pipeline
            await _next(context);

            // If the response is not JSON, convert it to JSON
            if (!context.Response.ContentType?.Contains("application/json") ?? true)
            {
                // Only process certain status codes (4xx and 5xx)
                if (context.Response.StatusCode >= 400)
                {
                    // Read the response body
                    responseBody.Seek(0, SeekOrigin.Begin);
                    var responseText = await new StreamReader(responseBody).ReadToEndAsync();

                    // If the response is not empty and not already JSON
                    if (!string.IsNullOrEmpty(responseText) && !responseText.TrimStart().StartsWith("{"))
                    {
                        // Create a JSON response
                        var jsonResponse = JsonSerializer.Serialize(new { message = responseText });

                        // Clear the response body
                        responseBody.SetLength(0);

                        // Write the JSON response
                        context.Response.ContentType = "application/json";
                        var bytes = Encoding.UTF8.GetBytes(jsonResponse);
                        await responseBody.WriteAsync(bytes, 0, bytes.Length);
                    }
                }
            }

            // Copy the modified response to the original body stream
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in JsonResponseMiddleware");
            // Ensure we still have the original body stream
            context.Response.Body = originalBodyStream;

            // Create a JSON error response
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            context.Response.ContentType = "application/json";
            
            var jsonResponse = JsonSerializer.Serialize(new { message = "An error occurred processing the response" });
            await context.Response.WriteAsync(jsonResponse);
        }
    }
}

// Extension method to add the middleware to the pipeline
public static class JsonResponseMiddlewareExtensions
{
    public static IApplicationBuilder UseJsonResponse(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<JsonResponseMiddleware>();
    }
}
