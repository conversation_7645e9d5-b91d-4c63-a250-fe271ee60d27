using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IRecipeService
{
    Task<IEnumerable<RecipeListDto>> GetAllRecipesAsync();
    Task<RecipeDto?> GetRecipeByIdAsync(int id);
    Task<IEnumerable<RecipeListDto>> GetRecipesByProductIdAsync(int productId);
    Task<IEnumerable<RecipeListDto>> GetActiveRecipesAsync();
    Task<IEnumerable<RecipeListDto>> GetSubRecipesAsync();
    Task<RecipeDto> CreateRecipeAsync(CreateRecipeDto createRecipeDto);
    Task UpdateRecipeAsync(UpdateRecipeDto updateRecipeDto);
    Task DeleteRecipeAsync(int id);
    Task<RecipeIngredientDto> AddIngredientAsync(int recipeId, CreateRecipeIngredientDto createRecipeIngredientDto);
    Task UpdateIngredientAsync(UpdateRecipeIngredientDto updateRecipeIngredientDto);
    Task RemoveIngredientAsync(int recipeId, int ingredientId);
    Task<decimal> CalculateRecipeCostAsync(int recipeId);
}
