namespace SCM.Application.DTOs;

public class UnitImportDto
{
    public int TotalRows { get; set; }
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public List<UnitImportError> Errors { get; set; } = new List<UnitImportError>();
    public List<string> SuccessfulUnits { get; set; } = new List<string>();
}

public class UnitImportError
{
    public int RowNumber { get; set; }
    public string UnitName { get; set; } = string.Empty;
    public string UnitAbbreviation { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
}

public class UnitImportTemplate
{
    public string Name { get; set; } = string.Empty;
    public string Abbreviation { get; set; } = string.Empty;
    public string UnitGroup { get; set; } = string.Empty;
    public decimal ConversionFactor { get; set; } = 1;
    public decimal BaseConversionFactor { get; set; } = 1;
    public bool IsBaseUnit { get; set; } = false;
}
