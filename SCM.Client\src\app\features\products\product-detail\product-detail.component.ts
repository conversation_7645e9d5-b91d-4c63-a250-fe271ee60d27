import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ProductService } from '../../../core/services/product.service';
import { DepartmentService } from '../../../core/services/department.service';
import { BrandService } from '../../../core/services/brand.service';
import { UnitService } from '../../../core/services/unit.service';
import { SupplierService } from '../../../core/services/supplier.service';
import { TaxService } from '../../../core/services/tax.service';
import { ErrorService } from '../../../core/services/error.service';
import { forkJoin, catchError, of } from 'rxjs';

@Component({
  selector: 'app-product-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTabsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    RouterModule
  ],
  templateUrl: './product-detail.component.html',
  styleUrls: ['./product-detail.component.scss']
})
export class ProductDetailComponent implements OnInit {
  productForm!: FormGroup;
  isEditMode: boolean = false;
  productId: string | null = null;
  isLoading: boolean = false;

  // Data from API
  departments: any[] = [];
  brands: any[] = [];
  productGroups: any[] = [];
  units: any[] = [];
  suppliers: any[] = [];
  taxRates: any[] = [];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private productService: ProductService,
    private departmentService: DepartmentService,
    private brandService: BrandService,
    private unitService: UnitService,
    private supplierService: SupplierService,
    private taxService: TaxService,
    private errorService: ErrorService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadReferenceData();

    this.route.paramMap.subscribe(params => {
      this.productId = params.get('id');
      if (this.productId) {
        this.isEditMode = true;
        this.loadProductData(parseInt(this.productId, 10));
      }
    });
  }

  loadReferenceData(): void {
    this.isLoading = true;

    // Use forkJoin to load all reference data in parallel
    forkJoin({
      departments: this.departmentService.getAll().pipe(
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      ),
      brands: this.brandService.getAll().pipe(
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      ),
      units: this.unitService.getAll().pipe(
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      ),
      suppliers: this.supplierService.getAll().pipe(
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      ),
      taxes: this.taxService.getAll().pipe(
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      )
    })
    .subscribe({
      next: (results) => {
        this.departments = results.departments;
        this.brands = results.brands;
        this.units = results.units;
        this.suppliers = results.suppliers;
        this.taxRates = results.taxes;

        if (!this.isEditMode) {
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorService.handleError(error);
      }
    });
  }

  initForm(): void {
    this.productForm = this.fb.group({
      id: [null],
      code: ['', Validators.required],
      name: ['', Validators.required],
      departmentId: [null],
      brandId: [null],
      unitId: [null, Validators.required],
      salesUnitId: [null],
      salesUnitConversionFactor: [1],
      costPrice: [0, [Validators.required, Validators.min(0)]],
      salesPrice: [0, [Validators.required, Validators.min(0)]],
      minStock: [0, [Validators.min(0)]],
      maxStock: [0, [Validators.min(0)]],
      reorderPoint: [0, [Validators.min(0)]],
      taxId: [null],
      notes: [''],
      isStockItem: [true],
      isRecipe: [false],
      hasExpiry: [false],
      isProduction: [false],
      isSaleable: [true],
      allowDiscount: [true],
      isActive: [true]
    });
  }

  loadProductData(id: number): void {
    this.isLoading = true;

    this.productService.getById(id).subscribe({
      next: (product: any) => {
        this.isLoading = false;
        if (product) {
          this.productForm.patchValue({
            id: product.id,
            code: product.code,
            name: product.name,
            departmentId: product.departmentId,
            brandId: product.brandId,
            unitId: product.unitId,
            salesUnitId: product.salesUnitId,
            salesUnitConversionFactor: product.salesUnitConversionFactor || 1,
            costPrice: product.costPrice,
            salesPrice: product.salesPrice,
            minStock: product.minStock,
            maxStock: product.maxStock,
            reorderPoint: product.reorderPoint,
            taxId: product.taxId,
            notes: product.notes,
            isStockItem: product.isStockItem,
            isRecipe: product.isRecipe,
            hasExpiry: product.hasExpiry,
            isProduction: product.isProduction,
            isSaleable: product.isSaleable,
            allowDiscount: product.allowDiscount,
            isActive: product.isActive
          });
        } else {
          this.errorService.showError('Product not found');
          this.router.navigate(['/products']);
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorService.handleError(error);
      }
    });
  }

  saveProduct(): void {
    if (this.productForm.valid) {
      this.isLoading = true;
      const productData = this.productForm.value;

      if (this.isEditMode) {
        this.productService.update(productData.id, productData).subscribe({
          next: () => {
            this.isLoading = false;
            this.errorService.showSuccess('Product updated successfully');
            this.router.navigate(['/products']);
          },
          error: (error) => {
            this.isLoading = false;
            this.errorService.handleError(error);
          }
        });
      } else {
        this.productService.create(productData).subscribe({
          next: () => {
            this.isLoading = false;
            this.errorService.showSuccess('Product created successfully');
            this.router.navigate(['/products']);
          },
          error: (error) => {
            this.isLoading = false;
            this.errorService.handleError(error);
          }
        });
      }
    } else {
      this.markFormGroupTouched(this.productForm);
      this.errorService.showError('Please fix the errors in the form');
    }
  }

  cancel(): void {
    this.router.navigate(['/products']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
