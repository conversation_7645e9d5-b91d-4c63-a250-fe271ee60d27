<div class="page-container">
  <div class="page-header">
    <h1>{{ isEditMode ? 'Edit Recipe' : 'Add Recipe' }}</h1>
    <div class="header-actions">
      <button mat-button (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" (click)="saveRecipe()">Save</button>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <form [formGroup]="recipeForm" class="recipe-form" *ngIf="!isLoading">
    <mat-tab-group>
      <mat-tab label="Basic Information">
        <div class="tab-content">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Product</mat-label>
              <mat-select formControlName="productId">
                <mat-option *ngFor="let product of products" [value]="product.id">
                  {{product.code}} - {{product.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="recipeForm.get('productId')?.hasError('required')">
                Product is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Recipe Name</mat-label>
              <input matInput formControlName="name">
              <mat-error *ngIf="recipeForm.get('name')?.hasError('required')">
                Recipe Name is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <div class="yield-container">
              <mat-form-field appearance="outline">
                <mat-label>Yield</mat-label>
                <input matInput type="number" formControlName="yield" min="0.01" step="0.01">
                <mat-error *ngIf="recipeForm.get('yield')?.hasError('required')">
                  Yield is required
                </mat-error>
                <mat-error *ngIf="recipeForm.get('yield')?.hasError('min')">
                  Yield must be greater than 0
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Unit</mat-label>
                <mat-select formControlName="unitId">
                  <mat-option *ngFor="let unit of units" [value]="unit.id">
                    {{unit.name}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="recipeForm.get('unitId')?.hasError('required')">
                  Unit is required
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <textarea matInput formControlName="notes" rows="3"></textarea>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-checkbox formControlName="isSubRecipe">Sub Recipe</mat-checkbox>
            <mat-checkbox formControlName="isActive">Active</mat-checkbox>
          </div>
        </div>
      </mat-tab>

      <mat-tab label="Ingredients">
        <div class="tab-content">
          <div class="table-container mat-elevation-z2">
            <table mat-table [dataSource]="ingredients_.controls">
              <!-- Product Column -->
              <ng-container matColumnDef="ingredient">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <mat-select [formControl]="item.get('productId')">
                      <mat-option *ngFor="let product of products" [value]="product.id">
                        {{product.code}} - {{product.name}}
                      </mat-option>
                    </mat-select>
                    <mat-error *ngIf="item.get('productId')?.hasError('required')">
                      Product is required
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('quantity')" min="0.01" step="0.01">
                    <mat-error *ngIf="item.get('quantity')?.hasError('required')">
                      Required
                    </mat-error>
                    <mat-error *ngIf="item.get('quantity')?.hasError('min')">
                      Min 0.01
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Unit Column -->
              <ng-container matColumnDef="unit">
                <th mat-header-cell *matHeaderCellDef>Unit</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <mat-select [formControl]="item.get('unitId')">
                      <mat-option *ngFor="let unit of units" [value]="unit.id">
                        {{unit.name}}
                      </mat-option>
                    </mat-select>
                    <mat-error *ngIf="item.get('unitId')?.hasError('required')">
                      Required
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Wastage Column -->
              <ng-container matColumnDef="wastage">
                <th mat-header-cell *matHeaderCellDef>Wastage %</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('wastagePercentage')" min="0" step="0.1">
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Unit Cost Column -->
              <ng-container matColumnDef="unitCost">
                <th mat-header-cell *matHeaderCellDef>Unit Cost</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('unitCost')" readonly>
                    <span matTextPrefix>$&nbsp;</span>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Total Cost Column -->
              <ng-container matColumnDef="totalCost">
                <th mat-header-cell *matHeaderCellDef>Total Cost</th>
                <td mat-cell *matCellDef="let item">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="item.get('totalCost')" readonly>
                    <span matTextPrefix>$&nbsp;</span>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <button mat-icon-button color="warn" (click)="removeIngredient(i)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <div class="table-actions">
              <button mat-button color="primary" (click)="addIngredient()">
                <mat-icon>add</mat-icon> Add Ingredient
              </button>
            </div>
          </div>

          <div class="cost-summary">
            <div class="cost-row">
              <span>Total Cost:</span>
              <span class="cost-value">{{calculateTotalCost() | currency}}</span>
            </div>
            <div class="cost-row">
              <span>Cost Per Unit:</span>
              <span class="cost-value">{{calculateCostPerUnit() | currency}}</span>
            </div>
          </div>
        </div>
      </mat-tab>

      <mat-tab label="Instructions">
        <div class="tab-content">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Preparation Instructions</mat-label>
              <textarea matInput formControlName="instructions" rows="15"></textarea>
            </mat-form-field>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </form>
</div>
