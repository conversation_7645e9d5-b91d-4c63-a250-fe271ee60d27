.page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;

    .status-chip {
      margin-left: 8px;
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
    }

    .status-draft {
      background-color: #e0e0e0;
      color: #616161;
    }

    .status-submitted {
      background-color: #fff8e1;
      color: #ff8f00;
    }

    .status-approved {
      background-color: #e8f5e9;
      color: #2e7d32;
    }

    .status-rejected {
      background-color: #ffebee;
      color: #c62828;
    }
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.spinner-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 999;
}

.product-request-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;

  mat-form-field {
    flex: 1;
    min-width: 200px;
  }

  &.full-width {
    width: 100%;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 10px;

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 4px;

  table {
    width: 100%;
  }

  .table-form-field {
    width: 100%;
    margin: -16px 0;
  }

  .mat-column-actions {
    width: 60px;
    text-align: center;
  }

  .mat-column-productCode {
    min-width: 150px;
  }

  .mat-column-productName {
    min-width: 200px;
  }

  .mat-column-unitName {
    width: 100px;
  }

  .mat-column-quantity,
  .mat-column-price,
  .mat-column-total {
    width: 120px;
  }

  .mat-column-deliveryDate {
    width: 150px;
  }
}

.empty-table-message {
  padding: 20px;
  text-align: center;
  color: rgba(0, 0, 0, 0.54);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-label {
  font-weight: 500;
  min-width: 150px;
  color: rgba(0, 0, 0, 0.6);
}

.info-value {
  flex: 1;
}

.notes-text {
  white-space: pre-line;
  grid-column: span 2;
}

.order-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px;
  font-size: 18px;
  font-weight: 500;

  .total-amount {
    margin-left: 16px;
    font-size: 20px;
    font-weight: 700;
    color: #1976d2;
  }
}

::ng-deep {
  .mat-mdc-form-field-subscript-wrapper {
    height: 0;
  }
}
