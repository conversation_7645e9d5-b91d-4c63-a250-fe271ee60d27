using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

[Authorize]
public class PurchaseOrdersController : ApiControllerBase
{
    private readonly ITransactionService _transactionService;
    private readonly ILogger<PurchaseOrdersController> _logger;

    public PurchaseOrdersController(ITransactionService transactionService, ILogger<PurchaseOrdersController> logger)
    {
        _transactionService = transactionService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetAll()
    {
        try
        {
            // Get all transactions that are product orders (stage type = Order)
            var stageTypes = await _transactionService.GetAllTransactionStageTypesAsync();
            var orderStageType = stageTypes.FirstOrDefault(st => st.Name == "Order");

            if (orderStageType == null)
                return Ok(new List<TransactionHeaderDto>());

            var productOrders = await _transactionService.GetTransactionsByStageTypeIdAsync(orderStageType.StageTypeId);
            return Ok(productOrders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all product orders");
            return StatusCode(500, "An error occurred while retrieving product orders");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<TransactionHeaderDto>> GetById(int id)
    {
        try
        {
            var transaction = await _transactionService.GetTransactionByIdAsync(id);
            if (transaction == null)
                return NotFound();

            return Ok(transaction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the product order");
        }
    }

    [HttpGet("supplier/{supplierId}")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetBySupplier(int supplierId)
    {
        try
        {
            // Get all transactions that are product orders (stage type = Order) for the specified supplier
            var stageTypes = await _transactionService.GetAllTransactionStageTypesAsync();
            var orderStageType = stageTypes.FirstOrDefault(st => st.Name == "Order");

            if (orderStageType == null)
                return Ok(new List<TransactionHeaderDto>());

            var allSupplierTransactions = await _transactionService.GetTransactionsBySupplierAsync(supplierId);
            var productOrders = allSupplierTransactions.Where(t => t.StageTypeId == orderStageType.StageTypeId).ToList();

            return Ok(productOrders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product orders for supplier ID {SupplierId}", supplierId);
            return StatusCode(500, "An error occurred while retrieving product orders");
        }
    }

    [HttpGet("costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetByCostCenter(int costCenterId)
    {
        try
        {
            // Get all transactions that are product orders (stage type = Order) for the specified cost center
            var stageTypes = await _transactionService.GetAllTransactionStageTypesAsync();
            var orderStageType = stageTypes.FirstOrDefault(st => st.Name == "Order");

            if (orderStageType == null)
                return Ok(new List<TransactionHeaderDto>());

            var allCostCenterTransactions = await _transactionService.GetTransactionsByCostCenterAsync(costCenterId);
            var productOrders = allCostCenterTransactions.Where(t => t.StageTypeId == orderStageType.StageTypeId).ToList();

            return Ok(productOrders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product orders for cost center ID {CostCenterId}", costCenterId);
            return StatusCode(500, "An error occurred while retrieving product orders");
        }
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetByStatus(string status)
    {
        try
        {
            // Get all transactions that are product orders (stage type = Order) with the specified status
            var stageTypes = await _transactionService.GetAllTransactionStageTypesAsync();
            var orderStageType = stageTypes.FirstOrDefault(st => st.Name == "Order");

            if (orderStageType == null)
                return Ok(new List<TransactionHeaderDto>());

            var allStatusTransactions = await _transactionService.GetTransactionsByStatusAsync(status);
            var productOrders = allStatusTransactions.Where(t => t.StageTypeId == orderStageType.StageTypeId).ToList();

            return Ok(productOrders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product orders with status {Status}", status);
            return StatusCode(500, "An error occurred while retrieving product orders");
        }
    }

    [HttpPost]
    public async Task<ActionResult<TransactionHeaderDto>> Create(CreateProductOrderDto createProductOrderDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateProductOrderAsync(createProductOrderDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating product order");
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product order");
            return StatusCode(500, "An error occurred while creating the product order");
        }
    }

    [HttpPost("from-request/{productRequestId}")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateFromRequest(int productRequestId, CreateProductOrderDto createProductOrderDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateProductOrderFromRequestAsync(productRequestId, createProductOrderDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating product order from request {ProductRequestId}", productRequestId);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product order from request with ID {ProductRequestId}", productRequestId);
            return StatusCode(500, "An error occurred while creating the product order");
        }
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, UpdateProductOrderDto updateProductOrderDto)
    {
        try
        {
            await _transactionService.UpdateProductOrderAsync(id, updateProductOrderDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the product order");
        }
    }

    [HttpPut("{id}/submit")]
    public async Task<ActionResult> Submit(int id, [FromBody] object data)
    {
        try
        {
            string notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.SubmitProductOrderAsync(id, notes);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while submitting the product order");
        }
    }

    [HttpPut("{id}/approve")]
    public async Task<ActionResult> Approve(int id, [FromBody] object data)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();

            string notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.ApproveProductOrderAsync(id, userId, notes);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when approving product order {Id}", id);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while approving the product order");
        }
    }

    [HttpPut("{id}/reject")]
    public async Task<ActionResult> Reject(int id, [FromBody] RejectReasonDto rejectReasonDto)
    {
        try
        {
            await _transactionService.RejectProductOrderAsync(id, rejectReasonDto.Reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while rejecting the product order");
        }
    }

    [HttpPut("{id}/cancel")]
    public async Task<ActionResult> Cancel(int id, [FromBody] CancelReasonDto cancelReasonDto)
    {
        try
        {
            await _transactionService.CancelProductOrderAsync(id, cancelReasonDto.Reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while cancelling the product order");
        }
    }
}

public class RejectReasonDto
{
    public string Reason { get; set; } = string.Empty;
}

public class CancelReasonDto
{
    public string Reason { get; set; } = string.Empty;
}
