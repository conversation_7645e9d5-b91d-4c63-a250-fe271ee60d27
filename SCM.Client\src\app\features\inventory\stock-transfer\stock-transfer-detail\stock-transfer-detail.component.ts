import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { StockTransferService } from '../../../../core/services/stock-transfer.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductService } from '../../../../core/services/product.service';
import { StockService } from '../../../../core/services/stock.service';
import { UnitService } from '../../../../core/services/unit.service';
import { 
  StockTransferHeader, 
  StockTransferDetail, 
  CreateStockTransferHeader, 
  CreateStockTransferDetail 
} from '../../../../core/models/stock-transfer.model';
import { CostCenter } from '../../../../core/models/cost-center.model';
import { Product } from '../../../../core/models/product.model';
import { StockOnHand } from '../../../../core/models/stock.model';
import { Unit } from '../../../../core/models/unit.model';
import { Observable, forkJoin, of, map, startWith, switchMap, finalize } from 'rxjs';

@Component({
  selector: 'app-stock-transfer-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatAutocompleteModule,
    MatTooltipModule,
    MatDividerModule
  ],
  templateUrl: './stock-transfer-detail.component.html',
  styleUrls: ['./stock-transfer-detail.component.scss']
})
export class StockTransferDetailComponent implements OnInit {
  transferForm!: FormGroup;
  isLoading = false;
  isEditMode = false;
  stockTransferId: number | null = null;
  stockTransfer: StockTransferHeader | null = null;
  
  costCenters: CostCenter[] = [];
  products: Product[] = [];
  units: Unit[] = [];
  
  displayedColumns: string[] = [
    'productName',
    'quantity',
    'unitName',
    'notes',
    'actions'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private stockTransferService: StockTransferService,
    private costCenterService: CostCenterService,
    private productService: ProductService,
    private stockService: StockService,
    private unitService: UnitService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadData();
    
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.stockTransferId = +id;
        this.isEditMode = true;
        this.loadStockTransfer(this.stockTransferId);
      }
    });
  }

  get detailsFormArray(): FormArray {
    return this.transferForm.get('details') as FormArray;
  }

  initForm(): void {
    this.transferForm = this.fb.group({
      fromCostCenterId: ['', Validators.required],
      toCostCenterId: ['', Validators.required],
      transferDate: [new Date(), Validators.required],
      notes: [''],
      details: this.fb.array([])
    });
    
    // Add validation to prevent selecting the same cost center
    this.transferForm.get('fromCostCenterId')?.valueChanges.subscribe(value => {
      const toCostCenterId = this.transferForm.get('toCostCenterId')?.value;
      if (value && toCostCenterId && value === toCostCenterId) {
        this.transferForm.get('toCostCenterId')?.setErrors({ sameCostCenter: true });
      } else {
        const errors = this.transferForm.get('toCostCenterId')?.errors;
        if (errors) {
          delete errors['sameCostCenter'];
          this.transferForm.get('toCostCenterId')?.setErrors(Object.keys(errors).length ? errors : null);
        }
      }
    });
    
    this.transferForm.get('toCostCenterId')?.valueChanges.subscribe(value => {
      const fromCostCenterId = this.transferForm.get('fromCostCenterId')?.value;
      if (value && fromCostCenterId && value === fromCostCenterId) {
        this.transferForm.get('toCostCenterId')?.setErrors({ sameCostCenter: true });
      } else {
        const errors = this.transferForm.get('toCostCenterId')?.errors;
        if (errors) {
          delete errors['sameCostCenter'];
          this.transferForm.get('toCostCenterId')?.setErrors(Object.keys(errors).length ? errors : null);
        }
      }
    });
  }

  loadData(): void {
    this.isLoading = true;
    
    forkJoin({
      costCenters: this.costCenterService.getAllCostCenters(),
      products: this.productService.getAllProducts(),
      units: this.unitService.getAllUnits()
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe({
      next: (data) => {
        this.costCenters = data.costCenters;
        this.products = data.products;
        this.units = data.units;
        
        // Add an empty detail if creating a new transfer
        if (!this.isEditMode) {
          this.addDetail();
        }
      },
      error: (error) => {
        console.error('Error loading data', error);
        this.snackBar.open('Error loading data. Please try again.', 'Close', {
          duration: 5000
        });
      }
    });
  }

  loadStockTransfer(id: number): void {
    this.isLoading = true;
    
    this.stockTransferService.getStockTransferById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (stockTransfer) => {
          this.stockTransfer = stockTransfer;
          
          // Patch form values
          this.transferForm.patchValue({
            fromCostCenterId: stockTransfer.fromCostCenterId,
            toCostCenterId: stockTransfer.toCostCenterId,
            transferDate: new Date(stockTransfer.transferDate),
            notes: stockTransfer.notes
          });
          
          // Clear existing details
          while (this.detailsFormArray.length) {
            this.detailsFormArray.removeAt(0);
          }
          
          // Add details
          if (stockTransfer.details && stockTransfer.details.length > 0) {
            stockTransfer.details.forEach(detail => {
              this.addDetail(detail);
            });
          } else {
            this.addDetail();
          }
          
          // Disable form if not in Draft status
          if (stockTransfer.status !== 'Draft') {
            this.transferForm.disable();
          }
        },
        error: (error) => {
          console.error('Error loading stock transfer', error);
          this.snackBar.open('Error loading stock transfer. Please try again.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  addDetail(detail?: StockTransferDetail): void {
    const detailForm = this.fb.group({
      id: [detail?.id || 0],
      productId: [detail?.productId || '', Validators.required],
      batchId: [detail?.batchId || null],
      unitId: [detail?.unitId || null],
      quantity: [detail?.quantity || 0, [Validators.required, Validators.min(0.01)]],
      notes: [detail?.notes || '']
    });
    
    this.detailsFormArray.push(detailForm);
  }

  removeDetail(index: number): void {
    this.detailsFormArray.removeAt(index);
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.name : '';
  }

  getUnitName(unitId: number): string {
    const unit = this.units.find(u => u.id === unitId);
    return unit ? unit.name : '';
  }

  getCostCenterName(costCenterId: number): string {
    const costCenter = this.costCenters.find(cc => cc.id === costCenterId);
    return costCenter ? costCenter.name : '';
  }

  checkStockAvailability(productId: number, index: number): void {
    const fromCostCenterId = this.transferForm.get('fromCostCenterId')?.value;
    
    if (productId && fromCostCenterId) {
      this.isLoading = true;
      
      this.stockService.getStockByProductAndCostCenter(productId, fromCostCenterId)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (stock) => {
            const availableQuantity = stock ? stock.quantity : 0;
            const detailForm = this.detailsFormArray.at(index) as FormGroup;
            
            if (availableQuantity <= 0) {
              detailForm.get('quantity')?.setErrors({ noStock: true });
              this.snackBar.open(`No stock available for this product in the selected cost center.`, 'Close', {
                duration: 5000
              });
            } else {
              // Set the unit to match the product's default unit
              const product = this.products.find(p => p.id === productId);
              if (product && product.unitId) {
                detailForm.get('unitId')?.setValue(product.unitId);
              }
            }
          },
          error: (error) => {
            console.error('Error checking stock availability', error);
          }
        });
    }
  }

  validateQuantity(index: number): void {
    const detailForm = this.detailsFormArray.at(index) as FormGroup;
    const productId = detailForm.get('productId')?.value;
    const quantity = detailForm.get('quantity')?.value;
    const fromCostCenterId = this.transferForm.get('fromCostCenterId')?.value;
    
    if (productId && fromCostCenterId && quantity > 0) {
      this.stockService.getStockByProductAndCostCenter(productId, fromCostCenterId)
        .subscribe({
          next: (stock) => {
            const availableQuantity = stock ? stock.quantity : 0;
            
            if (quantity > availableQuantity) {
              detailForm.get('quantity')?.setErrors({ exceedsStock: true });
              this.snackBar.open(`Quantity exceeds available stock (${availableQuantity}).`, 'Close', {
                duration: 5000
              });
            }
          },
          error: (error) => {
            console.error('Error validating quantity', error);
          }
        });
    }
  }

  saveStockTransfer(): void {
    if (this.transferForm.invalid) {
      this.markFormGroupTouched(this.transferForm);
      this.snackBar.open('Please fix the errors in the form before saving.', 'Close', {
        duration: 5000
      });
      return;
    }
    
    this.isLoading = true;
    
    if (this.isEditMode && this.stockTransferId) {
      // Update existing stock transfer
      const updateData = {
        id: this.stockTransferId,
        ...this.transferForm.value,
        status: 'Draft'
      };
      
      this.stockTransferService.updateStockTransfer(this.stockTransferId, updateData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock transfer updated successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-transfers']);
          },
          error: (error) => {
            console.error('Error updating stock transfer', error);
            this.snackBar.open('Error updating stock transfer: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    } else {
      // Create new stock transfer
      const createData: CreateStockTransferHeader = {
        ...this.transferForm.value
      };
      
      this.stockTransferService.createStockTransfer(createData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (result) => {
            this.snackBar.open('Stock transfer created successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-transfers']);
          },
          error: (error) => {
            console.error('Error creating stock transfer', error);
            this.snackBar.open('Error creating stock transfer: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  completeStockTransfer(): void {
    if (!this.stockTransferId) return;
    
    if (confirm('Are you sure you want to complete this stock transfer? This will update inventory levels.')) {
      this.isLoading = true;
      
      this.stockTransferService.completeStockTransfer(this.stockTransferId, { id: this.stockTransferId })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock transfer completed successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-transfers']);
          },
          error: (error) => {
            console.error('Error completing stock transfer', error);
            this.snackBar.open('Error completing stock transfer: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancelStockTransfer(): void {
    if (!this.stockTransferId) return;
    
    if (confirm('Are you sure you want to cancel this stock transfer?')) {
      this.isLoading = true;
      
      this.stockTransferService.cancelStockTransfer(this.stockTransferId)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock transfer cancelled successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-transfers']);
          },
          error: (error) => {
            console.error('Error cancelling stock transfer', error);
            this.snackBar.open('Error cancelling stock transfer: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancel(): void {
    this.router.navigate(['/inventory/stock-transfers']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        for (let i = 0; i < control.length; i++) {
          const arrayControl = control.at(i);
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        }
      }
    });
  }
}
