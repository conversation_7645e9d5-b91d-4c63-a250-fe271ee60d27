import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import {
  PurchaseOrder,
  PurchaseOrderListItem,
  CreatePurchaseOrder,
  UpdatePurchaseOrder,
  UpdatePurchaseOrderDetail
} from '../models/purchase-order.model';
import { TransactionHeader, CreateProductOrder } from '../models/transaction.model';

@Injectable({
  providedIn: 'root'
})
export class PurchaseOrderService {
  // Use the transactions endpoint with product-order path for all operations
  private readonly path = 'transactions/product-order';
  private readonly transactionPath = 'transactions';
  private readonly purchaseOrdersPath = 'purchaseorders';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<TransactionHeader[]> {
    // Use the purchaseorders endpoint for backward compatibility
    return this.apiService.get<TransactionHeader[]>(this.purchaseOrdersPath);
  }

  getById(id: number): Observable<TransactionHeader> {
    return this.apiService.get<TransactionHeader>(`${this.transactionPath}/${id}`);
  }

  getByCostCenter(costCenterId: number): Observable<TransactionHeader[]> {
    // Use the purchaseorders endpoint for backward compatibility
    return this.apiService.get<TransactionHeader[]>(`${this.purchaseOrdersPath}/costcenter/${costCenterId}`);
  }

  getBySupplier(supplierId: number): Observable<TransactionHeader[]> {
    // Use the purchaseorders endpoint for backward compatibility
    return this.apiService.get<TransactionHeader[]>(`${this.purchaseOrdersPath}/supplier/${supplierId}`);
  }

  getByStatus(status: string): Observable<TransactionHeader[]> {
    // Use the purchaseorders endpoint for backward compatibility
    return this.apiService.get<TransactionHeader[]>(`${this.purchaseOrdersPath}/status/${status}`);
  }

  create(purchaseOrder: CreatePurchaseOrder): Observable<TransactionHeader> {
    // Convert to CreateProductOrder if needed
    const productOrder: CreateProductOrder = {
      supplierId: purchaseOrder.supplierId,
      sourceCostCenterId: purchaseOrder.costCenterId,
      transactionDate: purchaseOrder.orderDate,
      requiredDate: purchaseOrder.expectedDeliveryDate,
      notes: purchaseOrder.notes,
      details: purchaseOrder.details.map(d => ({
        productId: d.productId,
        quantity: d.quantity,
        unitPrice: d.unitPrice,
        notes: d.notes
      }))
    };

    return this.apiService.post<TransactionHeader, CreateProductOrder>(this.transactionPath + '/product-order', productOrder);
  }

  createFromRequest(productRequestId: number, purchaseOrder: CreatePurchaseOrder): Observable<TransactionHeader> {
    // Convert to CreateProductOrder if needed
    const productOrder: CreateProductOrder = {
      supplierId: purchaseOrder.supplierId,
      sourceCostCenterId: purchaseOrder.costCenterId,
      transactionDate: purchaseOrder.orderDate,
      requiredDate: purchaseOrder.expectedDeliveryDate,
      notes: purchaseOrder.notes,
      details: purchaseOrder.details?.map(d => ({
        productId: d.productId,
        quantity: d.quantity,
        unitPrice: d.unitPrice,
        notes: d.notes
      }))
    };

    return this.apiService.post<TransactionHeader, CreateProductOrder>(
      `${this.transactionPath}/product-order/from-request/${productRequestId}`,
      productOrder
    );
  }

  update(id: number, purchaseOrder: UpdatePurchaseOrder): Observable<void> {
    // Convert to the format expected by the transaction service
    const updateData = {
      id: id,
      sourceCostCenterId: purchaseOrder.costCenterId,
      supplierId: purchaseOrder.supplierId,
      transactionDate: purchaseOrder.orderDate,
      requiredDate: purchaseOrder.expectedDeliveryDate,
      notes: purchaseOrder.notes
    };

    return this.apiService.put<void, any>(`${this.transactionPath}/product-order/${id}`, updateData);
  }

  submit(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.transactionPath}/product-order/${id}/submit`, { notes });
  }

  approve(id: number, notes?: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.transactionPath}/product-order/${id}/approve`, { notes });
  }

  reject(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.transactionPath}/product-order/${id}/reject`, { reason });
  }

  cancel(id: number, reason: string): Observable<void> {
    return this.apiService.put<void, any>(`${this.transactionPath}/product-order/${id}/cancel`, { reason });
  }

  // Transaction detail methods - use the transaction service for these
  addDetail(transactionId: number, detail: any): Observable<any> {
    return this.apiService.post<any, any>(`${this.transactionPath}/${transactionId}/details`, detail);
  }

  updateDetail(id: number, detail: any): Observable<void> {
    return this.apiService.put<void, any>(`${this.transactionPath}/details/${id}`, detail);
  }

  deleteDetail(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.transactionPath}/details/${id}`);
  }

  // Method to replace the deleted delete method
  // Instead of deleting, we'll cancel the purchase order
  delete(id: number): Observable<void> {
    return this.cancel(id, 'Deleted by user');
  }
}
