using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class StockAdjustmentDetail : BaseEntity
{
    public int StockAdjustmentHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal CurrentQuantity { get; set; }
    public decimal AdjustmentQuantity { get; set; }
    public decimal NewQuantity { get; set; }
    public decimal? CostPrice { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual StockAdjustmentHeader StockAdjustmentHeader { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    public virtual Batch? Batch { get; set; }
    public virtual Unit? Unit { get; set; }
}
