using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class SupplierService : ISupplierService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public SupplierService(ApplicationDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<SupplierListDto>> GetAllSuppliersAsync()
    {
        var suppliers = await _dbContext.Suppliers
            .OrderBy(s => s.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<SupplierListDto>>(suppliers);
    }

    public async Task<SupplierDto?> GetSupplierByIdAsync(int id)
    {
        var supplier = await _dbContext.Suppliers
            .FirstOrDefaultAsync(s => s.Id == id);

        return supplier != null ? _mapper.Map<SupplierDto>(supplier) : null;
    }

    public async Task<SupplierDto?> GetSupplierByNameAsync(string name)
    {
        var supplier = await _dbContext.Suppliers
            .FirstOrDefaultAsync(s => s.Name.ToLower() == name.ToLower());

        return supplier != null ? _mapper.Map<SupplierDto>(supplier) : null;
    }

    public async Task<IEnumerable<SupplierListDto>> SearchSuppliersAsync(string searchTerm)
    {
        var suppliers = await _dbContext.Suppliers
            .Where(s => s.Name.Contains(searchTerm) || 
                        (s.ContactPerson != null && s.ContactPerson.Contains(searchTerm)) ||
                        (s.Email != null && s.Email.Contains(searchTerm)) ||
                        (s.Phone != null && s.Phone.Contains(searchTerm)))
            .OrderBy(s => s.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<SupplierListDto>>(suppliers);
    }

    public async Task<SupplierDto> CreateSupplierAsync(CreateSupplierDto createSupplierDto)
    {
        // Check if supplier with the same name already exists
        if (await SupplierExistsAsync(createSupplierDto.Name))
            throw new InvalidOperationException($"Supplier with name '{createSupplierDto.Name}' already exists.");

        var supplier = _mapper.Map<Supplier>(createSupplierDto);
        supplier.IsActive = true;
        supplier.CreatedAt = DateTime.UtcNow;

        _dbContext.Suppliers.Add(supplier);
        await _dbContext.SaveChangesAsync();

        return _mapper.Map<SupplierDto>(supplier);
    }

    public async Task UpdateSupplierAsync(UpdateSupplierDto updateSupplierDto)
    {
        var supplier = await _dbContext.Suppliers
            .FirstOrDefaultAsync(s => s.Id == updateSupplierDto.Id);

        if (supplier == null)
            throw new KeyNotFoundException($"Supplier with ID {updateSupplierDto.Id} not found.");

        // Check if supplier with the same name already exists (excluding current supplier)
        if (await SupplierExistsAsync(updateSupplierDto.Name, updateSupplierDto.Id))
            throw new InvalidOperationException($"Supplier with name '{updateSupplierDto.Name}' already exists.");

        _mapper.Map(updateSupplierDto, supplier);
        supplier.UpdatedAt = DateTime.UtcNow;

        _dbContext.Suppliers.Update(supplier);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteSupplierAsync(int id)
    {
        var supplier = await _dbContext.Suppliers
            .FirstOrDefaultAsync(s => s.Id == id);

        if (supplier == null)
            throw new KeyNotFoundException($"Supplier with ID {id} not found.");

        // Check if supplier is used in any purchase orders
        var hasPurchaseOrders = await _dbContext.PurchaseOrders
            .AnyAsync(po => po.SupplierId == id);

        if (hasPurchaseOrders)
            throw new InvalidOperationException("Cannot delete supplier that is used in purchase orders.");

        _dbContext.Suppliers.Remove(supplier);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<bool> SupplierExistsAsync(string name, int? excludeId = null)
    {
        var query = _dbContext.Suppliers.AsQueryable();

        if (excludeId.HasValue)
            query = query.Where(s => s.Id != excludeId.Value);

        return await query.AnyAsync(s => s.Name.ToLower() == name.ToLower());
    }
}
