using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class CostCenterTypesController : ApiControllerBase
{
    private readonly ICostCenterTypeService _costCenterTypeService;

    public CostCenterTypesController(ICostCenterTypeService costCenterTypeService)
    {
        _costCenterTypeService = costCenterTypeService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<CostCenterTypeDto>>> GetAll()
    {
        try
        {
            var costCenterTypes = await _costCenterTypeService.GetAllCostCenterTypesAsync();
            return Ok(costCenterTypes);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving cost center types. Please try again later.");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<CostCenterTypeDto>> GetById(int id)
    {
        try
        {
            var costCenterType = await _costCenterTypeService.GetCostCenterTypeByIdAsync(id);
            if (costCenterType == null)
                return NotFound();

            return Ok(costCenterType);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving the cost center type. Please try again later.");
        }
    }

    [HttpGet("{id}/cost-centers")]
    public async Task<ActionResult<CostCenterTypeDto>> GetWithCostCenters(int id)
    {
        try
        {
            var costCenterType = await _costCenterTypeService.GetCostCenterTypeWithCostCentersAsync(id);
            if (costCenterType == null)
                return NotFound();

            return Ok(costCenterType);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving the cost center type with cost centers. Please try again later.");
        }
    }

    [HttpPost]
    public async Task<ActionResult<CostCenterTypeDto>> Create(CreateCostCenterTypeDto createCostCenterTypeDto)
    {
        try
        {
            var costCenterType = await _costCenterTypeService.CreateCostCenterTypeAsync(createCostCenterTypeDto);
            return CreatedAtAction(nameof(GetById), new { id = costCenterType.Id }, costCenterType);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while creating the cost center type. Please try again later.");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateCostCenterTypeDto updateCostCenterTypeDto)
    {
        if (id != updateCostCenterTypeDto.Id)
            return BadRequest();

        try
        {
            await _costCenterTypeService.UpdateCostCenterTypeAsync(updateCostCenterTypeDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while updating the cost center type. Please try again later.");
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _costCenterTypeService.DeleteCostCenterTypeAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while deleting the cost center type. Please try again later.");
        }

        return NoContent();
    }
}
