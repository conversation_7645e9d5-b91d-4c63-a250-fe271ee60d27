using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class StockAdjustmentsController : ApiControllerBase
{
    private readonly IStockAdjustmentService _stockAdjustmentService;

    public StockAdjustmentsController(IStockAdjustmentService stockAdjustmentService)
    {
        _stockAdjustmentService = stockAdjustmentService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<StockAdjustmentHeaderDto>>> GetAll()
    {
        var stockAdjustments = await _stockAdjustmentService.GetAllStockAdjustmentsAsync();
        return Ok(stockAdjustments);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<StockAdjustmentHeaderDto>> GetById(int id)
    {
        var stockAdjustment = await _stockAdjustmentService.GetStockAdjustmentByIdAsync(id);
        if (stockAdjustment == null)
            return NotFound();

        return Ok(stockAdjustment);
    }

    [HttpGet("cost-center/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockAdjustmentHeaderDto>>> GetByCostCenterId(int costCenterId)
    {
        var stockAdjustments = await _stockAdjustmentService.GetStockAdjustmentsByCostCenterIdAsync(costCenterId);
        return Ok(stockAdjustments);
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<StockAdjustmentHeaderDto>>> GetByStatus(string status)
    {
        var stockAdjustments = await _stockAdjustmentService.GetStockAdjustmentsByStatusAsync(status);
        return Ok(stockAdjustments);
    }

    [HttpPost]
    public async Task<ActionResult<StockAdjustmentHeaderDto>> Create(CreateStockAdjustmentHeaderDto createStockAdjustmentHeaderDto)
    {
        var stockAdjustment = await _stockAdjustmentService.CreateStockAdjustmentAsync(createStockAdjustmentHeaderDto);
        return CreatedAtAction(nameof(GetById), new { id = stockAdjustment.Id }, stockAdjustment);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateStockAdjustmentHeaderDto updateStockAdjustmentHeaderDto)
    {
        if (id != updateStockAdjustmentHeaderDto.Id)
            return BadRequest();

        try
        {
            await _stockAdjustmentService.UpdateStockAdjustmentAsync(updateStockAdjustmentHeaderDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _stockAdjustmentService.DeleteStockAdjustmentAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/complete")]
    public async Task<IActionResult> Complete(int id, CompleteStockAdjustmentDto completeStockAdjustmentDto)
    {
        if (id != completeStockAdjustmentDto.Id)
            return BadRequest();

        try
        {
            await _stockAdjustmentService.CompleteStockAdjustmentAsync(completeStockAdjustmentDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/cancel")]
    public async Task<IActionResult> Cancel(int id)
    {
        try
        {
            await _stockAdjustmentService.CancelStockAdjustmentAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpGet("{stockAdjustmentHeaderId}/details")]
    public async Task<ActionResult<IEnumerable<StockAdjustmentDetailDto>>> GetDetails(int stockAdjustmentHeaderId)
    {
        var details = await _stockAdjustmentService.GetStockAdjustmentDetailsAsync(stockAdjustmentHeaderId);
        return Ok(details);
    }

    [HttpGet("details/{id}")]
    public async Task<ActionResult<StockAdjustmentDetailDto>> GetDetailById(int id)
    {
        var detail = await _stockAdjustmentService.GetStockAdjustmentDetailByIdAsync(id);
        if (detail == null)
            return NotFound();

        return Ok(detail);
    }

    [HttpPost("{stockAdjustmentHeaderId}/details")]
    public async Task<ActionResult<StockAdjustmentDetailDto>> CreateDetail(int stockAdjustmentHeaderId, CreateStockAdjustmentDetailDto createStockAdjustmentDetailDto)
    {
        try
        {
            var detail = await _stockAdjustmentService.CreateStockAdjustmentDetailAsync(stockAdjustmentHeaderId, createStockAdjustmentDetailDto);
            return CreatedAtAction(nameof(GetDetailById), new { id = detail.Id }, detail);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("details/{id}")]
    public async Task<IActionResult> UpdateDetail(int id, UpdateStockAdjustmentDetailDto updateStockAdjustmentDetailDto)
    {
        if (id != updateStockAdjustmentDetailDto.Id)
            return BadRequest();

        try
        {
            await _stockAdjustmentService.UpdateStockAdjustmentDetailAsync(updateStockAdjustmentDetailDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("details/{id}")]
    public async Task<IActionResult> DeleteDetail(int id)
    {
        try
        {
            await _stockAdjustmentService.DeleteStockAdjustmentDetailAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
