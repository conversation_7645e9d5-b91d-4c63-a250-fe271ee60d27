.page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h1 {
    margin: 0;
    font-size: 24px;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.filters-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  
  mat-form-field {
    flex: 1;
    min-width: 200px;
  }
}

.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.table-container {
  overflow-x: auto;
  
  .receiving-table {
    width: 100%;
    
    .clickable-row {
      cursor: pointer;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
    
    .status-chip {
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      display: inline-block;
      text-align: center;
      min-width: 80px;
    }
    
    .status-draft {
      background-color: #e0e0e0;
      color: #616161;
    }
    
    .status-submitted {
      background-color: #bbdefb;
      color: #1976d2;
    }
    
    .status-approved {
      background-color: #c8e6c9;
      color: #388e3c;
    }
    
    .status-completed {
      background-color: #c8e6c9;
      color: #388e3c;
    }
    
    .status-cancelled {
      background-color: #ffcdd2;
      color: #d32f2f;
    }
    
    .status-rejected {
      background-color: #ffcdd2;
      color: #d32f2f;
    }
  }
}

@media (max-width: 768px) {
  .filters-container {
    flex-direction: column;
    gap: 0;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    
    .header-actions {
      width: 100%;
    }
  }
}
