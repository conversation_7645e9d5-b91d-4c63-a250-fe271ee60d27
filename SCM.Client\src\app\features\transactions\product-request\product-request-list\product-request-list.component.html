<div class="page-container">
  <div class="page-header">
    <h1>Product Requests</h1>
    <div class="header-actions">
      <button mat-icon-button color="primary" (click)="showWorkflowInfo()" matTooltip="Show workflow information">
        <mat-icon>help_outline</mat-icon>
      </button>
      <button mat-raised-button color="primary" (click)="createProductRequest()">
        <mat-icon>add</mat-icon> New Product Request
      </button>
    </div>
  </div>

  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput (keyup)="onSearch($event)" [value]="searchTerm" placeholder="Search by reference number, cost center...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(value)]="selectedStatus" (selectionChange)="onStatusChange()">
            <mat-option *ngFor="let status of statusOptions" [value]="status">
              {{status}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2">
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading product requests...</p>
    </div>

    <div *ngIf="!isLoading && filteredRequests.length === 0" class="empty-state">
      <mat-icon>description</mat-icon>
      <p>No product requests found</p>
      <button mat-raised-button color="primary" (click)="createProductRequest()">Create Product Request</button>
    </div>

    <table mat-table [dataSource]="filteredRequests" matSort *ngIf="!isLoading && filteredRequests.length > 0">
      <!-- Reference Number Column -->
      <ng-container matColumnDef="referenceNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Reference Number</th>
        <td mat-cell *matCellDef="let request">{{request.referenceNumber}}</td>
      </ng-container>

      <!-- Cost Center Column -->
      <ng-container matColumnDef="costCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Cost Center</th>
        <td mat-cell *matCellDef="let request">{{request.costCenterName}}</td>
      </ng-container>

      <!-- Request Date Column -->
      <ng-container matColumnDef="requestDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Request Date</th>
        <td mat-cell *matCellDef="let request">{{request.requestDate | date:'mediumDate'}}</td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let request">
          <span class="status-chip" [ngClass]="getStatusClass(request.status)">
            {{request.status}}
          </span>
        </td>
      </ng-container>

      <!-- Created By Column -->
      <ng-container matColumnDef="createdByName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Created By</th>
        <td mat-cell *matCellDef="let request">{{request.createdByName || 'N/A'}}</td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let request">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions" (click)="$event.stopPropagation()">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="viewProductRequest(request.id); $event.stopPropagation()">
              <mat-icon>visibility</mat-icon>
              <span>View</span>
            </button>
            <button mat-menu-item (click)="editProductRequest(request.id); $event.stopPropagation()" *ngIf="request.status === 'Draft'">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item (click)="deleteProductRequest(request.id); $event.stopPropagation()" *ngIf="request.status === 'Draft'">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
            <button mat-menu-item (click)="approveProductRequest(request.id); $event.stopPropagation()" *ngIf="request.status === 'Submitted'">
              <mat-icon>check_circle</mat-icon>
              <span>Approve</span>
            </button>
            <button mat-menu-item (click)="rejectProductRequest(request.id); $event.stopPropagation()" *ngIf="request.status === 'Submitted'">
              <mat-icon>cancel</mat-icon>
              <span>Reject</span>
            </button>
            <button mat-menu-item (click)="createPurchaseOrder(request.id); $event.stopPropagation()" *ngIf="request.status === 'Approved'">
              <mat-icon>shopping_cart</mat-icon>
              <span>Create Purchase Order</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"
          (click)="viewProductRequest(row.id)"
          class="clickable-row"></tr>
    </table>

    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons
                   *ngIf="!isLoading && filteredRequests.length > 0"></mat-paginator>
  </div>
</div>
