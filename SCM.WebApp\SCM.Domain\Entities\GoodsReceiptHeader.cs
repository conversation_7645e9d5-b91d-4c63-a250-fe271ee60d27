using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class GoodsReceiptHeader : BaseEntity
{
    public string DocumentNumber { get; set; } = string.Empty;
    public int? PurchaseOrderId { get; set; }
    public int SupplierId { get; set; }
    public int CostCenterId { get; set; }
    public DateTime ReceiptDate { get; set; } = DateTime.UtcNow;
    public string? DeliveryNoteNumber { get; set; }
    public string? InvoiceNumber { get; set; }
    public DateTime? InvoiceDate { get; set; }
    public string Status { get; set; } = "Draft"; // Draft, Completed, Cancelled
    public string? Notes { get; set; }
    public decimal TotalAmount { get; set; }
    public int? ReceivedById { get; set; }
    public int? ApprovedById { get; set; }
    public DateTime? ApprovedAt { get; set; }
    
    // Navigation properties
    public virtual PurchaseOrder? PurchaseOrder { get; set; }
    public virtual Supplier Supplier { get; set; } = null!;
    public virtual CostCenter CostCenter { get; set; } = null!;
    public virtual User? ReceivedBy { get; set; }
    public virtual User? ApprovedBy { get; set; }
    public virtual ICollection<GoodsReceiptDetail> Details { get; set; } = new List<GoodsReceiptDetail>();
}
