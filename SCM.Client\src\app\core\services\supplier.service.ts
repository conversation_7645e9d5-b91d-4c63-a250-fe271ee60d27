import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface Supplier {
  id: number;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  taxNumber?: string;
  notes?: string;
  bankAccount?: string;
  bankName?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface SupplierListItem {
  id: number;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  taxNumber?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateSupplier {
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  taxNumber?: string;
  notes?: string;
  bankAccount?: string;
  bankName?: string;
}

export interface UpdateSupplier {
  id: number;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  taxNumber?: string;
  notes?: string;
  bankAccount?: string;
  bankName?: string;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SupplierService {
  private readonly apiUrl = `${environment.apiUrl}/suppliers`;

  constructor(private http: HttpClient) { }

  getAllSuppliers(): Observable<SupplierListItem[]> {
    return this.http.get<SupplierListItem[]>(this.apiUrl);
  }

  getAll(): Observable<SupplierListItem[]> {
    return this.getAllSuppliers();
  }

  getSupplierById(id: number): Observable<Supplier> {
    return this.http.get<Supplier>(`${this.apiUrl}/${id}`);
  }

  getSupplierByName(name: string): Observable<Supplier> {
    return this.http.get<Supplier>(`${this.apiUrl}/name/${name}`);
  }

  searchSuppliers(term: string): Observable<SupplierListItem[]> {
    return this.http.get<SupplierListItem[]>(`${this.apiUrl}/search?term=${term}`);
  }

  createSupplier(supplier: CreateSupplier): Observable<Supplier> {
    return this.http.post<Supplier>(this.apiUrl, supplier);
  }

  updateSupplier(id: number, supplier: UpdateSupplier): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, supplier);
  }

  deleteSupplier(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
