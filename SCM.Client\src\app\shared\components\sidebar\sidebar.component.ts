import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { BusinessStructureTreeComponent, BusinessNode } from '../business-structure-tree/business-structure-tree.component';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatExpansionModule,
    MatIconModule,
    MatListModule,
    MatDividerModule,
    BusinessStructureTreeComponent
  ],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  showBusinessTree = true;
  selectedNode: BusinessNode | null = null;

  constructor(private router: Router) {}

  ngOnInit(): void {
  }

  onNodeSelected(node: BusinessNode): void {
    this.selectedNode = node;
    
    // Navigate based on node type
    switch (node.type) {
      case 'company':
        this.router.navigate(['/companies', node.id]);
        break;
      case 'location':
        this.router.navigate(['/locations', node.id]);
        break;
      case 'store':
        this.router.navigate(['/stores', node.id]);
        break;
      case 'costCenter':
        this.router.navigate(['/cost-centers', node.id]);
        break;
    }
  }

  toggleBusinessTree(): void {
    this.showBusinessTree = !this.showBusinessTree;
  }
}
