using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionType : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool AffectsInventory { get; set; } = true;

    // Navigation properties
    public virtual ICollection<TransactionProcess> Processes { get; set; } = new List<TransactionProcess>();
    public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; } = new List<TransactionHeader>();
}
