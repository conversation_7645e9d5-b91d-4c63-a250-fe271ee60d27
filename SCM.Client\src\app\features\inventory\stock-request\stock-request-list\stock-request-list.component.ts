import { Compo<PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StockRequestService } from '../../../../core/services/stock-request.service';
import { StockRequestHeader } from '../../../../core/models/stock-request.model';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-stock-request-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './stock-request-list.component.html',
  styleUrls: ['./stock-request-list.component.scss']
})
export class StockRequestListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  
  displayedColumns: string[] = [
    'referenceNumber', 
    'fromCostCenterName', 
    'toCostCenterName', 
    'requestDate', 
    'status', 
    'actions'
  ];
  
  stockRequests: StockRequestHeader[] = [];
  filteredRequests: StockRequestHeader[] = [];
  isLoading = false;
  searchTerm = '';
  selectedStatus = '';
  
  statusOptions = ['All', 'Draft', 'Submitted', 'Approved', 'Rejected', 'Completed', 'Cancelled'];

  constructor(
    private stockRequestService: StockRequestService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.loadStockRequests();
  }

  loadStockRequests(): void {
    this.isLoading = true;
    this.stockRequestService.getAllStockRequests()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.stockRequests = data;
          this.filteredRequests = [...this.stockRequests];
        },
        error: (error) => {
          console.error('Error loading stock requests', error);
          this.snackBar.open('Error loading stock requests', 'Close', {
            duration: 3000
          });
        }
      });
  }

  applyFilter(): void {
    this.filteredRequests = this.stockRequests.filter(request => {
      const matchesSearch = this.searchTerm === '' || 
        request.referenceNumber.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        request.fromCostCenterName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        request.toCostCenterName.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesStatus = this.selectedStatus === 'All' || this.selectedStatus === '' || 
        request.status === this.selectedStatus;
      
      return matchesSearch && matchesStatus;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.filteredRequests = [...this.stockRequests];
  }

  addStockRequest(): void {
    this.router.navigate(['/inventory/stock-requests/new']);
  }

  editStockRequest(request: StockRequestHeader): void {
    this.router.navigate(['/inventory/stock-requests', request.id]);
  }

  viewStockRequest(request: StockRequestHeader): void {
    this.router.navigate(['/inventory/stock-requests', request.id]);
  }

  deleteStockRequest(request: StockRequestHeader): void {
    if (confirm(`Are you sure you want to delete stock request ${request.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockRequestService.deleteStockRequest(request.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadStockRequests();
          },
          error: (error) => {
            console.error('Error deleting stock request', error);
            this.snackBar.open('Error deleting stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  submitStockRequest(request: StockRequestHeader): void {
    if (confirm(`Are you sure you want to submit stock request ${request.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockRequestService.submitStockRequest(request.id, { id: request.id })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request submitted successfully', 'Close', {
              duration: 3000
            });
            this.loadStockRequests();
          },
          error: (error) => {
            console.error('Error submitting stock request', error);
            this.snackBar.open('Error submitting stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  approveStockRequest(request: StockRequestHeader): void {
    if (confirm(`Are you sure you want to approve stock request ${request.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockRequestService.approveStockRequest(request.id, { id: request.id })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request approved successfully', 'Close', {
              duration: 3000
            });
            this.loadStockRequests();
          },
          error: (error) => {
            console.error('Error approving stock request', error);
            this.snackBar.open('Error approving stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  rejectStockRequest(request: StockRequestHeader): void {
    if (confirm(`Are you sure you want to reject stock request ${request.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockRequestService.rejectStockRequest(request.id, { id: request.id })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request rejected successfully', 'Close', {
              duration: 3000
            });
            this.loadStockRequests();
          },
          error: (error) => {
            console.error('Error rejecting stock request', error);
            this.snackBar.open('Error rejecting stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  completeStockRequest(request: StockRequestHeader): void {
    if (confirm(`Are you sure you want to complete stock request ${request.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockRequestService.completeStockRequest(request.id, { id: request.id })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request completed successfully', 'Close', {
              duration: 3000
            });
            this.loadStockRequests();
          },
          error: (error) => {
            console.error('Error completing stock request', error);
            this.snackBar.open('Error completing stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancelStockRequest(request: StockRequestHeader): void {
    if (confirm(`Are you sure you want to cancel stock request ${request.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockRequestService.cancelStockRequest(request.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request cancelled successfully', 'Close', {
              duration: 3000
            });
            this.loadStockRequests();
          },
          error: (error) => {
            console.error('Error cancelling stock request', error);
            this.snackBar.open('Error cancelling stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Draft': return 'status-draft';
      case 'Submitted': return 'status-submitted';
      case 'Approved': return 'status-approved';
      case 'Rejected': return 'status-rejected';
      case 'Completed': return 'status-completed';
      case 'Cancelled': return 'status-cancelled';
      default: return '';
    }
  }
}
