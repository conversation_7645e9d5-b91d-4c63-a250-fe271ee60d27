<div class="page-container">
  <div class="page-header">
    <h1>Goods Receiving</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="createReceiving()">
        <mat-icon>add</mat-icon> New Receiving
      </button>
      <button mat-raised-button color="accent" (click)="createFromPurchaseOrder()">
        <mat-icon>shopping_cart</mat-icon> Create from Purchase Order
      </button>
    </div>
  </div>

  <div class="filters-container">
    <mat-form-field appearance="outline">
      <mat-label>Search</mat-label>
      <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilters()" placeholder="Search by number, supplier, or cost center">
      <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; applyFilters()">
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Status</mat-label>
      <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilters()">
        <mat-option *ngFor="let status of statusOptions" [value]="status">
          {{ status }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <button mat-button (click)="clearFilters()">
      <mat-icon>filter_alt_off</mat-icon> Clear Filters
    </button>
  </div>

  <div class="spinner-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <div class="table-container mat-elevation-z2">
    <table mat-table [dataSource]="filteredReceivings" matSort class="receiving-table">
      <!-- Transaction Number Column -->
      <ng-container matColumnDef="transactionNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Receiving #</th>
        <td mat-cell *matCellDef="let receiving">{{ receiving.transactionNumber }}</td>
      </ng-container>

      <!-- Reference Number Column -->
      <ng-container matColumnDef="referenceNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Reference #</th>
        <td mat-cell *matCellDef="let receiving">{{ receiving.referenceNumber }}</td>
      </ng-container>

      <!-- Supplier Column -->
      <ng-container matColumnDef="supplierName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Supplier</th>
        <td mat-cell *matCellDef="let receiving">{{ receiving.supplierName }}</td>
      </ng-container>

      <!-- Cost Center Column -->
      <ng-container matColumnDef="costCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Cost Center</th>
        <td mat-cell *matCellDef="let receiving">{{ receiving.sourceCostCenterName }}</td>
      </ng-container>

      <!-- Transaction Date Column -->
      <ng-container matColumnDef="transactionDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
        <td mat-cell *matCellDef="let receiving">{{ receiving.transactionDate | date:'mediumDate' }}</td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let receiving">
          <span class="status-chip" [ngClass]="getStatusClass(receiving.status)">
            {{ receiving.status }}
          </span>
        </td>
      </ng-container>

      <!-- Total Amount Column -->
      <ng-container matColumnDef="totalAmount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Total</th>
        <td mat-cell *matCellDef="let receiving">{{ receiving.totalAmount | currency }}</td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let receiving" (click)="$event.stopPropagation()">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="viewReceiving(receiving)">
              <mat-icon>visibility</mat-icon>
              <span>View</span>
            </button>
            <button mat-menu-item *ngIf="receiving.status === 'Draft'" (click)="editReceiving(receiving)">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item *ngIf="receiving.status === 'Draft'" (click)="submitReceiving(receiving.id)">
              <mat-icon>send</mat-icon>
              <span>Submit</span>
            </button>
            <button mat-menu-item *ngIf="receiving.status === 'Draft'" (click)="completeReceiving(receiving.id)">
              <mat-icon>check_circle</mat-icon>
              <span>Complete</span>
            </button>
            <button mat-menu-item *ngIf="receiving.status === 'Draft'" (click)="cancelReceiving(receiving.id)">
              <mat-icon>cancel</mat-icon>
              <span>Cancel</span>
            </button>
            <button mat-menu-item *ngIf="receiving.status === 'Approved'" (click)="completeReceiving(receiving.id)">
              <mat-icon>check_circle</mat-icon>
              <span>Complete</span>
            </button>
            <button mat-menu-item *ngIf="receiving.status === 'Submitted'" (click)="approveReceiving(receiving)">
              <mat-icon>check_circle</mat-icon>
              <span>Approve</span>
            </button>
            <button mat-menu-item *ngIf="receiving.status === 'Submitted'" (click)="rejectReceiving(receiving)">
              <mat-icon>cancel</mat-icon>
              <span>Reject</span>
            </button>
            <mat-divider *ngIf="receiving.status === 'Completed'"></mat-divider>
            <button mat-menu-item *ngIf="receiving.status === 'Completed'" (click)="createCreditNote(receiving)">
              <mat-icon>assignment_return</mat-icon>
              <span>Create Credit Note</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"
          (click)="viewReceiving(row)"
          class="clickable-row"></tr>
    </table>

    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
