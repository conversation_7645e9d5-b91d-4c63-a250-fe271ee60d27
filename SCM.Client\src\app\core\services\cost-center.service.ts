import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import {
  CostCenter,
  CostCenterList,
  CreateCostCenter,
  UpdateCostCenter,
  CostCenterType,
  CreateCostCenterType,
  UpdateCostCenterType
} from '../models/cost-center.model';

@Injectable({
  providedIn: 'root'
})
export class CostCenterService {
  private readonly path = 'costcenters';
  private readonly typePath = 'costcentertypes';

  constructor(private apiService: ApiService) { }

  // Cost Centers
  getAll(): Observable<CostCenterList[]> {
    return this.apiService.get<CostCenterList[]>(this.path);
  }

  getAllCostCenters(): Observable<CostCenter[]> {
    return this.apiService.get<CostCenter[]>(this.path);
  }

  getById(id: number): Observable<CostCenter> {
    return this.apiService.get<CostCenter>(`${this.path}/${id}`);
  }

  getCostCenterById(id: number): Observable<CostCenter> {
    return this.getById(id);
  }

  getByStore(storeId: number): Observable<CostCenterList[]> {
    return this.apiService.get<CostCenterList[]>(`${this.path}/store/${storeId}`);
  }

  getCostCentersByStoreId(storeId: number): Observable<CostCenter[]> {
    return this.apiService.get<CostCenter[]>(`${this.path}/store/${storeId}`);
  }

  getByLocation(locationId: number): Observable<CostCenterList[]> {
    return this.apiService.get<CostCenterList[]>(`${this.path}/location/${locationId}`);
  }

  getByCompany(companyId: number): Observable<CostCenterList[]> {
    return this.apiService.get<CostCenterList[]>(`${this.path}/company/${companyId}`);
  }

  getByType(typeId: number): Observable<CostCenterList[]> {
    return this.apiService.get<CostCenterList[]>(`${this.path}/type/${typeId}`);
  }

  getCostCentersByTypeId(typeId: number): Observable<CostCenter[]> {
    return this.apiService.get<CostCenter[]>(`${this.path}/type/${typeId}`);
  }

  create(costCenter: CreateCostCenter): Observable<CostCenter> {
    return this.apiService.post<CostCenter, CreateCostCenter>(this.path, costCenter);
  }

  createCostCenter(costCenter: CreateCostCenter): Observable<CostCenter> {
    return this.create(costCenter);
  }

  update(id: number, costCenter: UpdateCostCenter): Observable<void> {
    return this.apiService.put<void, UpdateCostCenter>(`${this.path}/${id}`, costCenter);
  }

  updateCostCenter(id: number, costCenter: UpdateCostCenter): Observable<void> {
    return this.update(id, costCenter);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  deleteCostCenter(id: number): Observable<void> {
    return this.delete(id);
  }

  activate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/activate`, {});
  }

  deactivate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/deactivate`, {});
  }

  // Cost Center Types
  getAllCostCenterTypes(): Observable<CostCenterType[]> {
    return this.apiService.get<CostCenterType[]>(this.typePath);
  }

  getCostCenterTypeById(id: number): Observable<CostCenterType> {
    return this.apiService.get<CostCenterType>(`${this.typePath}/${id}`);
  }

  createCostCenterType(costCenterType: CreateCostCenterType): Observable<CostCenterType> {
    return this.apiService.post<CostCenterType, CreateCostCenterType>(this.typePath, costCenterType);
  }

  updateCostCenterType(id: number, costCenterType: UpdateCostCenterType): Observable<void> {
    return this.apiService.put<void, UpdateCostCenterType>(`${this.typePath}/${id}`, costCenterType);
  }

  deleteCostCenterType(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.typePath}/${id}`);
  }
}
