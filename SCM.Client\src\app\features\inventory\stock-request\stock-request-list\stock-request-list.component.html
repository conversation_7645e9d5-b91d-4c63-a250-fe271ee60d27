<div class="container">
  <div class="header">
    <h1>Stock Requests</h1>
    <button mat-raised-button color="primary" (click)="addStockRequest()">
      <mat-icon>add</mat-icon> New Stock Request
    </button>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>
  
  <mat-card class="filter-card" *ngIf="!isLoading">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by reference number or cost center">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilter()">
            <mat-option value="">All</mat-option>
            <mat-option *ngFor="let status of statusOptions" [value]="status">
              {{status}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2" *ngIf="!isLoading">
    <table mat-table [dataSource]="filteredRequests" class="stock-request-table" matSort>
      <!-- Reference Number Column -->
      <ng-container matColumnDef="referenceNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Reference #</th>
        <td mat-cell *matCellDef="let request">{{request.referenceNumber}}</td>
      </ng-container>
      
      <!-- From Cost Center Column -->
      <ng-container matColumnDef="fromCostCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>From</th>
        <td mat-cell *matCellDef="let request">{{request.fromCostCenterName}}</td>
      </ng-container>
      
      <!-- To Cost Center Column -->
      <ng-container matColumnDef="toCostCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>To</th>
        <td mat-cell *matCellDef="let request">{{request.toCostCenterName}}</td>
      </ng-container>
      
      <!-- Request Date Column -->
      <ng-container matColumnDef="requestDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
        <td mat-cell *matCellDef="let request">{{request.requestDate | date:'medium'}}</td>
      </ng-container>
      
      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let request">
          <span class="status-badge" [ngClass]="getStatusClass(request.status)">
            {{request.status}}
          </span>
        </td>
      </ng-container>
      
      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let request">
          <button mat-icon-button color="primary" (click)="viewStockRequest(request)" matTooltip="View Details">
            <mat-icon>visibility</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="request.status === 'Draft'" (click)="editStockRequest(request)" matTooltip="Edit">
            <mat-icon>edit</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="request.status === 'Draft'" (click)="submitStockRequest(request)" matTooltip="Submit Request">
            <mat-icon>send</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="request.status === 'Submitted'" (click)="approveStockRequest(request)" matTooltip="Approve Request">
            <mat-icon>thumb_up</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" *ngIf="request.status === 'Submitted'" (click)="rejectStockRequest(request)" matTooltip="Reject Request">
            <mat-icon>thumb_down</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="request.status === 'Approved'" (click)="completeStockRequest(request)" matTooltip="Complete Request">
            <mat-icon>check_circle</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" *ngIf="request.status === 'Draft' || request.status === 'Submitted' || request.status === 'Approved'" (click)="cancelStockRequest(request)" matTooltip="Cancel Request">
            <mat-icon>cancel</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" *ngIf="request.status === 'Draft'" (click)="deleteStockRequest(request)" matTooltip="Delete">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      
      <!-- Row shown when there is no matching data -->
      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" colspan="6">
          <div class="no-data">
            <mat-icon>info</mat-icon>
            <span>No stock requests found</span>
          </div>
        </td>
      </tr>
    </table>
    
    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
