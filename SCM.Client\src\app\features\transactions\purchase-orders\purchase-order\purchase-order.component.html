<div class="page-container">
  <div class="page-header">
    <h1>{{ isEditMode ? 'Edit Purchase Order' : 'New Purchase Order' }}</h1>
    <div class="header-actions">
      <button mat-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back
      </button>
      <button mat-raised-button color="primary" (click)="savePurchaseOrder()" [disabled]="isLoading">
        <mat-icon>save</mat-icon> Save
      </button>
      <button mat-raised-button color="accent" (click)="submitPurchaseOrder()" [disabled]="isLoading || !canSubmit()">
        <mat-icon>send</mat-icon> Submit
      </button>
      <button mat-button color="warn" (click)="cancelPurchaseOrder()" [disabled]="isLoading">
        <mat-icon>cancel</mat-icon> Cancel
      </button>
    </div>
  </div>

  <div class="spinner-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <form [formGroup]="purchaseOrderForm" class="purchase-order-form">
    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Cost Center</mat-label>
        <mat-select formControlName="sourceCostCenterId" required>
          <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
            {{ costCenter.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="purchaseOrderForm.get('sourceCostCenterId')?.hasError('required')">
          Cost center is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Supplier</mat-label>
        <mat-select formControlName="supplierId" required>
          <mat-option *ngFor="let supplier of suppliers" [value]="supplier.id">
            {{ supplier.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="purchaseOrderForm.get('supplierId')?.hasError('required')">
          Supplier is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Order Date</mat-label>
        <input matInput [matDatepicker]="orderDatePicker" formControlName="transactionDate" required>
        <mat-datepicker-toggle matSuffix [for]="orderDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #orderDatePicker></mat-datepicker>
        <mat-error *ngIf="purchaseOrderForm.get('transactionDate')?.hasError('required')">
          Order date is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Reference Number</mat-label>
        <input matInput formControlName="referenceNumber" placeholder="Enter reference number">
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field full-width">
        <mat-label>Notes</mat-label>
        <textarea matInput formControlName="notes" rows="3" placeholder="Enter notes"></textarea>
      </mat-form-field>
    </div>

    <div class="section-header">
      <h2>Order Items</h2>
      <button mat-mini-fab color="primary" (click)="addItem()" type="button">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div class="table-container mat-elevation-z2">
      <table mat-table [dataSource]="details.controls" class="order-items-table">
        <!-- Product Code Column -->
        <ng-container matColumnDef="productCode">
          <th mat-header-cell *matHeaderCellDef>Product Code</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productCode')"
                     [matAutocomplete]="auto" placeholder="Enter product code">
              <mat-autocomplete #auto="matAutocomplete">
                <mat-option *ngFor="let product of filteredProducts[i] | async"
                           [value]="product.code"
                           (click)="selectProduct(i, product)">
                  {{product.code}} - {{product.name}}
                </mat-option>
              </mat-autocomplete>
              <mat-error *ngIf="item.get('productCode')?.hasError('required')">
                Product code is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Product Name Column -->
        <ng-container matColumnDef="productName">
          <th mat-header-cell *matHeaderCellDef>Product Name</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Unit Column -->
        <ng-container matColumnDef="unitName">
          <th mat-header-cell *matHeaderCellDef>Unit</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('unitName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Quantity Column -->
        <ng-container matColumnDef="quantity">
          <th mat-header-cell *matHeaderCellDef>Quantity</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('quantity')" min="0.01" step="0.01">
              <mat-error *ngIf="item.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="item.get('quantity')?.hasError('min')">
                Quantity must be greater than 0
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Unit Price Column -->
        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef>Unit Price</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('unitPrice')" min="0" step="0.01">
              <mat-error *ngIf="item.get('unitPrice')?.hasError('required')">
                Unit price is required
              </mat-error>
              <mat-error *ngIf="item.get('unitPrice')?.hasError('min')">
                Unit price must be greater than or equal to 0
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Tax Amount Column -->
        <ng-container matColumnDef="taxAmount">
          <th mat-header-cell *matHeaderCellDef>Tax</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('taxAmount')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Discount Amount Column -->
        <ng-container matColumnDef="discountAmount">
          <th mat-header-cell *matHeaderCellDef>Discount</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('discountAmount')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Total Column -->
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef>Total</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('total')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <button mat-icon-button color="warn" (click)="removeItem(i)" type="button">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <div class="order-summary">
      <div class="order-total">
        <span>Total Amount:</span>
        <span class="total-value">{{ calculateOrderTotal() | currency }}</span>
      </div>
    </div>
  </form>
</div>
