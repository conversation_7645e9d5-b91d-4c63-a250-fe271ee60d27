<div class="container">
  <div class="header">
    <h1>Suppliers</h1>
    <button mat-raised-button color="primary" (click)="addSupplier()">
      <mat-icon>add</mat-icon> New Supplier
    </button>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>
  
  <mat-card class="filter-card" *ngIf="!isLoading">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, contact, email, or phone">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <div class="filter-options">
          <mat-checkbox [(ngModel)]="showInactive" (change)="applyFilter()">Show Inactive</mat-checkbox>
          
          <button mat-button color="primary" (click)="resetFilters()">
            <mat-icon>clear</mat-icon> Reset
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2" *ngIf="!isLoading">
    <table mat-table [dataSource]="filteredSuppliers" class="supplier-table" matSort>
      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
        <td mat-cell *matCellDef="let supplier">{{supplier.name}}</td>
      </ng-container>
      
      <!-- Contact Person Column -->
      <ng-container matColumnDef="contactPerson">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Contact Person</th>
        <td mat-cell *matCellDef="let supplier">{{supplier.contactPerson || '-'}}</td>
      </ng-container>
      
      <!-- Phone Column -->
      <ng-container matColumnDef="phone">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Phone</th>
        <td mat-cell *matCellDef="let supplier">{{supplier.phone || '-'}}</td>
      </ng-container>
      
      <!-- Email Column -->
      <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
        <td mat-cell *matCellDef="let supplier">{{supplier.email || '-'}}</td>
      </ng-container>
      
      <!-- Tax Number Column -->
      <ng-container matColumnDef="taxNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Tax Number</th>
        <td mat-cell *matCellDef="let supplier">{{supplier.taxNumber || '-'}}</td>
      </ng-container>
      
      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let supplier">
          <span class="status-badge" [ngClass]="supplier.isActive ? 'status-active' : 'status-inactive'">
            {{supplier.isActive ? 'Active' : 'Inactive'}}
          </span>
        </td>
      </ng-container>
      
      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let supplier">
          <button mat-icon-button color="primary" (click)="viewSupplier(supplier)" matTooltip="View Details">
            <mat-icon>visibility</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" (click)="editSupplier(supplier)" matTooltip="Edit">
            <mat-icon>edit</mat-icon>
          </button>
          
          <button mat-icon-button [color]="supplier.isActive ? 'warn' : 'accent'" (click)="toggleStatus(supplier)" 
                  [matTooltip]="supplier.isActive ? 'Deactivate' : 'Activate'">
            <mat-icon>{{ supplier.isActive ? 'toggle_on' : 'toggle_off' }}</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" (click)="deleteSupplier(supplier)" matTooltip="Delete">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      
      <!-- Row shown when there is no matching data -->
      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" colspan="7">
          <div class="no-data">
            <mat-icon>info</mat-icon>
            <span>No suppliers found</span>
          </div>
        </td>
      </tr>
    </table>
    
    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
