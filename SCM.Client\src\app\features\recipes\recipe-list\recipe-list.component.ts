import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { RecipeService } from '../../../core/services/recipe.service';
import { RecipeList, UpdateRecipe } from '../../../core/models/recipe.model';
import { UnitService } from '../../../core/services/unit.service';
import { ProductService } from '../../../core/services/product.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-recipe-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './recipe-list.component.html',
  styleUrls: ['./recipe-list.component.scss']
})
export class RecipeListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = ['id', 'name', 'productName', 'yield', 'ingredientCount', 'cost', 'isActive', 'actions'];
  recipes: RecipeList[] = [];
  filteredRecipes: RecipeList[] = [];
  searchTerm: string = '';
  selectedStatus: string = '';
  isLoading: boolean = false;

  statusOptions: string[] = ['All', 'Active', 'Inactive'];
  selectedCategory: string = '';
  categories: string[] = [];

  constructor(
    private recipeService: RecipeService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadRecipes();
  }

  loadRecipes(): void {
    this.isLoading = true;
    this.recipeService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.recipes = data;
          this.filteredRecipes = [...this.recipes];
        },
        error: (error) => {
          console.error('Error loading recipes', error);
          this.snackBar.open('Error loading recipes', 'Close', {
            duration: 3000
          });
        }
      });
  }

  applyFilter(): void {
    this.filteredRecipes = this.recipes.filter(recipe => {
      const matchesSearch = this.searchTerm === '' ||
        recipe.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        recipe.id.toString().includes(this.searchTerm.toLowerCase()) ||
        recipe.productName.toLowerCase().includes(this.searchTerm.toLowerCase());

      let matchesStatus = true;
      if (this.selectedStatus === 'Active') {
        matchesStatus = recipe.isActive;
      } else if (this.selectedStatus === 'Inactive') {
        matchesStatus = !recipe.isActive;
      }

      return matchesSearch && matchesStatus;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.filteredRecipes = [...this.recipes];
  }

  addRecipe(): void {
    this.router.navigate(['/recipes/new']);
  }

  editRecipe(recipe: RecipeList): void {
    this.router.navigate(['/recipes', recipe.id]);
  }

  deleteRecipe(recipe: RecipeList): void {
    if (confirm(`Are you sure you want to delete recipe "${recipe.name}"?`)) {
      this.recipeService.delete(recipe.id)
        .subscribe({
          next: () => {
            this.snackBar.open('Recipe deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadRecipes();
          },
          error: (error) => {
            console.error('Error deleting recipe', error);
            this.snackBar.open('Error deleting recipe', 'Close', {
              duration: 3000
            });
          }
        });
    }
  }

  viewRecipeDetails(recipe: RecipeList): void {
    this.router.navigate(['/recipes', recipe.id]);
  }

  duplicateRecipe(recipe: RecipeList): void {
    this.router.navigate(['/recipes/new'], {
      queryParams: { duplicate: recipe.id }
    });
  }

  toggleRecipeStatus(recipe: RecipeList): void {
    const updateRecipe: UpdateRecipe = {
      id: recipe.id,
      name: recipe.name,
      productId: recipe.productId,
      yield: recipe.yield,
      unitId: undefined,
      instructions: undefined,
      notes: undefined,
      isSubRecipe: false,
      isActive: !recipe.isActive
    };

    this.recipeService.update(recipe.id, updateRecipe)
      .subscribe({
        next: () => {
          this.snackBar.open(`Recipe ${recipe.isActive ? 'deactivated' : 'activated'} successfully`, 'Close', {
            duration: 3000
          });
          this.loadRecipes();
        },
        error: (error) => {
          console.error('Error updating recipe status', error);
          this.snackBar.open('Error updating recipe status', 'Close', {
            duration: 3000
          });
        }
      });
  }
}
