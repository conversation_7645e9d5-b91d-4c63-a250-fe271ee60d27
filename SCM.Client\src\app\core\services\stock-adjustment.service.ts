import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { 
  StockAdjustmentHeader, 
  StockAdjustmentDetail, 
  CreateStockAdjustmentHeader, 
  UpdateStockAdjustmentHeader, 
  CreateStockAdjustmentDetail, 
  UpdateStockAdjustmentDetail,
  CompleteStockAdjustment
} from '../models/stock-adjustment.model';

@Injectable({
  providedIn: 'root'
})
export class StockAdjustmentService {
  private apiUrl = `${environment.apiUrl}/stock-adjustments`;

  constructor(private http: HttpClient) { }

  // Stock Adjustment Header methods
  getAllStockAdjustments(): Observable<StockAdjustmentHeader[]> {
    return this.http.get<StockAdjustmentHeader[]>(this.apiUrl);
  }

  getStockAdjustmentById(id: number): Observable<StockAdjustmentHeader> {
    return this.http.get<StockAdjustmentHeader>(`${this.apiUrl}/${id}`);
  }

  getStockAdjustmentsByCostCenterId(costCenterId: number): Observable<StockAdjustmentHeader[]> {
    return this.http.get<StockAdjustmentHeader[]>(`${this.apiUrl}/cost-center/${costCenterId}`);
  }

  getStockAdjustmentsByStatus(status: string): Observable<StockAdjustmentHeader[]> {
    return this.http.get<StockAdjustmentHeader[]>(`${this.apiUrl}/status/${status}`);
  }

  createStockAdjustment(stockAdjustment: CreateStockAdjustmentHeader): Observable<StockAdjustmentHeader> {
    return this.http.post<StockAdjustmentHeader>(this.apiUrl, stockAdjustment);
  }

  updateStockAdjustment(id: number, stockAdjustment: UpdateStockAdjustmentHeader): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, stockAdjustment);
  }

  deleteStockAdjustment(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  completeStockAdjustment(id: number, completeData: CompleteStockAdjustment): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/complete`, completeData);
  }

  cancelStockAdjustment(id: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/cancel`, {});
  }

  // Stock Adjustment Detail methods
  getStockAdjustmentDetails(stockAdjustmentHeaderId: number): Observable<StockAdjustmentDetail[]> {
    return this.http.get<StockAdjustmentDetail[]>(`${this.apiUrl}/${stockAdjustmentHeaderId}/details`);
  }

  getStockAdjustmentDetailById(id: number): Observable<StockAdjustmentDetail> {
    return this.http.get<StockAdjustmentDetail>(`${this.apiUrl}/details/${id}`);
  }

  createStockAdjustmentDetail(stockAdjustmentHeaderId: number, detail: CreateStockAdjustmentDetail): Observable<StockAdjustmentDetail> {
    return this.http.post<StockAdjustmentDetail>(`${this.apiUrl}/${stockAdjustmentHeaderId}/details`, detail);
  }

  updateStockAdjustmentDetail(id: number, detail: UpdateStockAdjustmentDetail): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/details/${id}`, detail);
  }

  deleteStockAdjustmentDetail(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/details/${id}`);
  }
}
