export interface StockAdjustmentHeader {
  id: number;
  referenceNumber: string;
  costCenterId: number;
  costCenterName: string;
  adjustmentDate: Date;
  notes?: string;
  status: string;
  createdById?: number;
  createdByName?: string;
  completedById?: number;
  completedByName?: string;
  completedAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  details?: StockAdjustmentDetail[];
}

export interface StockAdjustmentDetail {
  id: number;
  stockAdjustmentHeaderId: number;
  productId: number;
  productName: string;
  productCode: string;
  batchId?: number;
  batchNumber?: string;
  unitId?: number;
  unitName?: string;
  currentQuantity: number;
  adjustmentQuantity: number;
  newQuantity: number;
  costPrice?: number;
  reason: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateStockAdjustmentHeader {
  costCenterId: number;
  adjustmentDate: Date;
  notes?: string;
  details: CreateStockAdjustmentDetail[];
}

export interface CreateStockAdjustmentDetail {
  productId: number;
  batchId?: number;
  unitId?: number;
  currentQuantity: number;
  adjustmentQuantity: number;
  newQuantity: number;
  costPrice?: number;
  reason: string;
  notes?: string;
}

export interface UpdateStockAdjustmentHeader {
  id: number;
  costCenterId: number;
  adjustmentDate: Date;
  notes?: string;
  status: string;
}

export interface UpdateStockAdjustmentDetail {
  id: number;
  productId: number;
  batchId?: number;
  unitId?: number;
  currentQuantity: number;
  adjustmentQuantity: number;
  newQuantity: number;
  costPrice?: number;
  reason: string;
  notes?: string;
}

export interface CompleteStockAdjustment {
  id: number;
  notes?: string;
}

export enum AdjustmentReason {
  STOCK_COUNT = 'Stock Count',
  DAMAGED = 'Damaged',
  EXPIRED = 'Expired',
  LOST = 'Lost',
  FOUND = 'Found',
  CORRECTION = 'Correction',
  OTHER = 'Other'
}
