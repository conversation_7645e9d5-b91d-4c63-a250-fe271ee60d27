import { Compo<PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StockTransferService } from '../../../../core/services/stock-transfer.service';
import { StockTransferHeader } from '../../../../core/models/stock-transfer.model';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-stock-transfer-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './stock-transfer-list.component.html',
  styleUrls: ['./stock-transfer-list.component.scss']
})
export class StockTransferListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  
  displayedColumns: string[] = [
    'referenceNumber', 
    'fromCostCenterName', 
    'toCostCenterName', 
    'transferDate', 
    'status', 
    'actions'
  ];
  
  stockTransfers: StockTransferHeader[] = [];
  filteredTransfers: StockTransferHeader[] = [];
  isLoading = false;
  searchTerm = '';
  selectedStatus = '';
  
  statusOptions = ['All', 'Draft', 'Pending', 'Completed', 'Cancelled'];

  constructor(
    private stockTransferService: StockTransferService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.loadStockTransfers();
  }

  loadStockTransfers(): void {
    this.isLoading = true;
    this.stockTransferService.getAllStockTransfers()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.stockTransfers = data;
          this.filteredTransfers = [...this.stockTransfers];
        },
        error: (error) => {
          console.error('Error loading stock transfers', error);
          this.snackBar.open('Error loading stock transfers', 'Close', {
            duration: 3000
          });
        }
      });
  }

  applyFilter(): void {
    this.filteredTransfers = this.stockTransfers.filter(transfer => {
      const matchesSearch = this.searchTerm === '' || 
        transfer.referenceNumber.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        transfer.fromCostCenterName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        transfer.toCostCenterName.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesStatus = this.selectedStatus === 'All' || this.selectedStatus === '' || 
        transfer.status === this.selectedStatus;
      
      return matchesSearch && matchesStatus;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.filteredTransfers = [...this.stockTransfers];
  }

  addStockTransfer(): void {
    this.router.navigate(['/inventory/stock-transfers/new']);
  }

  editStockTransfer(transfer: StockTransferHeader): void {
    this.router.navigate(['/inventory/stock-transfers', transfer.id]);
  }

  viewStockTransfer(transfer: StockTransferHeader): void {
    this.router.navigate(['/inventory/stock-transfers', transfer.id]);
  }

  deleteStockTransfer(transfer: StockTransferHeader): void {
    if (confirm(`Are you sure you want to delete stock transfer ${transfer.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockTransferService.deleteStockTransfer(transfer.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock transfer deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadStockTransfers();
          },
          error: (error) => {
            console.error('Error deleting stock transfer', error);
            this.snackBar.open('Error deleting stock transfer: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  completeStockTransfer(transfer: StockTransferHeader): void {
    if (confirm(`Are you sure you want to complete stock transfer ${transfer.referenceNumber}? This will update inventory levels.`)) {
      this.isLoading = true;
      this.stockTransferService.completeStockTransfer(transfer.id, { id: transfer.id })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock transfer completed successfully', 'Close', {
              duration: 3000
            });
            this.loadStockTransfers();
          },
          error: (error) => {
            console.error('Error completing stock transfer', error);
            this.snackBar.open('Error completing stock transfer: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancelStockTransfer(transfer: StockTransferHeader): void {
    if (confirm(`Are you sure you want to cancel stock transfer ${transfer.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockTransferService.cancelStockTransfer(transfer.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock transfer cancelled successfully', 'Close', {
              duration: 3000
            });
            this.loadStockTransfers();
          },
          error: (error) => {
            console.error('Error cancelling stock transfer', error);
            this.snackBar.open('Error cancelling stock transfer: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Draft': return 'status-draft';
      case 'Pending': return 'status-pending';
      case 'Completed': return 'status-completed';
      case 'Cancelled': return 'status-cancelled';
      default: return '';
    }
  }
}
