export interface Unit {
  id: number;
  name: string;
  abbreviation: string;
  unitGroupId: number;
  unitGroupName?: string;
  conversionFactor: number;
  baseConversionFactor: number;
  isBaseUnit: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface UnitGroup {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  units?: Unit[];
  unitCount: number;
}
