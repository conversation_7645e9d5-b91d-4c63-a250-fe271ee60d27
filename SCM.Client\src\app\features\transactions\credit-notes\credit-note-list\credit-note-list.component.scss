.credit-note-list-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

mat-card {
  margin-bottom: 20px;
}

mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-actions {
    margin-left: auto;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  mat-spinner {
    margin-bottom: 20px;
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);

  mat-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 10px 0;
    font-weight: 400;
  }

  p {
    margin: 0 0 30px 0;
    font-size: 14px;
  }
}

.table-container {
  overflow-x: auto;
}

.credit-notes-table {
  width: 100%;
  min-width: 1000px;

  .transaction-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  mat-chip {
    font-size: 12px;
    min-height: 24px;
  }

  .delete-action {
    color: #f44336;
  }
}

// Responsive design
@media (max-width: 768px) {
  .credit-note-list-container {
    padding: 10px;
  }

  mat-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    .header-actions {
      margin-left: 0;
      width: 100%;
    }
  }

  .credit-notes-table {
    min-width: 800px;
  }
}
