.unit-groups-container {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    color: #1976d2;
  }
}

.unit-groups-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
  
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

.form-card, .table-card {
  width: 100%;
}

.form-row {
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.form-field-checkbox {
  margin: 20px 0;
  display: flex;
  align-items: center;
  
  label {
    margin-right: 10px;
  }
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.status-active {
  color: green;
  font-weight: 500;
}

.status-inactive {
  color: red;
  font-weight: 500;
}

.filter-container {
  margin-bottom: 16px;
}

.filter-field {
  width: 100%;
}

table {
  width: 100%;
}

.mat-column-id {
  width: 5%;
}

.mat-column-name {
  width: 20%;
}

.mat-column-description {
  width: 35%;
}

.mat-column-unitCount {
  width: 10%;
}

.mat-column-isActive {
  width: 10%;
}

.mat-column-actions {
  width: 20%;
  text-align: center;
}
