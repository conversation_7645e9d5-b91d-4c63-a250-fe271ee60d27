using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class ProductRequestService : IProductRequestService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ITransactionService _transactionService;

    public ProductRequestService(
        ApplicationDbContext dbContext,
        IMapper mapper,
        ITransactionService transactionService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _transactionService = transactionService;
    }

    public async Task<IEnumerable<ProductRequestListItem>> GetAllAsync()
    {
        // Get all transactions that are product requests (stage type = Request)
        var requestStageType = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(st => st.Name == "Request");

        if (requestStageType == null)
            return new List<ProductRequestListItem>();

        // Use standard Entity Framework approach
        var transactions = await _dbContext.TransactionHeaders
            .Where(t => t.StageTypeId == requestStageType.Id)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();

        // Load the cost centers manually
        foreach (var transaction in transactions)
        {
            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters
                    .FirstOrDefaultAsync(cc => cc.Id == transaction.SourceCostCenterId.Value);
            }
        }

        // Map TransactionHeader to ProductRequestListItem
        var result = _mapper.Map<IEnumerable<ProductRequestListItem>>(transactions).ToList();

        // Manually set the cost center name since it's ignored in the mapping
        foreach (var item in result)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == item.Id);
            if (transaction?.SourceCostCenter != null)
            {
                item.CostCenterName = transaction.SourceCostCenter.Name;
            }
        }

        return result;
    }

    public async Task<ProductRequestHeader?> GetByIdAsync(int id)
    {
        // Get the transaction by ID
        var transactionDto = await _transactionService.GetTransactionByIdAsync(id);
        if (transactionDto == null)
            return null;

        // Map TransactionHeaderDto to ProductRequestHeader
        return _mapper.Map<ProductRequestHeader>(transactionDto);
    }

    public async Task<IEnumerable<ProductRequestListItem>> GetByCostCenterAsync(int costCenterId)
    {
        // Get all transactions that are product requests (stage type = Request) for the specified cost center
        var requestStageType = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(st => st.Name == "Request");

        if (requestStageType == null)
            return new List<ProductRequestListItem>();

        // Use standard Entity Framework approach
        var transactions = await _dbContext.TransactionHeaders
            .Where(t => t.StageTypeId == requestStageType.Id && t.SourceCostCenterId == costCenterId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();

        // Load the cost centers manually
        foreach (var transaction in transactions)
        {
            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters
                    .FirstOrDefaultAsync(cc => cc.Id == transaction.SourceCostCenterId.Value);
            }
        }

        // Map TransactionHeader to ProductRequestListItem
        var result = _mapper.Map<IEnumerable<ProductRequestListItem>>(transactions).ToList();

        // Manually set the cost center name since it's ignored in the mapping
        foreach (var item in result)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == item.Id);
            if (transaction?.SourceCostCenter != null)
            {
                item.CostCenterName = transaction.SourceCostCenter.Name;
            }
        }

        return result;
    }

    public async Task<IEnumerable<ProductRequestListItem>> GetByStatusAsync(string status)
    {
        // Get all transactions that are product requests (stage type = Request) with the specified status
        var requestStageType = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(st => st.Name == "Request");

        if (requestStageType == null)
            return new List<ProductRequestListItem>();

        // Use standard Entity Framework approach
        var transactions = await _dbContext.TransactionHeaders
            .Where(t => t.StageTypeId == requestStageType.Id && t.Status == status)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();

        // Load the cost centers manually
        foreach (var transaction in transactions)
        {
            if (transaction.SourceCostCenterId.HasValue)
            {
                transaction.SourceCostCenter = await _dbContext.CostCenters
                    .FirstOrDefaultAsync(cc => cc.Id == transaction.SourceCostCenterId.Value);
            }
        }

        // Map TransactionHeader to ProductRequestListItem
        var result = _mapper.Map<IEnumerable<ProductRequestListItem>>(transactions).ToList();

        // Manually set the cost center name since it's ignored in the mapping
        foreach (var item in result)
        {
            var transaction = transactions.FirstOrDefault(t => t.Id == item.Id);
            if (transaction?.SourceCostCenter != null)
            {
                item.CostCenterName = transaction.SourceCostCenter.Name;
            }
        }

        return result;
    }

    public async Task<ProductRequestHeader> CreateAsync(CreateProductRequestHeader createProductRequest)
    {
        try
        {
            // Convert CreateProductRequestHeader to CreateProductRequestDto
            var createProductRequestDto = new CreateProductRequestDto
            {
                SourceCostCenterId = createProductRequest.CostCenterId,
                TransactionDate = createProductRequest.RequestDate,
                RequiredDate = createProductRequest.RequestDate.AddDays(7), // Default to 7 days from request date
                Notes = createProductRequest.Notes,
                Details = createProductRequest.Details?.Select(d => new CreateTransactionDetailDto
                {
                    ProductId = d.ProductId,
                    BatchId = d.BatchId,
                    UnitId = d.UnitId,
                    Quantity = d.Quantity,
                    UnitPrice = d.Price,
                    Notes = d.Notes
                }).ToList()
            };

            // Use TransactionService to create the product request
            var transactionDto = await _transactionService.CreateProductRequestAsync(createProductRequestDto, 1); // TODO: Get user ID from current user

            // Map TransactionHeaderDto to ProductRequestHeader
            return _mapper.Map<ProductRequestHeader>(transactionDto);
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error creating product request: {ex.Message}");
            throw;
        }
    }

    public async Task UpdateAsync(int id, UpdateProductRequestHeader updateProductRequest)
    {
        // Convert UpdateProductRequestHeader to UpdateProductRequestDto
        var updateProductRequestDto = new UpdateProductRequestDto
        {
            SourceCostCenterId = updateProductRequest.CostCenterId,
            TransactionDate = updateProductRequest.RequestDate,
            RequiredDate = updateProductRequest.RequestDate.AddDays(7), // Default to 7 days from request date
            Notes = updateProductRequest.Notes
        };

        // Use TransactionService to update the product request
        await _transactionService.UpdateProductRequestAsync(id, updateProductRequestDto);
    }

    public async Task DeleteAsync(int id)
    {
        // Get the transaction
        var transaction = await _dbContext.TransactionHeaders
            .Include(t => t.Details)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (transaction == null)
            throw new KeyNotFoundException($"Product request with ID {id} not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Can only delete product requests in Draft status.");

        // Delete the transaction details and header
        _dbContext.TransactionDetails.RemoveRange(transaction.Details);
        _dbContext.TransactionHeaders.Remove(transaction);
        await _dbContext.SaveChangesAsync();
    }

    public async Task SubmitAsync(SubmitProductRequestDto submitDto)
    {
        // Use TransactionService to submit the product request
        await _transactionService.SubmitProductRequestAsync(submitDto.Id, submitDto.Notes);
    }

    public async Task ApproveAsync(ApproveProductRequestDto approveDto)
    {
        // Use TransactionService to approve the product request
        await _transactionService.ApproveProductRequestAsync(approveDto.Id, 1, approveDto.Notes); // TODO: Get user ID from current user
    }

    public async Task RejectAsync(RejectProductRequestDto rejectDto)
    {
        // Use TransactionService to reject the product request
        await _transactionService.RejectProductRequestAsync(rejectDto.Id, rejectDto.Reason);
    }

    public async Task CompleteAsync(CompleteProductRequestDto completeDto)
    {
        // For product requests, completing means creating a product order
        // Get the product request
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == completeDto.Id);

        if (transaction == null)
            throw new KeyNotFoundException($"Product request with ID {completeDto.Id} not found.");

        if (transaction.Status != "Approved")
            throw new InvalidOperationException("Can only complete product requests in Approved status.");

        // Update the status to Completed
        transaction.Status = "Completed";
        transaction.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelAsync(int id)
    {
        // Use TransactionService to cancel the product request
        await _transactionService.CancelProductRequestAsync(id);
    }

    public async Task<IEnumerable<ProductRequestDetail>> GetDetailsAsync(int productRequestId)
    {
        // Get the transaction details
        var details = await _dbContext.TransactionDetails
            .Include(d => d.Product)
            .Include(d => d.Unit)
            .Where(d => d.TransactionId == productRequestId)
            .ToListAsync();

        // Map TransactionDetail to ProductRequestDetail
        return _mapper.Map<IEnumerable<ProductRequestDetail>>(details);
    }

    public async Task<ProductRequestDetail> AddDetailAsync(int productRequestId, CreateProductRequestDetail createDetail)
    {
        // Get the transaction
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.Id == productRequestId);

        if (transaction == null)
            throw new KeyNotFoundException($"Product request with ID {productRequestId} not found.");

        if (transaction.Status != "Draft")
            throw new InvalidOperationException("Can only add details to product requests in Draft status.");

        // Verify product exists
        var product = await _dbContext.Products.FindAsync(createDetail.ProductId);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {createDetail.ProductId} not found.");

        // Create a new transaction detail
        var detail = new TransactionDetail
        {
            TransactionId = productRequestId,
            ProductId = createDetail.ProductId,
            BatchId = createDetail.BatchId,
            UnitId = createDetail.UnitId ?? product.UnitId ?? 1,
            Quantity = createDetail.Quantity,
            UnitPrice = createDetail.Price ?? 0,
            Notes = createDetail.Notes,
            LineTotal = createDetail.Price.HasValue ? createDetail.Quantity * createDetail.Price.Value : 0,
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };

        _dbContext.TransactionDetails.Add(detail);
        await _dbContext.SaveChangesAsync();

        // Get the created detail with related entities
        var createdDetail = await _dbContext.TransactionDetails
            .Include(d => d.Product)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == detail.Id);

        // Map TransactionDetail to ProductRequestDetail
        return _mapper.Map<ProductRequestDetail>(createdDetail);
    }

    public async Task UpdateDetailAsync(int id, UpdateProductRequestDetail updateDetail)
    {
        // Get the transaction detail
        var detail = await _dbContext.TransactionDetails
            .Include(d => d.Transaction)
            .FirstOrDefaultAsync(d => d.Id == id);

        if (detail == null)
            throw new KeyNotFoundException($"Product request detail with ID {id} not found.");

        if (detail.Transaction.Status != "Draft")
            throw new InvalidOperationException("Can only update details of product requests in Draft status.");

        // Update the detail
        detail.ProductId = updateDetail.ProductId;
        detail.BatchId = updateDetail.BatchId;
        detail.UnitId = updateDetail.UnitId ?? detail.UnitId;
        detail.Quantity = updateDetail.Quantity;
        detail.UnitPrice = updateDetail.Price ?? detail.UnitPrice;
        detail.Notes = updateDetail.Notes ?? detail.Notes;
        detail.LineTotal = updateDetail.Price.HasValue ? updateDetail.Quantity * updateDetail.Price.Value : detail.Quantity * detail.UnitPrice;
        detail.UpdatedAt = DateTime.UtcNow;

        _dbContext.TransactionDetails.Update(detail);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteDetailAsync(int id)
    {
        // Get the transaction detail
        var detail = await _dbContext.TransactionDetails
            .Include(d => d.Transaction)
            .FirstOrDefaultAsync(d => d.Id == id);

        if (detail == null)
            throw new KeyNotFoundException($"Product request detail with ID {id} not found.");

        if (detail.Transaction.Status != "Draft")
            throw new InvalidOperationException("Can only delete details of product requests in Draft status.");

        _dbContext.TransactionDetails.Remove(detail);
        await _dbContext.SaveChangesAsync();
    }
}
