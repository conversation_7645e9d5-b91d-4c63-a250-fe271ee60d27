using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class StockTransferHeader : BaseEntity
{
    public string ReferenceNumber { get; set; } = string.Empty;
    public int FromCostCenterId { get; set; }
    public int ToCostCenterId { get; set; }
    public DateTime TransferDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft"; // Draft, Pending, Completed, Cancelled
    public int? CreatedById { get; set; }
    public int? CompletedById { get; set; }
    public DateTime? CompletedAt { get; set; }
    
    // Navigation properties
    public virtual CostCenter FromCostCenter { get; set; } = null!;
    public virtual CostCenter ToCostCenter { get; set; } = null!;
    public virtual User? CreatedBy { get; set; }
    public virtual User? CompletedBy { get; set; }
    public virtual ICollection<StockTransferDetail> Details { get; set; } = new List<StockTransferDetail>();
}
