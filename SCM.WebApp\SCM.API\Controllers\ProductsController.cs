using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Application.Services;

namespace SCM.API.Controllers;

public class ProductsController : ApiControllerBase
{
    private readonly IProductService _productService;
    private readonly IProductImportService _productImportService;
    private readonly ILogger<ProductsController> _logger;

    public ProductsController(
        IProductService productService,
        IProductImportService productImportService,
        ILogger<ProductsController> logger)
    {
        _productService = productService;
        _productImportService = productImportService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetAll()
    {
        var products = await _productService.GetAllProductsAsync();
        return Ok(products);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductDto>> GetById(int id)
    {
        var product = await _productService.GetProductByIdAsync(id);
        if (product == null)
            return NotFound();

        return Ok(product);
    }

    [HttpGet("code/{code}")]
    public async Task<ActionResult<ProductDto>> GetByCode(string code)
    {
        var product = await _productService.GetProductByCodeAsync(code);
        if (product == null)
            return NotFound();

        return Ok(product);
    }

    [HttpGet("department/{departmentId}")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetByDepartmentId(int departmentId)
    {
        var products = await _productService.GetProductsByDepartmentIdAsync(departmentId);
        return Ok(products);
    }

    [HttpGet("group/{groupId}")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetByGroupId(int groupId)
    {
        var products = await _productService.GetProductsByGroupIdAsync(groupId);
        return Ok(products);
    }

    [HttpGet("subgroup/{subGroupId}")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> GetBySubGroupId(int subGroupId)
    {
        var products = await _productService.GetProductsBySubGroupIdAsync(subGroupId);
        return Ok(products);
    }

    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<ProductDto>>> Search([FromQuery] string term)
    {
        if (string.IsNullOrWhiteSpace(term))
            return BadRequest("Search term is required");

        var products = await _productService.SearchProductsAsync(term);
        return Ok(products);
    }

    [HttpPost]
    public async Task<ActionResult<ProductDto>> Create(CreateProductDto createProductDto)
    {
        var product = await _productService.CreateProductAsync(createProductDto);
        return CreatedAtAction(nameof(GetById), new { id = product.Id }, product);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateProductDto updateProductDto)
    {
        if (id != updateProductDto.Id)
            return BadRequest();

        try
        {
            await _productService.UpdateProductAsync(updateProductDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _productService.DeleteProductAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("import")]
    public async Task<ActionResult<ProductImportDto>> ImportProducts(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest("Only Excel (.xlsx) files are supported");
            }

            using var stream = file.OpenReadStream();
            var result = await _productImportService.ImportProductsFromExcelAsync(stream);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing products");
            return StatusCode(500, "An error occurred while importing products");
        }
    }

    [HttpGet("import/template")]
    public async Task<IActionResult> DownloadTemplate()
    {
        try
        {
            var templateBytes = await _productImportService.GenerateTemplateAsync();
            return File(templateBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "ProductImportTemplate.xlsx");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating import template");
            return StatusCode(500, "An error occurred while generating the import template");
        }
    }
}
