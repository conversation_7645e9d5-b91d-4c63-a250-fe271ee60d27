import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName?: string;
  phone?: string;
  roleId?: number;
  roleName?: string;
  isAdmin: boolean;
  lastLogin?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  permissionIds: number[];
}

export interface UserList {
  id: number;
  username: string;
  email: string;
  fullName: string;
  roleName?: string;
  lastLogin?: Date;
  isActive: boolean;
}

export interface CreateUser {
  username: string;
  email: string;
  firstName: string;
  lastName?: string;
  phone?: string;
  roleId?: number;
  password: string;
  isAdmin: boolean;
  permissionIds: number[];
}

export interface UpdateUser {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName?: string;
  phone?: string;
  roleId?: number;
  isAdmin: boolean;
  isActive: boolean;
  permissionIds: number[];
}

export interface ChangePassword {
  userId: number;
  currentPassword: string;
  newPassword: string;
}

export interface ResetPassword {
  userId: number;
  newPassword: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly path = 'users';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<UserList[]> {
    return this.apiService.get<UserList[]>(this.path);
  }

  getById(id: number): Observable<User> {
    return this.apiService.get<User>(`${this.path}/${id}`);
  }

  getByUsername(username: string): Observable<User> {
    return this.apiService.get<User>(`${this.path}/by-username/${username}`);
  }

  create(user: CreateUser): Observable<User> {
    return this.apiService.post<User, CreateUser>(this.path, user);
  }

  update(id: number, user: UpdateUser): Observable<void> {
    return this.apiService.put<void, UpdateUser>(`${this.path}/${id}`, user);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  changePassword(id: number, changePassword: ChangePassword): Observable<void> {
    return this.apiService.post<void, ChangePassword>(`${this.path}/${id}/change-password`, changePassword);
  }

  resetPassword(id: number, resetPassword: ResetPassword): Observable<void> {
    return this.apiService.post<void, ResetPassword>(`${this.path}/${id}/reset-password`, resetPassword);
  }

  validatePassword(id: number, password: string): Observable<boolean> {
    return this.apiService.post<boolean, string>(`${this.path}/${id}/validate-password`, password);
  }
}
