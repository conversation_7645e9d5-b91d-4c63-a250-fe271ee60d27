using Mapster;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SCM.Application.Services
{
    public class LocationService : ILocationService
    {
        private readonly IRepository<Location> _locationRepository;
        private readonly ApplicationDbContext _dbContext;
        private readonly IMapper _mapper;

        public LocationService(IRepository<Location> locationRepository, ApplicationDbContext dbContext, IMapper mapper)
        {
            _locationRepository = locationRepository ?? throw new ArgumentNullException(nameof(locationRepository));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public async Task<IEnumerable<LocationDto>> GetAllLocationsAsync()
        {
            try
            {
                // Get all locations with eager loading of related entities
                var locations = await _dbContext.Locations
                    .Include(l => l.Company)
                    .ToListAsync();

                // Manually map to DTOs to avoid null reference exceptions
                var locationDtos = new List<LocationDto>();
                foreach (var location in locations)
                {
                    var dto = new LocationDto
                    {
                        Id = location.Id,
                        Name = location.Name,
                        Code = location.Code,
                        Description = location.Description,
                        CompanyId = location.CompanyId,
                        CompanyName = location.Company?.Name,
                        Address = location.Address,
                        City = location.City,
                        State = location.State,
                        Country = location.Country,
                        PostalCode = location.PostalCode,
                        Phone = location.Phone,
                        Email = location.Email,
                        IsActive = location.IsActive,
                        CreatedAt = location.CreatedAt,
                        UpdatedAt = location.UpdatedAt,
                        StoresCount = await _dbContext.Stores.CountAsync(s => s.LocationId == location.Id)
                    };

                    locationDtos.Add(dto);
                }

                return locationDtos;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in GetAllLocationsAsync: {ex.Message}");

                // Return an empty list instead of throwing an exception
                return new List<LocationDto>();
            }
        }

        public async Task<LocationDto?> GetLocationByIdAsync(int id)
        {
            try
            {
                var location = await _dbContext.Locations
                    .Include(l => l.Company)
                    .FirstOrDefaultAsync(l => l.Id == id);

                if (location == null)
                    return null;

                var dto = new LocationDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    Code = location.Code,
                    Description = location.Description,
                    CompanyId = location.CompanyId,
                    CompanyName = location.Company?.Name,
                    Address = location.Address,
                    City = location.City,
                    State = location.State,
                    Country = location.Country,
                    PostalCode = location.PostalCode,
                    Phone = location.Phone,
                    Email = location.Email,
                    IsActive = location.IsActive,
                    CreatedAt = location.CreatedAt,
                    UpdatedAt = location.UpdatedAt,
                    StoresCount = await _dbContext.Stores.CountAsync(s => s.LocationId == location.Id)
                };

                return dto;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in GetLocationByIdAsync: {ex.Message}");
                return null;
            }
        }

        public async Task<IEnumerable<LocationDto>> GetLocationsByStoreIdAsync(int storeId)
        {
            try
            {
                var store = await _dbContext.Stores
                    .Include(s => s.Location)
                    .ThenInclude(l => l.Company)
                    .FirstOrDefaultAsync(s => s.Id == storeId);

                if (store == null || store.Location == null)
                    return new List<LocationDto>();

                var location = store.Location;
                var dto = new LocationDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    Code = location.Code,
                    Description = location.Description,
                    CompanyId = location.CompanyId,
                    CompanyName = location.Company?.Name,
                    Address = location.Address,
                    City = location.City,
                    State = location.State,
                    Country = location.Country,
                    PostalCode = location.PostalCode,
                    Phone = location.Phone,
                    Email = location.Email,
                    IsActive = location.IsActive,
                    CreatedAt = location.CreatedAt,
                    UpdatedAt = location.UpdatedAt,
                    StoresCount = await _dbContext.Stores.CountAsync(s => s.LocationId == location.Id)
                };

                return new List<LocationDto> { dto };
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in GetLocationsByStoreIdAsync: {ex.Message}");

                // Return an empty list instead of throwing an exception
                return new List<LocationDto>();
            }
        }

        public async Task<IEnumerable<LocationDto>> GetLocationsByCompanyIdAsync(int companyId)
        {
            try
            {
                var locations = await _dbContext.Locations
                    .Include(l => l.Company)
                    .Where(l => l.CompanyId == companyId)
                    .ToListAsync();

                // Manually map to DTOs to avoid null reference exceptions
                var locationDtos = new List<LocationDto>();
                foreach (var location in locations)
                {
                    var dto = new LocationDto
                    {
                        Id = location.Id,
                        Name = location.Name,
                        Code = location.Code,
                        Description = location.Description,
                        CompanyId = location.CompanyId,
                        CompanyName = location.Company?.Name,
                        Address = location.Address,
                        City = location.City,
                        State = location.State,
                        Country = location.Country,
                        PostalCode = location.PostalCode,
                        Phone = location.Phone,
                        Email = location.Email,
                        IsActive = location.IsActive,
                        CreatedAt = location.CreatedAt,
                        UpdatedAt = location.UpdatedAt,
                        StoresCount = await _dbContext.Stores.CountAsync(s => s.LocationId == location.Id)
                    };

                    locationDtos.Add(dto);
                }

                return locationDtos;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in GetLocationsByCompanyIdAsync: {ex.Message}");

                // Return an empty list instead of throwing an exception
                return new List<LocationDto>();
            }
        }

        public async Task<LocationDto?> GetLocationWithCostCentersAsync(int id)
        {
            try
            {
                var location = await _dbContext.Locations
                    .Include(l => l.Company)
                    .Include(l => l.CostCenters)
                    .FirstOrDefaultAsync(l => l.Id == id);

                if (location == null)
                    return null;

                var dto = new LocationDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    Code = location.Code,
                    Description = location.Description,
                    CompanyId = location.CompanyId,
                    CompanyName = location.Company?.Name,
                    Address = location.Address,
                    City = location.City,
                    State = location.State,
                    Country = location.Country,
                    PostalCode = location.PostalCode,
                    Phone = location.Phone,
                    Email = location.Email,
                    IsActive = location.IsActive,
                    CreatedAt = location.CreatedAt,
                    UpdatedAt = location.UpdatedAt,
                    StoresCount = await _dbContext.Stores.CountAsync(s => s.LocationId == location.Id),
                    CostCenters = location.CostCenters?.Select(cc => new CostCenterDto
                    {
                        Id = cc.Id,
                        Name = cc.Name,
                        StoreId = cc.StoreId,
                        TypeId = cc.TypeId,
                        TypeName = cc.TypeName,
                        IsActive = cc.IsActive
                    }).ToList()
                };

                return dto;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in GetLocationWithCostCentersAsync: {ex.Message}");
                return null;
            }
        }

        public async Task<LocationDto?> GetLocationWithStoresAsync(int id)
        {
            try
            {
                var location = await _dbContext.Locations
                    .Include(l => l.Company)
                    .Include(l => l.Stores)
                    .FirstOrDefaultAsync(l => l.Id == id);

                if (location == null)
                    return null;

                var dto = new LocationDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    Code = location.Code,
                    Description = location.Description,
                    CompanyId = location.CompanyId,
                    CompanyName = location.Company?.Name,
                    Address = location.Address,
                    City = location.City,
                    State = location.State,
                    Country = location.Country,
                    PostalCode = location.PostalCode,
                    Phone = location.Phone,
                    Email = location.Email,
                    IsActive = location.IsActive,
                    CreatedAt = location.CreatedAt,
                    UpdatedAt = location.UpdatedAt,
                    StoresCount = location.Stores?.Count ?? 0,
                    Stores = location.Stores?.Select(s => new StoreDto
                    {
                        Id = s.Id,
                        Name = s.Name,
                        LocationId = s.LocationId,
                        IsSalesPoint = s.IsSalesPoint,
                        IsActive = s.IsActive
                    }).ToList()
                };

                return dto;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in GetLocationWithStoresAsync: {ex.Message}");
                return null;
            }
        }

        public async Task<LocationDto> CreateLocationAsync(CreateLocationDto createLocationDto)
        {
            try
            {
                var location = new Location
                {
                    Name = createLocationDto.Name,
                    Code = createLocationDto.Code,
                    Description = createLocationDto.Description,
                    CompanyId = createLocationDto.CompanyId,
                    Address = createLocationDto.Address,
                    City = createLocationDto.City,
                    State = createLocationDto.State,
                    Country = createLocationDto.Country,
                    PostalCode = createLocationDto.PostalCode,
                    Phone = createLocationDto.Phone,
                    Email = createLocationDto.Email,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                await _locationRepository.AddAsync(location);

                // Get the company name for the DTO
                string? companyName = null;
                if (location.CompanyId > 0)
                {
                    var company = await _dbContext.Companies.FindAsync(location.CompanyId);
                    companyName = company?.Name;
                }

                return new LocationDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    Code = location.Code,
                    Description = location.Description,
                    CompanyId = location.CompanyId,
                    CompanyName = companyName,
                    Address = location.Address,
                    City = location.City,
                    State = location.State,
                    Country = location.Country,
                    PostalCode = location.PostalCode,
                    Phone = location.Phone,
                    Email = location.Email,
                    IsActive = location.IsActive,
                    CreatedAt = location.CreatedAt,
                    UpdatedAt = location.UpdatedAt,
                    StoresCount = 0
                };
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in CreateLocationAsync: {ex.Message}");
                throw; // Rethrow the exception to be handled by the controller
            }
        }

        public async Task UpdateLocationAsync(UpdateLocationDto updateLocationDto)
        {
            try
            {
                var location = await _locationRepository.GetByIdAsync(updateLocationDto.Id);
                if (location == null)
                    throw new KeyNotFoundException($"Location with ID {updateLocationDto.Id} not found.");

                // Manually update the location properties
                location.Name = updateLocationDto.Name;
                location.Code = updateLocationDto.Code;
                location.Description = updateLocationDto.Description;
                location.CompanyId = updateLocationDto.CompanyId;
                location.Address = updateLocationDto.Address;
                location.City = updateLocationDto.City;
                location.State = updateLocationDto.State;
                location.Country = updateLocationDto.Country;
                location.PostalCode = updateLocationDto.PostalCode;
                location.Phone = updateLocationDto.Phone;
                location.Email = updateLocationDto.Email;
                location.IsActive = updateLocationDto.IsActive;
                location.UpdatedAt = DateTime.UtcNow;

                await _locationRepository.UpdateAsync(location);
            }
            catch (KeyNotFoundException)
            {
                // Rethrow KeyNotFoundException to be handled by the controller
                throw;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in UpdateLocationAsync: {ex.Message}");
                throw; // Rethrow the exception to be handled by the controller
            }
        }

        public async Task DeleteLocationAsync(int id)
        {
            try
            {
                var location = await _locationRepository.GetByIdAsync(id);
                if (location == null)
                    throw new KeyNotFoundException($"Location with ID {id} not found.");

                await _locationRepository.DeleteAsync(location);
            }
            catch (KeyNotFoundException)
            {
                // Rethrow KeyNotFoundException to be handled by the controller
                throw;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error in DeleteLocationAsync: {ex.Message}");
                throw; // Rethrow the exception to be handled by the controller
            }
        }
    }
}
