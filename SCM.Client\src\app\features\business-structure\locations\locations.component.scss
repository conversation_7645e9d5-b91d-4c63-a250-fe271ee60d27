.container {
  padding: 20px;
}

.row {
  margin-bottom: 20px;
}

.full-width {
  width: 100%;
}

.button-row {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-container {
  position: relative;
  min-height: 200px;
  overflow: auto;
}

.no-data-message {
  text-align: center;
  padding: 20px;
  font-style: italic;
  color: rgba(0, 0, 0, 0.54);
}

.mat-column-actions {
  width: 120px;
  text-align: center;
}

.mat-column-isActive {
  width: 100px;
  text-align: center;
}

.mat-column-storesCount {
  width: 100px;
  text-align: center;
}

mat-card {
  margin-bottom: 20px;
}

.mt-4 {
  margin-top: 1.5rem;
}
