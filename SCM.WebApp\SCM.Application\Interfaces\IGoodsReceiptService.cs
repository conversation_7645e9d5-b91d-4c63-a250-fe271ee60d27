using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IGoodsReceiptService
{
    Task<IEnumerable<GoodsReceiptListDto>> GetAllGoodsReceiptsAsync();
    Task<GoodsReceiptHeaderDto?> GetGoodsReceiptByIdAsync(int id);
    Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByPurchaseOrderIdAsync(int purchaseOrderId);
    Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsBySupplierIdAsync(int supplierId);
    Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByStatusAsync(string status);
    Task<GoodsReceiptHeaderDto> CreateGoodsReceiptAsync(CreateGoodsReceiptHeaderDto createGoodsReceiptHeaderDto);
    Task UpdateGoodsReceiptAsync(UpdateGoodsReceiptHeaderDto updateGoodsReceiptHeaderDto);
    Task DeleteGoodsReceiptAsync(int id);
    Task CompleteGoodsReceiptAsync(CompleteGoodsReceiptDto completeGoodsReceiptDto);
    Task CancelGoodsReceiptAsync(int id);
    Task<IEnumerable<GoodsReceiptDetailDto>> GetGoodsReceiptDetailsAsync(int goodsReceiptHeaderId);
    Task<GoodsReceiptDetailDto?> GetGoodsReceiptDetailByIdAsync(int id);
    Task<GoodsReceiptDetailDto> CreateGoodsReceiptDetailAsync(int goodsReceiptHeaderId, CreateGoodsReceiptDetailDto createGoodsReceiptDetailDto);
    Task UpdateGoodsReceiptDetailAsync(UpdateGoodsReceiptDetailDto updateGoodsReceiptDetailDto);
    Task DeleteGoodsReceiptDetailAsync(int id);
    Task<PurchaseOrderDto?> GetPurchaseOrderForReceivingAsync(int purchaseOrderId);
}
