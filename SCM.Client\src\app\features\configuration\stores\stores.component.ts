import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { StoreService } from '../../../core/services/store.service';
import { LocationService } from '../../../core/services/location.service';
import { Store, StoreList, CreateStore, UpdateStore } from '../../../core/models/store.model';
import { Location, LocationList } from '../../../core/models/location.model';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-stores',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './stores.component.html',
  styleUrls: ['./stores.component.scss']
})
export class StoresComponent implements OnInit {
  storeForm!: FormGroup;
  stores: StoreList[] = [];
  locations: LocationList[] = [];
  displayedColumns: string[] = ['name', 'code', 'locationName', 'city', 'country', 'isSalesPoint', 'isActive', 'costCentersCount', 'actions'];
  isLoading = false;
  isSubmitting = false;
  isEditMode = false;
  selectedStoreId: number | null = null;
  
  // Pagination
  totalItems = 0;
  pageSize = 10;
  pageIndex = 0;
  
  constructor(
    private fb: FormBuilder,
    private storeService: StoreService,
    private locationService: LocationService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute
  ) {}
  
  ngOnInit(): void {
    this.initForm();
    this.loadStores();
    this.loadLocations();
  }
  
  initForm(): void {
    this.storeForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(150)]],
      code: ['', Validators.maxLength(50)],
      locationId: [null, Validators.required],
      description: ['', Validators.maxLength(500)],
      address: ['', Validators.maxLength(200)],
      city: ['', Validators.maxLength(100)],
      state: ['', Validators.maxLength(100)],
      country: ['', Validators.maxLength(100)],
      postalCode: ['', Validators.maxLength(20)],
      phone: ['', Validators.maxLength(20)],
      email: ['', [Validators.email, Validators.maxLength(100)]],
      contactPerson: ['', Validators.maxLength(100)],
      isSalesPoint: [false]
    });
  }
  
  loadLocations(): void {
    this.locationService.getAll()
      .subscribe({
        next: (data) => {
          this.locations = data;
        },
        error: (error) => {
          console.error('Error loading locations:', error);
          this.snackBar.open('Failed to load locations', 'Close', { duration: 3000 });
        }
      });
  }
  
  loadStores(): void {
    this.isLoading = true;
    this.storeService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.stores = data;
          this.totalItems = data.length;
        },
        error: (error) => {
          console.error('Error loading stores:', error);
          this.snackBar.open('Failed to load stores', 'Close', { duration: 3000 });
        }
      });
  }
  
  onSubmit(): void {
    if (this.storeForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    if (this.isEditMode && this.selectedStoreId) {
      const updateData: UpdateStore = {
        ...this.storeForm.value,
        id: this.selectedStoreId,
        isActive: true
      };

      this.storeService.update(this.selectedStoreId, updateData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Store updated successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadStores();
          },
          error: (error) => {
            console.error('Error updating store:', error);
            const errorMessage = error.error || 'Failed to update store. Please try again later.';
            this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
          }
        });
    } else {
      const createData: CreateStore = this.storeForm.value;

      this.storeService.create(createData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Store created successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadStores();
          },
          error: (error) => {
            console.error('Error creating store:', error);
            const errorMessage = error.error || 'Failed to create store. Please try again later.';
            this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
          }
        });
    }
  }
  
  editStore(store: StoreList): void {
    this.isEditMode = true;
    this.selectedStoreId = store.id;

    this.storeService.getById(store.id)
      .subscribe({
        next: (data) => {
          this.storeForm.patchValue({
            name: data.name,
            code: data.code,
            locationId: data.locationId,
            description: data.description,
            address: data.address,
            city: data.city,
            state: data.state,
            country: data.country,
            postalCode: data.postalCode,
            phone: data.phone,
            email: data.email,
            contactPerson: data.contactPerson,
            isSalesPoint: data.isSalesPoint
          });
        },
        error: (error) => {
          console.error('Error loading store details:', error);
          this.snackBar.open('Failed to load store details', 'Close', { duration: 3000 });
        }
      });
  }
  
  deleteStore(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: { title: 'Confirm Delete', message: 'Are you sure you want to delete this store?' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.storeService.delete(id)
          .subscribe({
            next: () => {
              this.snackBar.open('Store deleted successfully', 'Close', { duration: 3000 });
              this.loadStores();
            },
            error: (error) => {
              console.error('Error deleting store:', error);
              this.snackBar.open('Failed to delete store', 'Close', { duration: 3000 });
            }
          });
      }
    });
  }
  
  toggleStatus(store: StoreList): void {
    const action = store.isActive ? 'deactivate' : 'activate';
    const method = store.isActive ? this.storeService.deactivate(store.id) : this.storeService.activate(store.id);

    method.subscribe({
      next: () => {
        this.snackBar.open(`Store ${action}d successfully`, 'Close', { duration: 3000 });
        this.loadStores();
      },
      error: (error) => {
        console.error(`Error ${action}ing store:`, error);
        this.snackBar.open(`Failed to ${action} store`, 'Close', { duration: 3000 });
      }
    });
  }
  
  resetForm(): void {
    this.storeForm.reset();
    this.isEditMode = false;
    this.selectedStoreId = null;

    // Check if we have a locationId in the query params
    this.route.queryParams.subscribe(params => {
      const locationId = params['locationId'];
      if (locationId) {
        this.storeForm.patchValue({ locationId: +locationId });
      }
    });
  }
  
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
  }
  
  onSortChange(sort: Sort): void {
    // Implement sorting logic if needed
  }
  
  getLocationName(locationId: number): string {
    const location = this.locations.find(l => l.id === locationId);
    return location ? location.name : '';
  }

  manageCostCenters(store: StoreList): void {
    // Navigate to a new page or open a dialog to manage cost centers for this store
    this.router.navigate(['/configuration/cost-centers'], {
      queryParams: { storeId: store.id, storeName: store.name }
    });
  }
}
