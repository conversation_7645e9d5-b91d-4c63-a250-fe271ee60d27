import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Store, StoreList, CreateStore, UpdateStore } from '../models/store.model';

@Injectable({
  providedIn: 'root'
})
export class StoreService {
  private readonly path = 'stores';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<StoreList[]> {
    return this.apiService.get<StoreList[]>(this.path);
  }

  getAllStores(): Observable<Store[]> {
    return this.apiService.get<Store[]>(this.path);
  }

  getById(id: number): Observable<Store> {
    return this.apiService.get<Store>(`${this.path}/${id}`);
  }

  getStoreById(id: number): Observable<Store> {
    return this.getById(id);
  }

  getByLocation(locationId: number): Observable<StoreList[]> {
    return this.apiService.get<StoreList[]>(`${this.path}/location/${locationId}`);
  }

  getByCompany(companyId: number): Observable<StoreList[]> {
    return this.apiService.get<StoreList[]>(`${this.path}/company/${companyId}`);
  }

  create(store: CreateStore): Observable<Store> {
    return this.apiService.post<Store, CreateStore>(this.path, store);
  }

  createStore(store: CreateStore): Observable<Store> {
    return this.create(store);
  }

  update(id: number, store: UpdateStore): Observable<void> {
    return this.apiService.put<void, UpdateStore>(`${this.path}/${id}`, store);
  }

  updateStore(id: number, store: UpdateStore): Observable<void> {
    return this.update(id, store);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  deleteStore(id: number): Observable<void> {
    return this.delete(id);
  }

  activate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/activate`, {});
  }

  deactivate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/deactivate`, {});
  }
}
