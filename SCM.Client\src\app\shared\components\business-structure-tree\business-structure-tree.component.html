<div class="business-tree-container">
  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="30"></mat-spinner>
  </div>

  <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="business-tree">
    <!-- Tree node template -->
    <mat-nested-tree-node *matTreeNodeDef="let node">
      <div class="mat-tree-node" 
           [class.active]="isSelected(node)"
           [class.inactive]="!node.isActive">
        <button mat-icon-button 
                *ngIf="hasChild(0, node)"
                [attr.aria-label]="'Toggle ' + node.name"
                (click)="toggleNode(node); $event.stopPropagation()">
          <mat-icon class="mat-icon-rtl-mirror">
            {{treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
          </mat-icon>
        </button>
        <button mat-icon-button disabled *ngIf="!hasChild(0, node)">
          <mat-icon class="mat-icon-rtl-mirror">radio_button_unchecked</mat-icon>
        </button>
        
        <div class="node-content" (click)="selectNode(node)">
          <mat-icon *ngIf="showIcons" class="node-icon">{{getNodeIcon(node)}}</mat-icon>
          <span class="node-label">{{node.name}}</span>
          <mat-spinner *ngIf="node.isLoading" diameter="16" class="node-spinner"></mat-spinner>
        </div>
      </div>
      
      <!-- Children -->
      <div [class.tree-invisible]="!treeControl.isExpanded(node)" role="group">
        <ng-container matTreeNodeOutlet></ng-container>
      </div>
    </mat-nested-tree-node>
  </mat-tree>

  <div *ngIf="!isLoading && dataSource.data.length === 0" class="empty-state">
    <p>No business structure data available</p>
  </div>
</div>
