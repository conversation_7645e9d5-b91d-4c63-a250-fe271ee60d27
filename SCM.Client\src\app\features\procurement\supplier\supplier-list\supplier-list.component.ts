import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatTableModule, MatTable } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SupplierService, SupplierListItem } from '../../../../core/services/supplier.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-supplier-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './supplier-list.component.html',
  styleUrls: ['./supplier-list.component.scss']
})
export class SupplierListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatTable) table!: MatTable<SupplierListItem>;
  
  displayedColumns: string[] = [
    'name', 
    'contactPerson', 
    'phone', 
    'email', 
    'taxNumber', 
    'status', 
    'actions'
  ];
  
  suppliers: SupplierListItem[] = [];
  filteredSuppliers: SupplierListItem[] = [];
  isLoading = false;
  searchTerm = '';
  showInactive = false;

  constructor(
    private supplierService: SupplierService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.loadSuppliers();
  }

  loadSuppliers(): void {
    this.isLoading = true;
    this.supplierService.getAllSuppliers()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.suppliers = data;
          this.applyFilter();
        },
        error: (error) => {
          console.error('Error loading suppliers', error);
          this.snackBar.open('Error loading suppliers', 'Close', {
            duration: 3000
          });
        }
      });
  }

  applyFilter(): void {
    this.filteredSuppliers = this.suppliers.filter(supplier => {
      const matchesSearch = this.searchTerm === '' || 
        supplier.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (supplier.contactPerson && supplier.contactPerson.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
        (supplier.email && supplier.email.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
        (supplier.phone && supplier.phone.toLowerCase().includes(this.searchTerm.toLowerCase()));
      
      const matchesStatus = this.showInactive || supplier.isActive;
      
      return matchesSearch && matchesStatus;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.showInactive = false;
    this.applyFilter();
  }

  addSupplier(): void {
    this.router.navigate(['/procurement/suppliers/new']);
  }

  editSupplier(supplier: SupplierListItem): void {
    this.router.navigate(['/procurement/suppliers', supplier.id]);
  }

  viewSupplier(supplier: SupplierListItem): void {
    this.router.navigate(['/procurement/suppliers', supplier.id]);
  }

  deleteSupplier(supplier: SupplierListItem): void {
    if (confirm(`Are you sure you want to delete supplier ${supplier.name}?`)) {
      this.isLoading = true;
      this.supplierService.deleteSupplier(supplier.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Supplier deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadSuppliers();
          },
          error: (error) => {
            console.error('Error deleting supplier', error);
            this.snackBar.open('Error deleting supplier: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  toggleStatus(supplier: SupplierListItem): void {
    const action = supplier.isActive ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to ${action} supplier ${supplier.name}?`)) {
      this.isLoading = true;
      
      const updatedSupplier = {
        id: supplier.id,
        name: supplier.name,
        contactPerson: supplier.contactPerson,
        phone: supplier.phone,
        email: supplier.email,
        taxNumber: supplier.taxNumber,
        isActive: !supplier.isActive
      };
      
      this.supplierService.updateSupplier(supplier.id, updatedSupplier)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open(`Supplier ${action}d successfully`, 'Close', {
              duration: 3000
            });
            this.loadSuppliers();
          },
          error: (error) => {
            console.error(`Error ${action}ing supplier`, error);
            this.snackBar.open(`Error ${action}ing supplier: ` + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }
}
