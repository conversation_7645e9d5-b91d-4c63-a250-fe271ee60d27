import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Location, LocationList, CreateLocation, UpdateLocation } from '../models/location.model';

@Injectable({
  providedIn: 'root'
})
export class LocationService {
  private readonly path = 'locations';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<LocationList[]> {
    return this.apiService.get<LocationList[]>(this.path);
  }

  getById(id: number): Observable<Location> {
    return this.apiService.get<Location>(`${this.path}/${id}`);
  }

  getByCompany(companyId: number): Observable<LocationList[]> {
    return this.apiService.get<LocationList[]>(`${this.path}/company/${companyId}`);
  }

  create(location: CreateLocation): Observable<Location> {
    return this.apiService.post<Location, CreateLocation>(this.path, location);
  }

  update(id: number, location: UpdateLocation): Observable<void> {
    return this.apiService.put<void, UpdateLocation>(`${this.path}/${id}`, location);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  activate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/activate`, {});
  }

  deactivate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/deactivate`, {});
  }
}
