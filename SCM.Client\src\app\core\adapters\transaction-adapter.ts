import { TransactionHeader, TransactionDetail } from '../models/transaction.model';
import { PurchaseOrder, PurchaseOrderDetail, PurchaseOrderListItem } from '../models/purchase-order.model';

/**
 * Adapter class to convert between TransactionHeader and PurchaseOrder models
 */
export class TransactionAdapter {
  /**
   * Convert a TransactionHeader to a PurchaseOrder
   */
  static toPurchaseOrder(transaction: TransactionHeader): PurchaseOrder {
    // Use the totalAmount from the transaction if available, otherwise calculate it
    let totalAmount = transaction.totalAmount || 0;

    // If totalAmount is 0 and we have details, calculate it as a fallback
    if (totalAmount === 0 && transaction.details && transaction.details.length > 0) {
      totalAmount = transaction.details.reduce((sum, detail) => {
        return sum + (detail.quantity * (detail.unitPrice || 0));
      }, 0);
    }

    return {
      id: transaction.id,
      documentNumber: transaction.transactionNumber || transaction.referenceNumber,
      supplierId: transaction.supplierId || 0,
      supplierName: transaction.supplierName,
      costCenterId: transaction.sourceCostCenterId,
      costCenterName: transaction.sourceCostCenterName,
      orderDate: transaction.transactionDate,
      expectedDeliveryDate: transaction.requiredDate,
      status: transaction.status,
      notes: transaction.notes,
      totalAmount: totalAmount,
      createdBy: transaction.createdByName,
      createdAt: transaction.createdAt,
      updatedBy: transaction.approvedByName,
      updatedAt: transaction.approvedAt,
      details: transaction.details ? transaction.details.map(this.toPurchaseOrderDetail) : []
    };
  }

  /**
   * Convert a TransactionDetail to a PurchaseOrderDetail
   */
  static toPurchaseOrderDetail(detail: TransactionDetail): PurchaseOrderDetail {
    // Calculate the total price as quantity * unitPrice
    const totalPrice = detail.quantity * (detail.unitPrice || 0);

    return {
      id: detail.id,
      purchaseOrderId: detail.transactionHeaderId,
      productId: detail.productId,
      productCode: detail.productCode,
      productName: detail.productName,
      unitId: detail.unitId,
      unitName: detail.unitName,
      quantity: detail.quantity,
      unitPrice: detail.unitPrice || 0,
      totalPrice: totalPrice,
      notes: detail.notes
    };
  }

  /**
   * Convert a TransactionHeader to a PurchaseOrderListItem
   */
  static toPurchaseOrderListItem(transaction: TransactionHeader): PurchaseOrderListItem {
    // Use the totalAmount from the transaction if available, otherwise calculate it
    let totalAmount = transaction.totalAmount || 0;

    // If totalAmount is 0 and we have details, calculate it as a fallback
    if (totalAmount === 0 && transaction.details && transaction.details.length > 0) {
      totalAmount = transaction.details.reduce((sum, detail) => {
        return sum + (detail.quantity * (detail.unitPrice || 0));
      }, 0);
    }

    // Use the transaction number directly from the database
    // Only generate a fallback if it's not available
    let documentNumber = transaction.transactionNumber || '';

    // If no transaction number, use reference number or generate one from the ID
    if (!documentNumber) {
      documentNumber = transaction.referenceNumber ||
                      (transaction.id ? `PO-${transaction.id.toString().padStart(5, '0')}` : 'PO-XXXXX');
    }

    return {
      id: transaction.id,
      documentNumber: documentNumber,
      supplierName: transaction.supplierName || 'Unknown Supplier',
      costCenterName: transaction.sourceCostCenterName || 'N/A',
      orderDate: transaction.transactionDate,
      expectedDeliveryDate: transaction.requiredDate,
      status: transaction.status,
      totalAmount: totalAmount
    };
  }
}
