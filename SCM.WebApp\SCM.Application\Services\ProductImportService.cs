using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using SCM.Application.DTOs;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public interface IProductImportService
{
    Task<ProductImportDto> ImportProductsFromExcelAsync(Stream excelStream);
    Task<byte[]> GenerateTemplateAsync();
}

public class ProductImportService : IProductImportService
{
    private readonly ApplicationDbContext _dbContext;

    public ProductImportService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public async Task<ProductImportDto> ImportProductsFromExcelAsync(Stream excelStream)
    {
        var result = new ProductImportDto();
        
        try
        {
            using var package = new ExcelPackage(excelStream);
            var worksheet = package.Workbook.Worksheets[0];
            
            if (worksheet == null)
            {
                throw new InvalidOperationException("Excel file must contain at least one worksheet");
            }

            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount <= 1)
            {
                throw new InvalidOperationException("Excel file must contain data rows");
            }

            result.TotalRows = rowCount - 1; // Exclude header row

            // Load lookup data
            var brands = await _dbContext.Brands.ToDictionaryAsync(b => b.Name.ToLower(), b => b.Id);
            var units = await _dbContext.Units.ToDictionaryAsync(u => u.Name.ToLower(), u => u.Id);
            var departments = await _dbContext.Departments.ToDictionaryAsync(d => d.Name.ToLower(), d => d.Id);
            var groups = await _dbContext.ProductGroups.ToDictionaryAsync(g => g.Name.ToLower(), g => g.Id);
            var subGroups = await _dbContext.ProductSubGroups.ToDictionaryAsync(sg => sg.Name.ToLower(), sg => sg.Id);
            var taxes = await _dbContext.Taxes.ToDictionaryAsync(t => t.Name.ToLower(), t => t.Id);

            // Process each row
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var product = await ProcessRowAsync(worksheet, row, brands, units, departments, groups, subGroups, taxes);
                    if (product != null)
                    {
                        _dbContext.Products.Add(product);
                        result.SuccessfulProducts.Add($"{product.Code} - {product.Name}");
                        result.SuccessCount++;
                    }
                }
                catch (Exception ex)
                {
                    result.Errors.Add(new ProductImportError
                    {
                        RowNumber = row,
                        ProductCode = GetCellValue(worksheet, row, 1) ?? "",
                        ProductName = GetCellValue(worksheet, row, 2) ?? "",
                        ErrorMessage = ex.Message
                    });
                    result.ErrorCount++;
                }
            }

            if (result.SuccessCount > 0)
            {
                await _dbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            result.Errors.Add(new ProductImportError
            {
                RowNumber = 0,
                ErrorMessage = $"File processing error: {ex.Message}"
            });
            result.ErrorCount++;
        }

        return result;
    }

    private async Task<Product?> ProcessRowAsync(
        ExcelWorksheet worksheet, 
        int row,
        Dictionary<string, int> brands,
        Dictionary<string, int> units,
        Dictionary<string, int> departments,
        Dictionary<string, int> groups,
        Dictionary<string, int> subGroups,
        Dictionary<string, int> taxes)
    {
        var code = GetCellValue(worksheet, row, 1);
        var name = GetCellValue(worksheet, row, 2);

        if (string.IsNullOrWhiteSpace(code) || string.IsNullOrWhiteSpace(name))
        {
            throw new InvalidOperationException("Product code and name are required");
        }

        // Check if product already exists
        var existingProduct = await _dbContext.Products.FirstOrDefaultAsync(p => p.Code == code);
        if (existingProduct != null)
        {
            throw new InvalidOperationException($"Product with code '{code}' already exists");
        }

        var product = new Product
        {
            Code = code,
            Name = name,
            CostPrice = GetDecimalValue(worksheet, row, 8),
            SalesPrice = GetDecimalValue(worksheet, row, 9),
            MinStock = GetDecimalValue(worksheet, row, 10),
            MaxStock = GetDecimalValue(worksheet, row, 11),
            ReorderPoint = GetDecimalValue(worksheet, row, 12),
            Notes = GetCellValue(worksheet, row, 13),
            IsStockItem = GetBooleanValue(worksheet, row, 14, true),
            IsRecipe = GetBooleanValue(worksheet, row, 15, false),
            HasExpiry = GetBooleanValue(worksheet, row, 16, false),
            IsProduction = GetBooleanValue(worksheet, row, 17, false),
            IsSaleable = GetBooleanValue(worksheet, row, 18, true),
            SalesUnitConversionFactor = GetDecimalValue(worksheet, row, 21),
            AllowDiscount = GetBooleanValue(worksheet, row, 22, true),
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        // Set foreign key relationships
        var brandName = GetCellValue(worksheet, row, 3);
        if (!string.IsNullOrWhiteSpace(brandName) && brands.TryGetValue(brandName.ToLower(), out var brandId))
        {
            product.BrandId = brandId;
        }

        var unitName = GetCellValue(worksheet, row, 4);
        if (!string.IsNullOrWhiteSpace(unitName) && units.TryGetValue(unitName.ToLower(), out var unitId))
        {
            product.UnitId = unitId;
        }

        var departmentName = GetCellValue(worksheet, row, 5);
        if (!string.IsNullOrWhiteSpace(departmentName) && departments.TryGetValue(departmentName.ToLower(), out var departmentId))
        {
            product.DepartmentId = departmentId;
        }

        var groupName = GetCellValue(worksheet, row, 6);
        if (!string.IsNullOrWhiteSpace(groupName) && groups.TryGetValue(groupName.ToLower(), out var groupId))
        {
            product.GroupId = groupId;
        }

        var subGroupName = GetCellValue(worksheet, row, 7);
        if (!string.IsNullOrWhiteSpace(subGroupName) && subGroups.TryGetValue(subGroupName.ToLower(), out var subGroupId))
        {
            product.SubGroupId = subGroupId;
        }

        var taxName = GetCellValue(worksheet, row, 19);
        if (!string.IsNullOrWhiteSpace(taxName) && taxes.TryGetValue(taxName.ToLower(), out var taxId))
        {
            product.TaxId = taxId;
        }

        var salesUnitName = GetCellValue(worksheet, row, 20);
        if (!string.IsNullOrWhiteSpace(salesUnitName) && units.TryGetValue(salesUnitName.ToLower(), out var salesUnitId))
        {
            product.SalesUnitId = salesUnitId;
        }

        return product;
    }

    public async Task<byte[]> GenerateTemplateAsync()
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Product Import Template");

        // Add headers
        var headers = new[]
        {
            "Code*", "Name*", "Brand", "Unit", "Department", "Group", "SubGroup",
            "Cost Price", "Sales Price", "Min Stock", "Max Stock", "Reorder Point",
            "Notes", "Is Stock Item", "Is Recipe", "Has Expiry", "Is Production",
            "Is Saleable", "Tax", "Sales Unit", "Sales Unit Conversion Factor", "Allow Discount"
        };

        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
        }

        // Add sample data
        worksheet.Cells[2, 1].Value = "PROD001";
        worksheet.Cells[2, 2].Value = "Sample Product";
        worksheet.Cells[2, 3].Value = "Sample Brand";
        worksheet.Cells[2, 4].Value = "Piece";
        worksheet.Cells[2, 5].Value = "General";
        worksheet.Cells[2, 8].Value = 10.00;
        worksheet.Cells[2, 9].Value = 15.00;
        worksheet.Cells[2, 10].Value = 5;
        worksheet.Cells[2, 11].Value = 100;
        worksheet.Cells[2, 12].Value = 10;
        worksheet.Cells[2, 14].Value = "TRUE";
        worksheet.Cells[2, 18].Value = "TRUE";
        worksheet.Cells[2, 22].Value = "TRUE";

        // Auto-fit columns
        worksheet.Cells.AutoFitColumns();

        return package.GetAsByteArray();
    }

    private string? GetCellValue(ExcelWorksheet worksheet, int row, int col)
    {
        return worksheet.Cells[row, col].Value?.ToString()?.Trim();
    }

    private decimal? GetDecimalValue(ExcelWorksheet worksheet, int row, int col)
    {
        var value = GetCellValue(worksheet, row, col);
        if (string.IsNullOrWhiteSpace(value))
            return null;

        if (decimal.TryParse(value, out var result))
            return result;

        return null;
    }

    private bool GetBooleanValue(ExcelWorksheet worksheet, int row, int col, bool defaultValue)
    {
        var value = GetCellValue(worksheet, row, col);
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        return value.ToLower() is "true" or "yes" or "1";
    }
}
