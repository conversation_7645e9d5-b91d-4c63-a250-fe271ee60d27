.dialog-container {
  min-width: 800px;
  max-width: 1200px;
  padding: 0;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f5f5f5;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
}

mat-dialog-content {
  padding: 24px;
  max-height: 70vh;
}

.stock-take-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e0e0e0;

  .info-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    display: flex;
    flex-direction: column;

    &.full-width {
      flex: 2;
    }

    .label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .value {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;

  &.status-draft {
    background-color: #e0e0e0;
    color: #616161;
  }

  &.status-in-progress {
    background-color: #bbdefb;
    color: #1976d2;
  }

  &.status-completed {
    background-color: #c8e6c9;
    color: #388e3c;
  }

  &.status-cancelled {
    background-color: #ffcdd2;
    color: #d32f2f;
  }
}

.variance-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;

  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .value {
      font-size: 16px;
      font-weight: 500;

      &.positive {
        color: #388e3c;
      }

      &.negative {
        color: #d32f2f;
      }
    }
  }
}

.table-container {
  margin-bottom: 24px;
  max-height: 400px;
  overflow: auto;

  table {
    width: 100%;
  }

  .mat-column-productCode {
    width: 120px;
  }

  .mat-column-systemQuantity,
  .mat-column-actualQuantity,
  .mat-column-variance {
    width: 100px;
    text-align: right;
  }

  .negative-variance {
    color: #d32f2f;
  }

  .positive-variance {
    color: #388e3c;
  }
}

.full-width {
  width: 100%;
}

mat-dialog-actions {
  padding: 8px 24px 24px;
  border-top: 1px solid #e0e0e0;
}
