using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class CostCentersController : ApiControllerBase
{
    private readonly ICostCenterService _costCenterService;
    private readonly ILogger<CostCentersController> _logger;

    public CostCentersController(ICostCenterService costCenterService, ILogger<CostCentersController> logger)
    {
        _costCenterService = costCenterService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<CostCenterDto>>> GetAll()
    {
        try
        {
            var costCenters = await _costCenterService.GetAllCostCentersAsync();
            return Ok(costCenters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cost centers");
            return StatusCode(500, "An error occurred while retrieving cost centers");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<CostCenterDto>> GetById(int id)
    {
        try
        {
            var costCenter = await _costCenterService.GetCostCenterByIdAsync(id);
            if (costCenter == null)
                return NotFound();

            return Ok(costCenter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cost center with ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the cost center. Please try again later.");
        }
    }

    [HttpGet("store/{storeId}")]
    public async Task<ActionResult<IEnumerable<CostCenterDto>>> GetByStoreId(int storeId)
    {
        try
        {
            var costCenters = await _costCenterService.GetCostCentersByStoreIdAsync(storeId);
            return Ok(costCenters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cost centers by store ID {StoreId}", storeId);
            return StatusCode(500, "An error occurred while retrieving cost centers");
        }
    }

    [HttpGet("location/{locationId}")]
    public async Task<ActionResult<IEnumerable<CostCenterDto>>> GetByLocationId(int locationId)
    {
        try
        {
            var costCenters = await _costCenterService.GetCostCentersByLocationIdAsync(locationId);
            return Ok(costCenters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cost centers by location ID {LocationId}", locationId);
            return StatusCode(500, "An error occurred while retrieving cost centers");
        }
    }

    [HttpGet("type/{typeId}")]
    public async Task<ActionResult<IEnumerable<CostCenterDto>>> GetByTypeId(int typeId)
    {
        try
        {
            var costCenters = await _costCenterService.GetCostCentersByTypeIdAsync(typeId);
            return Ok(costCenters);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving cost centers. Please try again later.");
        }
    }

    [HttpGet("sales-points")]
    public async Task<ActionResult<IEnumerable<CostCenterDto>>> GetSalesPoints()
    {
        try
        {
            var costCenters = await _costCenterService.GetSalesPointsAsync();
            return Ok(costCenters);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving sales points. Please try again later.");
        }
    }

    [HttpPost]
    public async Task<ActionResult<CostCenterDto>> Create(CreateCostCenterDto createCostCenterDto)
    {
        try
        {
            var costCenter = await _costCenterService.CreateCostCenterAsync(createCostCenterDto);
            return CreatedAtAction(nameof(GetById), new { id = costCenter.Id }, costCenter);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while creating the cost center. Please try again later.");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateCostCenterDto updateCostCenterDto)
    {
        if (id != updateCostCenterDto.Id)
            return BadRequest();

        try
        {
            await _costCenterService.UpdateCostCenterAsync(updateCostCenterDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while updating the cost center. Please try again later.");
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _costCenterService.DeleteCostCenterAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while deleting the cost center. Please try again later.");
        }

        return NoContent();
    }

    [HttpPut("{id}/activate")]
    public async Task<IActionResult> Activate(int id)
    {
        try
        {
            var costCenter = await _costCenterService.GetCostCenterByIdAsync(id);
            if (costCenter == null)
                return NotFound();

            var updateDto = new UpdateCostCenterDto
            {
                Id = costCenter.Id,
                Name = costCenter.Name,
                StoreId = costCenter.StoreId,
                TypeId = costCenter.TypeId,
                AutoTransfer = costCenter.AutoTransfer,
                IsSalesPoint = costCenter.IsSalesPoint,
                Abbreviation = costCenter.Abbreviation,
                IsActive = true
            };

            await _costCenterService.UpdateCostCenterAsync(updateDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while activating the cost center. Please try again later.");
        }
    }

    [HttpPut("{id}/deactivate")]
    public async Task<IActionResult> Deactivate(int id)
    {
        try
        {
            var costCenter = await _costCenterService.GetCostCenterByIdAsync(id);
            if (costCenter == null)
                return NotFound();

            var updateDto = new UpdateCostCenterDto
            {
                Id = costCenter.Id,
                Name = costCenter.Name,
                StoreId = costCenter.StoreId,
                TypeId = costCenter.TypeId,
                AutoTransfer = costCenter.AutoTransfer,
                IsSalesPoint = costCenter.IsSalesPoint,
                Abbreviation = costCenter.Abbreviation,
                IsActive = false
            };

            await _costCenterService.UpdateCostCenterAsync(updateDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while deactivating the cost center. Please try again later.");
        }
    }
}
