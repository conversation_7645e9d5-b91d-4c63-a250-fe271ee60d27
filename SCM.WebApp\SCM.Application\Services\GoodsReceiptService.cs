using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class GoodsReceiptService : IGoodsReceiptService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IStockService _stockService;

    public GoodsReceiptService(
        ApplicationDbContext dbContext,
        IMapper mapper,
        IStockService stockService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _stockService = stockService;
    }

    public async Task<IEnumerable<GoodsReceiptListDto>> GetAllGoodsReceiptsAsync()
    {
        var goodsReceipts = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Supplier)
            .Include(gr => gr.CostCenter)
            .Include(gr => gr.PurchaseOrder)
            .Include(gr => gr.ReceivedBy)
            .OrderByDescending(gr => gr.ReceiptDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<GoodsReceiptListDto>>(goodsReceipts);
    }

    public async Task<GoodsReceiptHeaderDto?> GetGoodsReceiptByIdAsync(int id)
    {
        var goodsReceipt = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Supplier)
            .Include(gr => gr.CostCenter)
            .Include(gr => gr.PurchaseOrder)
            .Include(gr => gr.ReceivedBy)
            .Include(gr => gr.ApprovedBy)
            .Include(gr => gr.Details)
            .FirstOrDefaultAsync(gr => gr.Id == id);

        if (goodsReceipt == null)
            return null;

        // Load related data for goods receipt details
        foreach (var detail in goodsReceipt.Details)
        {
            // Load product information
            if (detail.ProductId > 0)
            {
                detail.Product = await _dbContext.Products.FindAsync(detail.ProductId);
            }

            // Load batch information
            if (detail.BatchId.HasValue)
            {
                detail.Batch = await _dbContext.Batches.FindAsync(detail.BatchId.Value);
            }

            // Load unit information
            if (detail.UnitId.HasValue)
            {
                detail.Unit = await _dbContext.Units.FindAsync(detail.UnitId.Value);
            }
        }

        return _mapper.Map<GoodsReceiptHeaderDto>(goodsReceipt);
    }

    public async Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByPurchaseOrderIdAsync(int purchaseOrderId)
    {
        var goodsReceipts = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Supplier)
            .Include(gr => gr.CostCenter)
            .Include(gr => gr.PurchaseOrder)
            .Include(gr => gr.ReceivedBy)
            .Where(gr => gr.PurchaseOrderId == purchaseOrderId)
            .OrderByDescending(gr => gr.ReceiptDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<GoodsReceiptListDto>>(goodsReceipts);
    }

    public async Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsBySupplierIdAsync(int supplierId)
    {
        var goodsReceipts = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Supplier)
            .Include(gr => gr.CostCenter)
            .Include(gr => gr.PurchaseOrder)
            .Include(gr => gr.ReceivedBy)
            .Where(gr => gr.SupplierId == supplierId)
            .OrderByDescending(gr => gr.ReceiptDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<GoodsReceiptListDto>>(goodsReceipts);
    }

    public async Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByCostCenterIdAsync(int costCenterId)
    {
        var goodsReceipts = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Supplier)
            .Include(gr => gr.CostCenter)
            .Include(gr => gr.PurchaseOrder)
            .Include(gr => gr.ReceivedBy)
            .Where(gr => gr.CostCenterId == costCenterId)
            .OrderByDescending(gr => gr.ReceiptDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<GoodsReceiptListDto>>(goodsReceipts);
    }

    public async Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByStatusAsync(string status)
    {
        var goodsReceipts = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Supplier)
            .Include(gr => gr.CostCenter)
            .Include(gr => gr.PurchaseOrder)
            .Include(gr => gr.ReceivedBy)
            .Where(gr => gr.Status == status)
            .OrderByDescending(gr => gr.ReceiptDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<GoodsReceiptListDto>>(goodsReceipts);
    }

    public async Task<GoodsReceiptHeaderDto> CreateGoodsReceiptAsync(CreateGoodsReceiptHeaderDto createGoodsReceiptHeaderDto)
    {
        // Generate document number
        var documentNumber = await GenerateDocumentNumberAsync();
        
        var goodsReceipt = _mapper.Map<GoodsReceiptHeader>(createGoodsReceiptHeaderDto);
        goodsReceipt.DocumentNumber = documentNumber;
        goodsReceipt.Status = "Draft";
        goodsReceipt.ReceivedById = 1; // TODO: Get from current user
        
        // If this is linked to a purchase order, get the purchase order details
        if (createGoodsReceiptHeaderDto.PurchaseOrderId.HasValue)
        {
            var purchaseOrder = await _dbContext.PurchaseOrders
                .FirstOrDefaultAsync(po => po.Id == createGoodsReceiptHeaderDto.PurchaseOrderId.Value);
                
            if (purchaseOrder == null)
                throw new KeyNotFoundException($"Purchase Order with ID {createGoodsReceiptHeaderDto.PurchaseOrderId.Value} not found.");
                
            // Update purchase order status if it's not already completed
            if (purchaseOrder.Status != "Completed")
            {
                purchaseOrder.Status = "Receiving";
                _dbContext.PurchaseOrders.Update(purchaseOrder);
            }
        }
        
        _dbContext.GoodsReceiptHeaders.Add(goodsReceipt);
        await _dbContext.SaveChangesAsync();
        
        // Add details if provided
        if (createGoodsReceiptHeaderDto.Details != null && createGoodsReceiptHeaderDto.Details.Any())
        {
            foreach (var detailDto in createGoodsReceiptHeaderDto.Details)
            {
                await CreateGoodsReceiptDetailAsync(goodsReceipt.Id, detailDto);
            }
        }
        
        // Calculate total amount
        await UpdateGoodsReceiptTotalAsync(goodsReceipt.Id);
        
        var createdGoodsReceipt = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Supplier)
            .Include(gr => gr.CostCenter)
            .Include(gr => gr.PurchaseOrder)
            .Include(gr => gr.ReceivedBy)
            .Include(gr => gr.Details)
            .FirstOrDefaultAsync(gr => gr.Id == goodsReceipt.Id);

        if (createdGoodsReceipt != null)
        {
            // Load related data for goods receipt details
            foreach (var detail in createdGoodsReceipt.Details)
            {
                // Load product information
                if (detail.ProductId > 0)
                {
                    detail.Product = await _dbContext.Products.FindAsync(detail.ProductId);
                }

                // Load batch information
                if (detail.BatchId.HasValue)
                {
                    detail.Batch = await _dbContext.Batches.FindAsync(detail.BatchId.Value);
                }

                // Load unit information
                if (detail.UnitId.HasValue)
                {
                    detail.Unit = await _dbContext.Units.FindAsync(detail.UnitId.Value);
                }
            }
        }

        return _mapper.Map<GoodsReceiptHeaderDto>(createdGoodsReceipt);
    }

    public async Task UpdateGoodsReceiptAsync(UpdateGoodsReceiptHeaderDto updateGoodsReceiptHeaderDto)
    {
        var goodsReceipt = await _dbContext.GoodsReceiptHeaders
            .FirstOrDefaultAsync(gr => gr.Id == updateGoodsReceiptHeaderDto.Id);
            
        if (goodsReceipt == null)
            throw new KeyNotFoundException($"Goods Receipt with ID {updateGoodsReceiptHeaderDto.Id} not found.");
            
        if (goodsReceipt.Status != "Draft")
            throw new InvalidOperationException("Only goods receipts in Draft status can be updated.");
            
        _mapper.Map(updateGoodsReceiptHeaderDto, goodsReceipt);
        
        _dbContext.GoodsReceiptHeaders.Update(goodsReceipt);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteGoodsReceiptAsync(int id)
    {
        var goodsReceipt = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Details)
            .FirstOrDefaultAsync(gr => gr.Id == id);
            
        if (goodsReceipt == null)
            throw new KeyNotFoundException($"Goods Receipt with ID {id} not found.");
            
        if (goodsReceipt.Status != "Draft")
            throw new InvalidOperationException("Only goods receipts in Draft status can be deleted.");
            
        // Remove details first
        _dbContext.GoodsReceiptDetails.RemoveRange(goodsReceipt.Details);
        
        // Then remove header
        _dbContext.GoodsReceiptHeaders.Remove(goodsReceipt);
        
        await _dbContext.SaveChangesAsync();
    }

    public async Task CompleteGoodsReceiptAsync(CompleteGoodsReceiptDto completeGoodsReceiptDto)
    {
        var goodsReceipt = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Details)
            .Include(gr => gr.PurchaseOrder)
            .FirstOrDefaultAsync(gr => gr.Id == completeGoodsReceiptDto.Id);

        if (goodsReceipt != null)
        {
            // Load related data for goods receipt details
            foreach (var detail in goodsReceipt.Details)
            {
                // Load product information
                if (detail.ProductId > 0)
                {
                    detail.Product = await _dbContext.Products.FindAsync(detail.ProductId);
                }

                // Load batch information
                if (detail.BatchId.HasValue)
                {
                    detail.Batch = await _dbContext.Batches.FindAsync(detail.BatchId.Value);
                }
            }
        }
            
        if (goodsReceipt == null)
            throw new KeyNotFoundException($"Goods Receipt with ID {completeGoodsReceiptDto.Id} not found.");
            
        if (goodsReceipt.Status != "Draft")
            throw new InvalidOperationException("Only goods receipts in Draft status can be completed.");
            
        if (!goodsReceipt.Details.Any())
            throw new InvalidOperationException("Cannot complete a goods receipt with no details.");
            
        // Process the goods receipt by updating stock levels
        foreach (var detail in goodsReceipt.Details)
        {
            // Create or update batch if needed
            if (detail.ExpiryDate.HasValue && detail.BatchId == null)
            {
                var batch = new Batch
                {
                    ProductId = detail.ProductId,
                    BatchNumber = $"GR-{goodsReceipt.DocumentNumber}-{detail.Id}",
                    ExpiryDate = detail.ExpiryDate.Value,
                    IsActive = true
                };
                
                _dbContext.Batches.Add(batch);
                await _dbContext.SaveChangesAsync();
                
                detail.BatchId = batch.Id;
                _dbContext.GoodsReceiptDetails.Update(detail);
            }
            
            // Add stock
            await _stockService.AddStockAsync(new StockAddDto
            {
                ProductId = detail.ProductId,
                CostCenterId = goodsReceipt.CostCenterId,
                BatchId = detail.BatchId,
                UnitId = detail.UnitId,
                Quantity = detail.ReceivedQuantity,
                CostPrice = detail.UnitPrice,
                ReferenceNumber = goodsReceipt.DocumentNumber,
                ReferenceType = "GoodsReceipt",
                Notes = $"Goods Receipt #{goodsReceipt.DocumentNumber}"
            });
        }
        
        // Update purchase order status if this is linked to a purchase order
        if (goodsReceipt.PurchaseOrderId.HasValue && goodsReceipt.PurchaseOrder != null)
        {
            // Check if all items have been received
            var allItemsReceived = await CheckAllPurchaseOrderItemsReceivedAsync(goodsReceipt.PurchaseOrderId.Value);
            
            goodsReceipt.PurchaseOrder.Status = allItemsReceived ? "Completed" : "Partially Received";
            _dbContext.PurchaseOrders.Update(goodsReceipt.PurchaseOrder);
        }
        
        // Update goods receipt status
        goodsReceipt.Status = "Completed";
        goodsReceipt.ApprovedById = 1; // TODO: Get from current user
        goodsReceipt.ApprovedAt = DateTime.UtcNow;
        goodsReceipt.Notes = completeGoodsReceiptDto.Notes ?? goodsReceipt.Notes;
        
        _dbContext.GoodsReceiptHeaders.Update(goodsReceipt);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelGoodsReceiptAsync(int id)
    {
        var goodsReceipt = await _dbContext.GoodsReceiptHeaders
            .FirstOrDefaultAsync(gr => gr.Id == id);
            
        if (goodsReceipt == null)
            throw new KeyNotFoundException($"Goods Receipt with ID {id} not found.");
            
        if (goodsReceipt.Status != "Draft")
            throw new InvalidOperationException("Only goods receipts in Draft status can be cancelled.");
            
        goodsReceipt.Status = "Cancelled";
        
        _dbContext.GoodsReceiptHeaders.Update(goodsReceipt);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<GoodsReceiptDetailDto>> GetGoodsReceiptDetailsAsync(int goodsReceiptHeaderId)
    {
        var details = await _dbContext.GoodsReceiptDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .Where(d => d.GoodsReceiptHeaderId == goodsReceiptHeaderId)
            .OrderBy(d => d.Product.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<GoodsReceiptDetailDto>>(details);
    }

    public async Task<GoodsReceiptDetailDto?> GetGoodsReceiptDetailByIdAsync(int id)
    {
        var detail = await _dbContext.GoodsReceiptDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        return detail != null ? _mapper.Map<GoodsReceiptDetailDto>(detail) : null;
    }

    public async Task<GoodsReceiptDetailDto> CreateGoodsReceiptDetailAsync(int goodsReceiptHeaderId, CreateGoodsReceiptDetailDto createGoodsReceiptDetailDto)
    {
        var goodsReceipt = await _dbContext.GoodsReceiptHeaders
            .FirstOrDefaultAsync(gr => gr.Id == goodsReceiptHeaderId);
            
        if (goodsReceipt == null)
            throw new KeyNotFoundException($"Goods Receipt with ID {goodsReceiptHeaderId} not found.");
            
        if (goodsReceipt.Status != "Draft")
            throw new InvalidOperationException("Can only add details to goods receipts in Draft status.");
            
        // Verify product exists
        var product = await _dbContext.Products.FindAsync(createGoodsReceiptDetailDto.ProductId);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {createGoodsReceiptDetailDto.ProductId} not found.");
            
        var detail = _mapper.Map<GoodsReceiptDetail>(createGoodsReceiptDetailDto);
        detail.GoodsReceiptHeaderId = goodsReceiptHeaderId;
        detail.TotalPrice = detail.ReceivedQuantity * detail.UnitPrice;
        
        _dbContext.GoodsReceiptDetails.Add(detail);
        await _dbContext.SaveChangesAsync();
        
        // Update the total amount of the goods receipt
        await UpdateGoodsReceiptTotalAsync(goodsReceiptHeaderId);
        
        var createdDetail = await _dbContext.GoodsReceiptDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == detail.Id);
            
        return _mapper.Map<GoodsReceiptDetailDto>(createdDetail);
    }

    public async Task UpdateGoodsReceiptDetailAsync(UpdateGoodsReceiptDetailDto updateGoodsReceiptDetailDto)
    {
        var detail = await _dbContext.GoodsReceiptDetails
            .Include(d => d.GoodsReceiptHeader)
            .FirstOrDefaultAsync(d => d.Id == updateGoodsReceiptDetailDto.Id);
            
        if (detail == null)
            throw new KeyNotFoundException($"Goods Receipt Detail with ID {updateGoodsReceiptDetailDto.Id} not found.");
            
        if (detail.GoodsReceiptHeader.Status != "Draft")
            throw new InvalidOperationException("Can only update details of goods receipts in Draft status.");
            
        _mapper.Map(updateGoodsReceiptDetailDto, detail);
        detail.TotalPrice = detail.ReceivedQuantity * detail.UnitPrice;
        
        _dbContext.GoodsReceiptDetails.Update(detail);
        await _dbContext.SaveChangesAsync();
        
        // Update the total amount of the goods receipt
        await UpdateGoodsReceiptTotalAsync(detail.GoodsReceiptHeaderId);
    }

    public async Task DeleteGoodsReceiptDetailAsync(int id)
    {
        var detail = await _dbContext.GoodsReceiptDetails
            .Include(d => d.GoodsReceiptHeader)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        if (detail == null)
            throw new KeyNotFoundException($"Goods Receipt Detail with ID {id} not found.");
            
        if (detail.GoodsReceiptHeader.Status != "Draft")
            throw new InvalidOperationException("Can only delete details of goods receipts in Draft status.");
            
        var goodsReceiptHeaderId = detail.GoodsReceiptHeaderId;
        
        _dbContext.GoodsReceiptDetails.Remove(detail);
        await _dbContext.SaveChangesAsync();
        
        // Update the total amount of the goods receipt
        await UpdateGoodsReceiptTotalAsync(goodsReceiptHeaderId);
    }

    public async Task<PurchaseOrderDto?> GetPurchaseOrderForReceivingAsync(int purchaseOrderId)
    {
        var purchaseOrder = await _dbContext.PurchaseOrders
            .Include(po => po.Supplier)
            .Include(po => po.CostCenter)
            .Include(po => po.Details)
            .FirstOrDefaultAsync(po => po.Id == purchaseOrderId);

        if (purchaseOrder != null)
        {
            // Load related data for purchase order details
            foreach (var detail in purchaseOrder.Details)
            {
                // Load product information
                if (detail.ProductId > 0)
                {
                    detail.Product = await _dbContext.Products.FindAsync(detail.ProductId);
                }
            }
        }
            
        if (purchaseOrder == null)
            return null;
            
        // Check if purchase order is in a valid status for receiving
        if (purchaseOrder.Status != "Approved" && purchaseOrder.Status != "Partially Received" && purchaseOrder.Status != "Receiving")
            throw new InvalidOperationException($"Purchase Order with status '{purchaseOrder.Status}' cannot be received.");
            
        return _mapper.Map<PurchaseOrderDto>(purchaseOrder);
    }

    private async Task<string> GenerateDocumentNumberAsync()
    {
        // Get the current date
        var today = DateTime.UtcNow;
        var year = today.Year.ToString().Substring(2); // Last 2 digits of year
        var month = today.Month.ToString().PadLeft(2, '0');
        var day = today.Day.ToString().PadLeft(2, '0');
        
        // Get the count of goods receipts for today
        var todayReceipts = await _dbContext.GoodsReceiptHeaders
            .CountAsync(gr => gr.CreatedAt.Date == today.Date);
        
        // Generate the document number
        var sequence = (todayReceipts + 1).ToString().PadLeft(3, '0');
        return $"GR-{year}{month}{day}-{sequence}";
    }

    private async Task UpdateGoodsReceiptTotalAsync(int goodsReceiptHeaderId)
    {
        var goodsReceipt = await _dbContext.GoodsReceiptHeaders
            .Include(gr => gr.Details)
            .FirstOrDefaultAsync(gr => gr.Id == goodsReceiptHeaderId);
            
        if (goodsReceipt == null)
            throw new KeyNotFoundException($"Goods Receipt with ID {goodsReceiptHeaderId} not found.");
            
        goodsReceipt.TotalAmount = goodsReceipt.Details.Sum(d => d.TotalPrice);
        
        _dbContext.GoodsReceiptHeaders.Update(goodsReceipt);
        await _dbContext.SaveChangesAsync();
    }

    private async Task<bool> CheckAllPurchaseOrderItemsReceivedAsync(int purchaseOrderId)
    {
        var purchaseOrderDetails = await _dbContext.PurchaseOrderDetails
            .Where(pod => pod.PurchaseOrderId == purchaseOrderId)
            .ToListAsync();
            
        if (!purchaseOrderDetails.Any())
            return false;
            
        foreach (var detail in purchaseOrderDetails)
        {
            // Get all goods receipt details for this purchase order detail
            var receivedDetails = await _dbContext.GoodsReceiptDetails
                .Where(grd => grd.PurchaseOrderDetailId == detail.Id && grd.GoodsReceiptHeader.Status == "Completed")
                .ToListAsync();
                
            // Calculate total received quantity
            var totalReceivedQuantity = receivedDetails.Sum(grd => grd.ReceivedQuantity);
            
            // If any item has not been fully received, return false
            if (totalReceivedQuantity < detail.Quantity)
                return false;
        }
        
        // All items have been fully received
        return true;
    }
}
