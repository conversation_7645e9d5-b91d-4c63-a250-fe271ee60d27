export interface GoodsReceipt {
  id: number;
  documentNumber: string;
  purchaseOrderId?: number;
  purchaseOrderNumber?: string;
  supplierId: number;
  supplierName: string;
  costCenterId: number;
  costCenterName: string;
  receiptDate: Date;
  deliveryNoteNumber?: string;
  invoiceNumber?: string;
  invoiceDate?: Date;
  status: string;
  notes?: string;
  totalAmount: number;
  receivedById?: number;
  receivedByName?: string;
  approvedById?: number;
  approvedByName?: string;
  approvedAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  details?: GoodsReceiptDetail[];
}

export interface GoodsReceiptListItem {
  id: number;
  documentNumber: string;
  purchaseOrderNumber?: string;
  supplierName: string;
  costCenterName: string;
  receiptDate: Date;
  invoiceNumber?: string;
  status: string;
  totalAmount: number;
  receivedByName?: string;
}

export interface GoodsReceiptDetail {
  id: number;
  goodsReceiptHeaderId: number;
  productId: number;
  productCode: string;
  productName: string;
  purchaseOrderDetailId?: number;
  batchId?: number;
  batchNumber?: string;
  unitId?: number;
  unitName?: string;
  orderedQuantity: number;
  receivedQuantity: number;
  unitPrice: number;
  totalPrice: number;
  expiryDate?: Date;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateGoodsReceipt {
  purchaseOrderId?: number;
  supplierId: number;
  costCenterId: number;
  receiptDate: Date;
  deliveryNoteNumber?: string;
  invoiceNumber?: string;
  invoiceDate?: Date;
  notes?: string;
  details: CreateGoodsReceiptDetail[];
}

export interface CreateGoodsReceiptDetail {
  productId: number;
  purchaseOrderDetailId?: number;
  batchId?: number;
  unitId?: number;
  orderedQuantity: number;
  receivedQuantity: number;
  unitPrice: number;
  expiryDate?: Date;
  notes?: string;
}

export interface UpdateGoodsReceipt {
  id: number;
  purchaseOrderId?: number;
  supplierId: number;
  costCenterId: number;
  receiptDate: Date;
  deliveryNoteNumber?: string;
  invoiceNumber?: string;
  invoiceDate?: Date;
  status?: string;
  notes?: string;
}

export interface UpdateGoodsReceiptDetail {
  id: number;
  productId: number;
  batchId?: number;
  unitId?: number;
  receivedQuantity: number;
  unitPrice: number;
  expiryDate?: Date;
  notes?: string;
}

export interface CompleteGoodsReceipt {
  id: number;
  notes?: string;
}
