import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { StoreService } from '../../../core/services/store.service';
import { CostCenter, CostCenterList, CostCenterType, CreateCostCenter, UpdateCostCenter } from '../../../core/models/cost-center.model';
import { Store } from '../../../core/models/store.model';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-cost-centers',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './cost-centers.component.html',
  styleUrls: ['./cost-centers.component.scss']
})
export class CostCentersComponent implements OnInit {
  costCenterForm!: FormGroup;
  costCenters: CostCenterList[] = [];
  costCenterTypes: CostCenterType[] = [];
  stores: Store[] = [];
  displayedColumns: string[] = ['name', 'code', 'storeName', 'typeName', 'isSalesPoint', 'isActive', 'actions'];
  isLoading = false;
  isSubmitting = false;
  isEditMode = false;
  selectedCostCenterId: number | null = null;

  // Filtering
  filteredStoreId: number | null = null;
  filteredStoreName: string | null = null;

  // Pagination
  totalItems = 0;
  pageSize = 10;
  pageIndex = 0;
  
  constructor(
    private fb: FormBuilder,
    private costCenterService: CostCenterService,
    private storeService: StoreService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router
  ) {}
  
  ngOnInit(): void {
    this.initForm();

    // Check for query parameters to filter by store
    this.route.queryParams.subscribe(params => {
      if (params['storeId']) {
        this.filteredStoreId = +params['storeId'];
        this.filteredStoreName = params['storeName'] || null;

        // Pre-select the store in the form
        this.costCenterForm.patchValue({
          storeId: this.filteredStoreId
        });
      }
    });

    this.loadCostCenters();
    this.loadCostCenterTypes();
    this.loadStores();
  }
  
  initForm(): void {
    this.costCenterForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(150)]],
      code: ['', Validators.maxLength(50)],
      storeId: [null, Validators.required],
      typeId: [null],
      description: ['', Validators.maxLength(500)],
      autoTransfer: [false],
      isSalesPoint: [false],
      abbreviation: ['', Validators.maxLength(50)]
    });
  }
  
  loadCostCenters(): void {
    this.isLoading = true;

    // If we have a filtered store ID, load cost centers for that store only
    const request = this.filteredStoreId
      ? this.costCenterService.getByStore(this.filteredStoreId)
      : this.costCenterService.getAll();

    request
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.costCenters = data;
          this.totalItems = data.length;

          // Add a notification if we're filtering by store
          if (this.filteredStoreId && this.filteredStoreName) {
            this.snackBar.open(`Showing cost centers for store: ${this.filteredStoreName}`, 'Clear Filter', {
              duration: 5000
            }).onAction().subscribe(() => {
              this.clearStoreFilter();
            });
          }
        },
        error: (error) => {
          console.error('Error loading cost centers:', error);
          this.snackBar.open('Failed to load cost centers', 'Close', { duration: 3000 });
        }
      });
  }
  
  loadCostCenterTypes(): void {
    this.costCenterService.getAllCostCenterTypes()
      .subscribe({
        next: (data) => {
          this.costCenterTypes = data;
        },
        error: (error) => {
          console.error('Error loading cost center types:', error);
          this.snackBar.open('Failed to load cost center types', 'Close', { duration: 3000 });
        }
      });
  }
  
  loadStores(): void {
    this.storeService.getAllStores()
      .subscribe({
        next: (data) => {
          this.stores = data;
        },
        error: (error) => {
          console.error('Error loading stores:', error);
          this.snackBar.open('Failed to load stores', 'Close', { duration: 3000 });
        }
      });
  }
  
  onSubmit(): void {
    if (this.costCenterForm.invalid) {
      return;
    }
    
    this.isSubmitting = true;
    
    if (this.isEditMode && this.selectedCostCenterId) {
      const updateData: UpdateCostCenter = {
        ...this.costCenterForm.value,
        id: this.selectedCostCenterId,
        isActive: true
      };
      
      this.costCenterService.update(this.selectedCostCenterId, updateData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Cost center updated successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadCostCenters();
          },
          error: (error) => {
            console.error('Error updating cost center:', error);
            this.snackBar.open('Failed to update cost center', 'Close', { duration: 3000 });
          }
        });
    } else {
      const createData: CreateCostCenter = this.costCenterForm.value;
      
      this.costCenterService.create(createData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Cost center created successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadCostCenters();
          },
          error: (error) => {
            console.error('Error creating cost center:', error);
            this.snackBar.open('Failed to create cost center', 'Close', { duration: 3000 });
          }
        });
    }
  }
  
  editCostCenter(costCenter: CostCenterList): void {
    this.isEditMode = true;
    this.selectedCostCenterId = costCenter.id;
    
    this.costCenterService.getById(costCenter.id)
      .subscribe({
        next: (data) => {
          this.costCenterForm.patchValue({
            name: data.name,
            code: data.code,
            storeId: data.storeId,
            typeId: data.typeId,
            description: data.description,
            autoTransfer: data.autoTransfer,
            isSalesPoint: data.isSalesPoint,
            abbreviation: data.abbreviation
          });
        },
        error: (error) => {
          console.error('Error loading cost center details:', error);
          this.snackBar.open('Failed to load cost center details', 'Close', { duration: 3000 });
        }
      });
  }
  
  deleteCostCenter(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: { title: 'Confirm Delete', message: 'Are you sure you want to delete this cost center?' }
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.costCenterService.delete(id)
          .subscribe({
            next: () => {
              this.snackBar.open('Cost center deleted successfully', 'Close', { duration: 3000 });
              this.loadCostCenters();
            },
            error: (error) => {
              console.error('Error deleting cost center:', error);
              this.snackBar.open('Failed to delete cost center', 'Close', { duration: 3000 });
            }
          });
      }
    });
  }
  
  toggleStatus(costCenter: CostCenterList): void {
    const action = costCenter.isActive ? 'deactivate' : 'activate';
    const method = costCenter.isActive ? this.costCenterService.deactivate(costCenter.id) : this.costCenterService.activate(costCenter.id);
    
    method.subscribe({
      next: () => {
        this.snackBar.open(`Cost center ${action}d successfully`, 'Close', { duration: 3000 });
        this.loadCostCenters();
      },
      error: (error) => {
        console.error(`Error ${action}ing cost center:`, error);
        this.snackBar.open(`Failed to ${action} cost center`, 'Close', { duration: 3000 });
      }
    });
  }
  
  resetForm(): void {
    this.costCenterForm.reset({
      autoTransfer: false,
      isSalesPoint: false
    });
    this.isEditMode = false;
    this.selectedCostCenterId = null;
  }
  
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
  }
  
  onSortChange(sort: Sort): void {
    // Implement sorting logic if needed
  }
  
  getStoreName(storeId: number): string {
    const store = this.stores.find(s => s.id === storeId);
    return store ? store.name : '';
  }
  
  getTypeName(typeId: number | undefined): string {
    if (!typeId) return '';
    const type = this.costCenterTypes.find(t => t.id === typeId);
    return type ? type.name : '';
  }

  clearStoreFilter(): void {
    this.filteredStoreId = null;
    this.filteredStoreName = null;

    // Clear the query parameters
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {}
    });

    // Reload cost centers without filter
    this.loadCostCenters();

    // Reset the form store selection
    this.costCenterForm.patchValue({
      storeId: null
    });
  }
}
