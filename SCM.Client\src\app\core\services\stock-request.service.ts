import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { 
  StockRequestHeader, 
  StockRequestDetail, 
  CreateStockRequestHeader, 
  UpdateStockRequestHeader, 
  CreateStockRequestDetail, 
  UpdateStockRequestDetail,
  SubmitStockRequest,
  ApproveStockRequest,
  RejectStockRequest,
  CompleteStockRequest
} from '../models/stock-request.model';

@Injectable({
  providedIn: 'root'
})
export class StockRequestService {
  private apiUrl = `${environment.apiUrl}/stock-requests`;

  constructor(private http: HttpClient) { }

  // Stock Request Header methods
  getAllStockRequests(): Observable<StockRequestHeader[]> {
    return this.http.get<StockRequestHeader[]>(this.apiUrl);
  }

  getStockRequestById(id: number): Observable<StockRequestHeader> {
    return this.http.get<StockRequestHeader>(`${this.apiUrl}/${id}`);
  }

  getStockRequestsByFromCostCenterId(costCenterId: number): Observable<StockRequestHeader[]> {
    return this.http.get<StockRequestHeader[]>(`${this.apiUrl}/from-cost-center/${costCenterId}`);
  }

  getStockRequestsByToCostCenterId(costCenterId: number): Observable<StockRequestHeader[]> {
    return this.http.get<StockRequestHeader[]>(`${this.apiUrl}/to-cost-center/${costCenterId}`);
  }

  getStockRequestsByStatus(status: string): Observable<StockRequestHeader[]> {
    return this.http.get<StockRequestHeader[]>(`${this.apiUrl}/status/${status}`);
  }

  createStockRequest(stockRequest: CreateStockRequestHeader): Observable<StockRequestHeader> {
    return this.http.post<StockRequestHeader>(this.apiUrl, stockRequest);
  }

  updateStockRequest(id: number, stockRequest: UpdateStockRequestHeader): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, stockRequest);
  }

  deleteStockRequest(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  submitStockRequest(id: number, submitData: SubmitStockRequest): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/submit`, submitData);
  }

  approveStockRequest(id: number, approveData: ApproveStockRequest): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/approve`, approveData);
  }

  rejectStockRequest(id: number, rejectData: RejectStockRequest): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/reject`, rejectData);
  }

  completeStockRequest(id: number, completeData: CompleteStockRequest): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/complete`, completeData);
  }

  cancelStockRequest(id: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/cancel`, {});
  }

  // Stock Request Detail methods
  getStockRequestDetails(stockRequestHeaderId: number): Observable<StockRequestDetail[]> {
    return this.http.get<StockRequestDetail[]>(`${this.apiUrl}/${stockRequestHeaderId}/details`);
  }

  getStockRequestDetailById(id: number): Observable<StockRequestDetail> {
    return this.http.get<StockRequestDetail>(`${this.apiUrl}/details/${id}`);
  }

  createStockRequestDetail(stockRequestHeaderId: number, detail: CreateStockRequestDetail): Observable<StockRequestDetail> {
    return this.http.post<StockRequestDetail>(`${this.apiUrl}/${stockRequestHeaderId}/details`, detail);
  }

  updateStockRequestDetail(id: number, detail: UpdateStockRequestDetail): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/details/${id}`, detail);
  }

  deleteStockRequestDetail(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/details/${id}`);
  }
}
