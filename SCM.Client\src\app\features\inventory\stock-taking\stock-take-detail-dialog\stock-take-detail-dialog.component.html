<div class="dialog-container">
  <div class="dialog-header">
    <h2 mat-dialog-title>Stock Take Details</h2>
    <button mat-icon-button (click)="close()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <mat-dialog-content *ngIf="!isLoading">
    <div class="stock-take-info">
      <div class="info-row">
        <div class="info-item">
          <span class="label">Document Number:</span>
          <span class="value">{{stockTake.documentNumber || stockTake.id}}</span>
        </div>
        <div class="info-item">
          <span class="label">Status:</span>
          <span class="value status-badge" [ngClass]="{'status-draft': stockTake.status === 'Draft', 'status-in-progress': stockTake.status === 'In Progress', 'status-completed': stockTake.status === 'Completed', 'status-cancelled': stockTake.status === 'Cancelled'}">
            {{stockTake.status}}
          </span>
        </div>
      </div>

      <div class="info-row">
        <div class="info-item">
          <span class="label">Cost Center:</span>
          <span class="value">{{stockTake.costCenterName}}</span>
        </div>
        <div class="info-item">
          <span class="label">Date:</span>
          <span class="value">{{stockTake.startDate | date:'medium'}}</span>
        </div>
      </div>

      <div class="info-row">
        <div class="info-item">
          <span class="label">Created By:</span>
          <span class="value">{{stockTake.createdBy || 'Unknown'}}</span>
        </div>
        <div class="info-item" *ngIf="stockTake.status === 'Completed'">
          <span class="label">Completed Date:</span>
          <span class="value">{{stockTake.updatedAt | date:'medium'}}</span>
        </div>
      </div>

      <div class="info-row" *ngIf="stockTake.notes">
        <div class="info-item full-width">
          <span class="label">Notes:</span>
          <span class="value">{{stockTake.notes}}</span>
        </div>
      </div>
    </div>

    <div class="variance-summary">
      <div class="summary-item">
        <span class="label">Total Items:</span>
        <span class="value">{{stockTake.details?.length || 0}}</span>
      </div>
      <div class="summary-item">
        <span class="label">Positive Variance:</span>
        <span class="value positive">{{getPositiveVarianceCount()}}</span>
      </div>
      <div class="summary-item">
        <span class="label">Negative Variance:</span>
        <span class="value negative">{{getNegativeVarianceCount()}}</span>
      </div>
      <div class="summary-item">
        <span class="label">No Variance:</span>
        <span class="value">{{getZeroVarianceCount()}}</span>
      </div>
    </div>

    <div class="table-container mat-elevation-z2">
      <table mat-table [dataSource]="stockTake.details || []">
        <!-- Product Code Column -->
        <ng-container matColumnDef="productCode">
          <th mat-header-cell *matHeaderCellDef>Product Code</th>
          <td mat-cell *matCellDef="let detail">{{detail.productCode}}</td>
        </ng-container>

        <!-- Product Name Column -->
        <ng-container matColumnDef="productName">
          <th mat-header-cell *matHeaderCellDef>Product Name</th>
          <td mat-cell *matCellDef="let detail">{{detail.productName}}</td>
        </ng-container>

        <!-- System Quantity Column -->
        <ng-container matColumnDef="systemQuantity">
          <th mat-header-cell *matHeaderCellDef>System Quantity</th>
          <td mat-cell *matCellDef="let detail">{{detail.systemQuantity}}</td>
        </ng-container>

        <!-- Actual Quantity Column -->
        <ng-container matColumnDef="actualQuantity">
          <th mat-header-cell *matHeaderCellDef>Counted Quantity</th>
          <td mat-cell *matCellDef="let detail">{{detail.actualQuantity}}</td>
        </ng-container>

        <!-- Variance Column -->
        <ng-container matColumnDef="variance">
          <th mat-header-cell *matHeaderCellDef>Variance</th>
          <td mat-cell *matCellDef="let detail" [ngClass]="{'negative-variance': detail.variance < 0, 'positive-variance': detail.variance > 0}">
            {{detail.variance}}
          </td>
        </ng-container>

        <!-- Notes Column -->
        <ng-container matColumnDef="notes">
          <th mat-header-cell *matHeaderCellDef>Notes</th>
          <td mat-cell *matCellDef="let detail">{{detail.notes}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <form [formGroup]="completeForm" *ngIf="stockTake.status === 'In Progress'">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Completion Notes</mat-label>
        <textarea matInput formControlName="notes" rows="3" placeholder="Enter any notes about this stock take"></textarea>
        <mat-error *ngIf="completeForm.get('notes')?.hasError('maxlength')">
          Notes cannot exceed 500 characters
        </mat-error>
      </mat-form-field>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="close()">Close</button>
    <button mat-button color="warn" *ngIf="stockTake.status === 'Draft' || stockTake.status === 'In Progress'" (click)="cancelStockTake()">
      Cancel Stock Take
    </button>
    <button mat-raised-button color="primary" *ngIf="stockTake.status === 'In Progress'" (click)="completeStockTake()">
      Complete Stock Take
    </button>
  </mat-dialog-actions>
</div>
