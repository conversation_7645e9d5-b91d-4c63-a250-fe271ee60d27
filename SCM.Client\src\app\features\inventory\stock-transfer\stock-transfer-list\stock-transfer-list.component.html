<div class="container">
  <div class="header">
    <h1>Stock Transfers</h1>
    <button mat-raised-button color="primary" (click)="addStockTransfer()">
      <mat-icon>add</mat-icon> New Stock Transfer
    </button>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>
  
  <mat-card class="filter-card" *ngIf="!isLoading">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by reference number or cost center">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilter()">
            <mat-option value="">All</mat-option>
            <mat-option *ngFor="let status of statusOptions" [value]="status">
              {{status}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2" *ngIf="!isLoading">
    <table mat-table [dataSource]="filteredTransfers" class="stock-transfer-table" matSort>
      <!-- Reference Number Column -->
      <ng-container matColumnDef="referenceNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Reference #</th>
        <td mat-cell *matCellDef="let transfer">{{transfer.referenceNumber}}</td>
      </ng-container>
      
      <!-- From Cost Center Column -->
      <ng-container matColumnDef="fromCostCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>From</th>
        <td mat-cell *matCellDef="let transfer">{{transfer.fromCostCenterName}}</td>
      </ng-container>
      
      <!-- To Cost Center Column -->
      <ng-container matColumnDef="toCostCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>To</th>
        <td mat-cell *matCellDef="let transfer">{{transfer.toCostCenterName}}</td>
      </ng-container>
      
      <!-- Transfer Date Column -->
      <ng-container matColumnDef="transferDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
        <td mat-cell *matCellDef="let transfer">{{transfer.transferDate | date:'medium'}}</td>
      </ng-container>
      
      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let transfer">
          <span class="status-badge" [ngClass]="getStatusClass(transfer.status)">
            {{transfer.status}}
          </span>
        </td>
      </ng-container>
      
      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let transfer">
          <button mat-icon-button color="primary" (click)="viewStockTransfer(transfer)" matTooltip="View Details">
            <mat-icon>visibility</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="transfer.status === 'Draft'" (click)="editStockTransfer(transfer)" matTooltip="Edit">
            <mat-icon>edit</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="transfer.status === 'Draft' || transfer.status === 'Pending'" (click)="completeStockTransfer(transfer)" matTooltip="Complete Transfer">
            <mat-icon>check_circle</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" *ngIf="transfer.status === 'Draft' || transfer.status === 'Pending'" (click)="cancelStockTransfer(transfer)" matTooltip="Cancel Transfer">
            <mat-icon>cancel</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" *ngIf="transfer.status === 'Draft'" (click)="deleteStockTransfer(transfer)" matTooltip="Delete">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      
      <!-- Row shown when there is no matching data -->
      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" colspan="6">
          <div class="no-data">
            <mat-icon>info</mat-icon>
            <span>No stock transfers found</span>
          </div>
        </td>
      </tr>
    </table>
    
    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>

  <!-- Approved Stock Requests Section -->
  <div class="approved-requests-section" *ngIf="!isLoading">
    <h2>Approved Stock Requests</h2>
    <p class="section-description">These stock requests have been approved and are ready to be converted to stock transfers.</p>

    <div *ngIf="isLoadingRequests" class="loading-spinner">
      <mat-spinner diameter="30"></mat-spinner>
    </div>

    <div class="table-container mat-elevation-z2" *ngIf="!isLoadingRequests">
      <table mat-table [dataSource]="approvedStockRequests" class="approved-requests-table">
        <!-- Reference Number Column -->
        <ng-container matColumnDef="referenceNumber">
          <th mat-header-cell *matHeaderCellDef>Reference #</th>
          <td mat-cell *matCellDef="let request">{{request.referenceNumber}}</td>
        </ng-container>

        <!-- From Cost Center Column -->
        <ng-container matColumnDef="fromCostCenterName">
          <th mat-header-cell *matHeaderCellDef>From</th>
          <td mat-cell *matCellDef="let request">{{request.fromCostCenterName}}</td>
        </ng-container>

        <!-- To Cost Center Column -->
        <ng-container matColumnDef="toCostCenterName">
          <th mat-header-cell *matHeaderCellDef>To</th>
          <td mat-cell *matCellDef="let request">{{request.toCostCenterName}}</td>
        </ng-container>

        <!-- Request Date Column -->
        <ng-container matColumnDef="requestDate">
          <th mat-header-cell *matHeaderCellDef>Request Date</th>
          <td mat-cell *matCellDef="let request">{{request.requestDate | date:'short'}}</td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let request">
            <span class="status-badge status-approved">{{request.status}}</span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let request">
            <button mat-raised-button color="primary" (click)="createTransferFromRequest(request)"
                    matTooltip="Create Transfer">
              <mat-icon>swap_horiz</mat-icon> Create Transfer
            </button>
            <button mat-button color="accent" (click)="completeStockRequest(request)"
                    matTooltip="Mark as Completed" style="margin-left: 8px;">
              <mat-icon>check</mat-icon> Complete
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="['referenceNumber', 'fromCostCenterName', 'toCostCenterName', 'requestDate', 'status', 'actions']"></tr>
        <tr mat-row *matRowDef="let row; columns: ['referenceNumber', 'fromCostCenterName', 'toCostCenterName', 'requestDate', 'status', 'actions'];"></tr>

        <!-- Row shown when there is no matching data -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="6">
            <div class="no-data">
              <mat-icon>info</mat-icon>
              <span>No approved stock requests found</span>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</div>
