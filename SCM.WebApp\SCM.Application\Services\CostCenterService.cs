using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class CostCenterService : ICostCenterService
{
    private readonly IRepository<CostCenter> _costCenterRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public CostCenterService(
        IRepository<CostCenter> costCenterRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _costCenterRepository = costCenterRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CostCenterDto>> GetAllCostCentersAsync()
    {
        var costCenters = await _dbContext.CostCenters
            .Include(cc => cc.Store)
                .ThenInclude(s => s.Location)
                    .ThenInclude(l => l.Company)
            .Include(cc => cc.Type)
            .ToListAsync();

        return _mapper.Map<IEnumerable<CostCenterDto>>(costCenters);
    }

    public async Task<CostCenterDto?> GetCostCenterByIdAsync(int id)
    {
        var costCenter = await _dbContext.CostCenters
            .Include(cc => cc.Store)
                .ThenInclude(s => s.Location)
                    .ThenInclude(l => l.Company)
            .Include(cc => cc.Type)
            .FirstOrDefaultAsync(cc => cc.Id == id);

        return costCenter != null ? _mapper.Map<CostCenterDto>(costCenter) : null;
    }

    public async Task<IEnumerable<CostCenterDto>> GetCostCentersByStoreIdAsync(int storeId)
    {
        var costCenters = await _dbContext.CostCenters
            .Include(cc => cc.Store)
                .ThenInclude(s => s.Location)
                    .ThenInclude(l => l.Company)
            .Include(cc => cc.Type)
            .Where(cc => cc.StoreId == storeId)
            .ToListAsync();

        return _mapper.Map<IEnumerable<CostCenterDto>>(costCenters);
    }

    public async Task<IEnumerable<CostCenterDto>> GetCostCentersByLocationIdAsync(int locationId)
    {
        var costCenters = await _dbContext.CostCenters
            .Include(cc => cc.Store)
                .ThenInclude(s => s.Location)
                    .ThenInclude(l => l.Company)
            .Include(cc => cc.Type)
            .Where(cc => cc.Store.LocationId == locationId)
            .ToListAsync();

        return _mapper.Map<IEnumerable<CostCenterDto>>(costCenters);
    }

    public async Task<IEnumerable<CostCenterDto>> GetCostCentersByTypeIdAsync(int typeId)
    {
        var costCenters = await _dbContext.CostCenters
            .Include(cc => cc.Store)
                .ThenInclude(s => s.Location)
                    .ThenInclude(l => l.Company)
            .Include(cc => cc.Type)
            .Where(cc => cc.TypeId == typeId)
            .ToListAsync();

        return _mapper.Map<IEnumerable<CostCenterDto>>(costCenters);
    }

    public async Task<IEnumerable<CostCenterDto>> GetSalesPointsAsync()
    {
        var costCenters = await _dbContext.CostCenters
            .Include(cc => cc.Store)
                .ThenInclude(s => s.Location)
                    .ThenInclude(l => l.Company)
            .Include(cc => cc.Type)
            .Where(cc => cc.IsSalesPoint)
            .ToListAsync();

        return _mapper.Map<IEnumerable<CostCenterDto>>(costCenters);
    }

    public async Task<CostCenterDto> CreateCostCenterAsync(CreateCostCenterDto createCostCenterDto)
    {
        // Check if cost center with the same name already exists
        var existingCostCenter = await _dbContext.CostCenters
            .FirstOrDefaultAsync(cc => cc.Name.ToLower() == createCostCenterDto.Name.ToLower());

        if (existingCostCenter != null)
            throw new InvalidOperationException($"Cost center with name '{createCostCenterDto.Name}' already exists.");

        var costCenter = _mapper.Map<CostCenter>(createCostCenterDto);

        // Get type name if TypeId is provided
        if (costCenter.TypeId.HasValue)
        {
            var type = await _dbContext.CostCenterTypes.FindAsync(costCenter.TypeId.Value);
            if (type != null)
            {
                costCenter.TypeName = type.Name;
            }
        }

        costCenter.IsActive = true;
        costCenter.CreatedAt = DateTime.UtcNow;

        await _costCenterRepository.AddAsync(costCenter);

        // Reload the cost center with navigation properties
        var createdCostCenter = await _dbContext.CostCenters
            .Include(cc => cc.Store)
                .ThenInclude(s => s.Location)
                    .ThenInclude(l => l.Company)
            .Include(cc => cc.Type)
            .FirstOrDefaultAsync(cc => cc.Id == costCenter.Id);

        return _mapper.Map<CostCenterDto>(createdCostCenter!);
    }

    public async Task UpdateCostCenterAsync(UpdateCostCenterDto updateCostCenterDto)
    {
        var costCenter = await _costCenterRepository.GetByIdAsync(updateCostCenterDto.Id);
        if (costCenter == null)
            throw new KeyNotFoundException($"Cost center with ID {updateCostCenterDto.Id} not found.");

        // Check if name is being changed and if the new name already exists
        if (costCenter.Name != updateCostCenterDto.Name)
        {
            var existingCostCenter = await _dbContext.CostCenters
                .FirstOrDefaultAsync(cc => cc.Name.ToLower() == updateCostCenterDto.Name.ToLower() && cc.Id != updateCostCenterDto.Id);

            if (existingCostCenter != null)
                throw new InvalidOperationException($"Cost center with name '{updateCostCenterDto.Name}' already exists.");
        }

        _mapper.Map(updateCostCenterDto, costCenter);

        // Get type name if TypeId is provided
        if (costCenter.TypeId.HasValue)
        {
            var type = await _dbContext.CostCenterTypes.FindAsync(costCenter.TypeId.Value);
            if (type != null)
            {
                costCenter.TypeName = type.Name;
            }
        }
        else
        {
            costCenter.TypeName = null;
        }

        costCenter.UpdatedAt = DateTime.UtcNow;

        await _costCenterRepository.UpdateAsync(costCenter);
    }

    public async Task DeleteCostCenterAsync(int id)
    {
        var costCenter = await _costCenterRepository.GetByIdAsync(id);
        if (costCenter == null)
            throw new KeyNotFoundException($"Cost center with ID {id} not found.");

        // Check if cost center is used in any related entities
        var hasStockOnHand = await _dbContext.StockOnHand.AnyAsync(s => s.CostCenterId == id);
        if (hasStockOnHand)
            throw new InvalidOperationException("Cannot delete cost center that has stock on hand.");

        var hasTransactions = await _dbContext.TransactionHeaders.AnyAsync(t => t.SourceCostCenterId == id || t.DestinationCostCenterId == id);
        if (hasTransactions)
            throw new InvalidOperationException("Cannot delete cost center that has transactions.");

        var hasProductLinks = await _dbContext.ProductCostCenterLinks.AnyAsync(p => p.CostCenterId == id);
        if (hasProductLinks)
            throw new InvalidOperationException("Cannot delete cost center that has product links.");

        var hasUserAccesses = await _dbContext.UserCostCenterAccesses.AnyAsync(u => u.CostCenterId == id);
        if (hasUserAccesses)
            throw new InvalidOperationException("Cannot delete cost center that has user accesses.");

        var hasStockTakes = await _dbContext.StockTakeHeaders.AnyAsync(s => s.CostCenterId == id);
        if (hasStockTakes)
            throw new InvalidOperationException("Cannot delete cost center that has stock takes.");

        // If no related entities, mark as inactive instead of deleting
        costCenter.IsActive = false;
        costCenter.UpdatedAt = DateTime.UtcNow;

        await _costCenterRepository.UpdateAsync(costCenter);
    }
}
