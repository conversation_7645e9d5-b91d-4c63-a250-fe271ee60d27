import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProductService } from '../../../core/services/product.service';

import { DepartmentService } from '../../../core/services/department.service';
import { Product } from '../../../core/models/product.model';
import { finalize, catchError, of } from 'rxjs';
import { ProductImportDialogComponent } from '../product-import-dialog/product-import-dialog.component';

interface ProductViewModel {
  id: number;
  code: string;
  name: string;
  category: string;
  unitSize: string;
  unitPrice: number;
  averageCost: number;
  stockOnHand: number;
  baseQuantity: number;
  reorderLevel: number;
  status: string;
}

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss']
})
export class ProductListComponent implements OnInit {
  displayedColumns: string[] = ['id', 'name', 'category', 'unitSize', 'unitPrice', 'averageCost', 'stockOnHand', 'baseQuantity', 'reorderLevel', 'status', 'actions'];
  products: ProductViewModel[] = [];
  filteredProducts: ProductViewModel[] = [];
  searchTerm: string = '';
  selectedCategory: string = '';
  isLoading: boolean = false;

  categories: string[] = [
    'Food',
    'Beverage',
    'Cleaning Supplies',
    'Office Supplies',
    'Engineering',
    'Tobacco'
  ];

  constructor(
    private productService: ProductService,
    private departmentService: DepartmentService,
    private snackBar: MatSnackBar,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProducts();
  }

  loadProducts(): void {
    this.isLoading = true;

    // Load departments to populate categories dropdown
    this.departmentService.getAll()
      .pipe(catchError(error => {
        console.error('Error loading departments', error);
        return of([]);
      }))
      .subscribe(departments => {
        this.categories = departments.map(d => d.name);
      });

    // Get all products (now includes stock information from backend)
    this.productService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (products) => {
          // Map products to view model (stock information is already included)
          this.products = products.map(p => this.mapToViewModel(p));
          this.filteredProducts = [...this.products];
        },
        error: (error) => {
          console.error('Error loading products', error);
          this.snackBar.open('Error loading products. Please try again later.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  mapToViewModel(product: Product): ProductViewModel {
    // Determine status based on stock level
    let status = 'Active';
    if (!product.isActive) {
      status = 'Inactive';
    } else if (product.minStock && product.reorderPoint) {
      if ((product.stockOnHand || 0) <= product.minStock) {
        status = 'Low Stock';
      }
    }

    return {
      id: product.id,
      code: product.code,
      name: product.name,
      category: product.departmentName || 'Uncategorized',
      unitSize: product.unitName || '',
      unitPrice: product.salesPrice || 0,
      averageCost: product.averageCost || 0,
      stockOnHand: product.stockOnHand || 0,
      baseQuantity: product.baseQuantity || 0,
      reorderLevel: product.reorderPoint || 0,
      status: status
    };
  }

  applyFilter(): void {
    this.filteredProducts = this.products.filter(product => {
      const matchesSearch = this.searchTerm === '' ||
        product.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.code.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.id.toString().includes(this.searchTerm);

      const matchesCategory = this.selectedCategory === '' ||
        product.category === this.selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedCategory = '';
    this.filteredProducts = [...this.products];
  }

  addProduct(): void {
    this.router.navigate(['/products/new']);
  }

  editProduct(product: ProductViewModel): void {
    this.router.navigate([`/products/${product.id}`]);
  }

  deleteProduct(product: ProductViewModel): void {
    if (confirm(`Are you sure you want to delete ${product.name}?`)) {
      // Delete the product directly using its ID
      this.productService.delete(product.id)
        .subscribe({
          next: () => {
            this.snackBar.open('Product deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadProducts(); // Reload the products
          },
          error: (error) => {
            console.error('Error deleting product', error);
            this.snackBar.open('Error deleting product. Please try again later.', 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  viewProductDetails(product: ProductViewModel): void {
    this.router.navigate([`/products/${product.id}`]);
  }

  openImportDialog(): void {
    const dialogRef = this.dialog.open(ProductImportDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.successCount > 0) {
        // Reload the products list if any products were imported successfully
        this.loadProducts();
      }
    });
  }
}
