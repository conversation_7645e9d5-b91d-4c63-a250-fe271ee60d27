using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class StockAdjustmentHeader : BaseEntity
{
    public string ReferenceNumber { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public DateTime AdjustmentDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft"; // Draft, Completed, Cancelled
    public int? CreatedById { get; set; }
    public int? CompletedById { get; set; }
    public DateTime? CompletedAt { get; set; }
    
    // Navigation properties
    public virtual CostCenter CostCenter { get; set; } = null!;
    public virtual User? CreatedBy { get; set; }
    public virtual User? CompletedBy { get; set; }
    public virtual ICollection<StockAdjustmentDetail> Details { get; set; } = new List<StockAdjustmentDetail>();
}
