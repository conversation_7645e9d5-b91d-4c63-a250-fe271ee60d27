.container {
  padding: 20px;
  position: relative;
  min-height: 400px;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

mat-card {
  max-width: 1000px;
  margin: 0 auto;
}

mat-card-actions {
  padding: 16px;
  display: flex;
  gap: 10px;
}

mat-list-item {
  height: auto !important;
  padding: 16px 0;
}

.tab-content {
  padding: 20px 0;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.full-width {
  width: 100%;
}

.no-data-message {
  text-align: center;
  padding: 20px;
  font-style: italic;
  color: rgba(0, 0, 0, 0.54);
}
