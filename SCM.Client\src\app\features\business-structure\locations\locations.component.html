<div class="container">
  <div class="row">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ isEditMode ? 'Edit Location' : 'Add New Location' }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="locationForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Name</mat-label>
                  <input matInput formControlName="name" placeholder="Enter location name">
                  <mat-error *ngIf="locationForm.get('name')?.hasError('required')">
                    Name is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Code</mat-label>
                  <input matInput formControlName="code" placeholder="Enter location code">
                </mat-form-field>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Company</mat-label>
                  <mat-select formControlName="companyId">
                    <mat-option *ngFor="let company of companies" [value]="company.id">
                      {{ company.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="locationForm.get('companyId')?.hasError('required')">
                    Company is required
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" placeholder="Enter description" rows="3"></textarea>
                </mat-form-field>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Address</mat-label>
                  <textarea matInput formControlName="address" placeholder="Enter address" rows="2"></textarea>
                </mat-form-field>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>City</mat-label>
                  <input matInput formControlName="city" placeholder="Enter city">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>State/Province</mat-label>
                  <input matInput formControlName="state" placeholder="Enter state/province">
                </mat-form-field>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Country</mat-label>
                  <input matInput formControlName="country" placeholder="Enter country">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Postal Code</mat-label>
                  <input matInput formControlName="postalCode" placeholder="Enter postal code">
                </mat-form-field>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Phone</mat-label>
                  <input matInput formControlName="phone" placeholder="Enter phone number">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email</mat-label>
                  <input matInput formControlName="email" placeholder="Enter email" type="email">
                  <mat-error *ngIf="locationForm.get('email')?.hasError('email')">
                    Please enter a valid email address
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="button-row">
              <button mat-raised-button color="primary" type="submit" [disabled]="locationForm.invalid || isSubmitting">
                <mat-icon>save</mat-icon>
                {{ isEditMode ? 'Update' : 'Save' }}
              </button>
              <button mat-raised-button type="button" (click)="resetForm()" [disabled]="isSubmitting">
                <mat-icon>refresh</mat-icon>
                Reset
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Locations</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="loading-shade" *ngIf="isLoading">
            <mat-spinner diameter="50"></mat-spinner>
          </div>

          <div class="table-container">
            <table mat-table [dataSource]="locations" matSort (matSortChange)="onSortChange($event)" class="full-width">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                <td mat-cell *matCellDef="let location">{{ location.name }}</td>
              </ng-container>

              <!-- Code Column -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Code</th>
                <td mat-cell *matCellDef="let location">{{ location.code }}</td>
              </ng-container>

              <!-- Company Column -->
              <ng-container matColumnDef="companyName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Company</th>
                <td mat-cell *matCellDef="let location">{{ location.companyName || 'N/A' }}</td>
              </ng-container>

              <!-- City Column -->
              <ng-container matColumnDef="city">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>City</th>
                <td mat-cell *matCellDef="let location">{{ location.city }}</td>
              </ng-container>

              <!-- Country Column -->
              <ng-container matColumnDef="country">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Country</th>
                <td mat-cell *matCellDef="let location">{{ location.country }}</td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="isActive">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                <td mat-cell *matCellDef="let location">
                  <button mat-icon-button [color]="location.isActive ? 'primary' : 'warn'"
                          (click)="toggleStatus(location)" matTooltip="Click to toggle status">
                    <mat-icon>{{ location.isActive ? 'toggle_on' : 'toggle_off' }}</mat-icon>
                  </button>
                </td>
              </ng-container>

              <!-- Stores Count Column -->
              <ng-container matColumnDef="storesCount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Stores</th>
                <td mat-cell *matCellDef="let location">{{ location.storesCount }}</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let location">
                  <button mat-icon-button color="primary" (click)="viewLocation(location.id)" matTooltip="View">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button color="accent" (click)="editLocation(location)" matTooltip="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteLocation(location.id)" matTooltip="Delete">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <mat-paginator [length]="totalItems"
                          [pageSize]="pageSize"
                          [pageSizeOptions]="[5, 10, 25, 50]"
                          (page)="onPageChange($event)">
            </mat-paginator>
          </div>

          <div class="no-data-message" *ngIf="locations.length === 0 && !isLoading">
            No locations found. Please add a location.
          </div>

          <!-- Debug information -->
          <div *ngIf="locations.length > 0 && !isLoading" style="margin-top: 20px; padding: 10px; background-color: #f5f5f5; border-radius: 4px;">
            <h3>Debug Information</h3>
            <p>Total locations: {{ locations.length }}</p>
            <div *ngFor="let location of locations">
              <p>ID: {{ location.id }} | Name: {{ location.name }} | Company: {{ location.companyName || 'N/A' }} | Active: {{ location.isActive ? 'Yes' : 'No' }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
