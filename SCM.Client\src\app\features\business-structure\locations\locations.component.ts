import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { LocationService } from '../../../core/services/location.service';
import { CompanyService } from '../../../core/services/company.service';
import { Location, LocationList, CreateLocation, UpdateLocation } from '../../../core/models/location.model';
import { Company } from '../../../core/models/company.model';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-locations',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './locations.component.html',
  styleUrls: ['./locations.component.scss']
})
export class LocationsComponent implements OnInit {
  locationForm!: FormGroup;
  locations: LocationList[] = [];
  companies: any[] = [];
  displayedColumns: string[] = ['name', 'code', 'companyName', 'city', 'country', 'isActive', 'storesCount', 'actions'];
  isLoading = false;
  isSubmitting = false;
  isEditMode = false;
  selectedLocationId: number | null = null;

  // Pagination
  totalItems = 0;
  pageSize = 10;
  pageIndex = 0;

  constructor(
    private fb: FormBuilder,
    private locationService: LocationService,
    private companyService: CompanyService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadCompanies();
    this.loadLocations();

    // Check if we have a companyId in the query params
    this.route.queryParams.subscribe(params => {
      const companyId = params['companyId'];
      if (companyId) {
        this.locationForm.patchValue({ companyId: +companyId });
      }
    });
  }

  initForm(): void {
    this.locationForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(150)]],
      code: ['', Validators.maxLength(50)],
      description: ['', Validators.maxLength(500)],
      companyId: [null, Validators.required],
      address: ['', Validators.maxLength(200)],
      city: ['', Validators.maxLength(100)],
      state: ['', Validators.maxLength(100)],
      country: ['', Validators.maxLength(100)],
      postalCode: ['', Validators.maxLength(20)],
      phone: ['', Validators.maxLength(20)],
      email: ['', [Validators.email, Validators.maxLength(100)]]
    });
  }

  loadCompanies(): void {
    this.companyService.getAll()
      .subscribe({
        next: (data) => {
          this.companies = data;
        },
        error: (error) => {
          console.error('Error loading companies:', error);
          this.snackBar.open('Failed to load companies', 'Close', { duration: 3000 });
        }
      });
  }

  loadLocations(): void {
    this.isLoading = true;
    this.locationService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          console.log('Locations loaded:', data);
          this.locations = data;
          this.totalItems = data.length;

          // Check if we have any locations
          if (data.length === 0) {
            console.log('No locations found in the response');
          } else {
            console.log(`Found ${data.length} locations`);

            // Log each location for debugging
            data.forEach(location => {
              console.log(`Location: ${location.id} - ${location.name}, CompanyId: ${location.companyId}, Active: ${location.isActive}`);
            });
          }
        },
        error: (error) => {
          console.error('Error loading locations:', error);
          this.snackBar.open('Failed to load locations', 'Close', { duration: 3000 });
        }
      });
  }

  onSubmit(): void {
    if (this.locationForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    if (this.isEditMode && this.selectedLocationId) {
      const updateData: UpdateLocation = {
        ...this.locationForm.value,
        id: this.selectedLocationId,
        isActive: true
      };

      this.locationService.update(this.selectedLocationId, updateData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Location updated successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadLocations();
          },
          error: (error) => {
            console.error('Error updating location:', error);
            this.snackBar.open('Failed to update location', 'Close', { duration: 3000 });
          }
        });
    } else {
      const createData: CreateLocation = this.locationForm.value;

      this.locationService.create(createData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Location created successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadLocations();
          },
          error: (error) => {
            console.error('Error creating location:', error);
            // Display the specific error message from the server if available
            const errorMessage = error.error || 'Failed to create location. Please try again later.';
            this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
          }
        });
    }
  }

  editLocation(location: LocationList): void {
    this.isEditMode = true;
    this.selectedLocationId = location.id;

    this.locationService.getById(location.id)
      .subscribe({
        next: (data) => {
          this.locationForm.patchValue({
            name: data.name,
            code: data.code,
            description: data.description,
            companyId: data.companyId,
            address: data.address,
            city: data.city,
            state: data.state,
            country: data.country,
            postalCode: data.postalCode,
            phone: data.phone,
            email: data.email
          });
        },
        error: (error) => {
          console.error('Error loading location details:', error);
          this.snackBar.open('Failed to load location details', 'Close', { duration: 3000 });
        }
      });
  }

  viewLocation(id: number): void {
    this.router.navigate(['/locations', id]);
  }

  deleteLocation(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: { title: 'Confirm Delete', message: 'Are you sure you want to delete this location?' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.locationService.delete(id)
          .subscribe({
            next: () => {
              this.snackBar.open('Location deleted successfully', 'Close', { duration: 3000 });
              this.loadLocations();
            },
            error: (error) => {
              console.error('Error deleting location:', error);
              this.snackBar.open('Failed to delete location', 'Close', { duration: 3000 });
            }
          });
      }
    });
  }

  toggleStatus(location: LocationList): void {
    const action = location.isActive ? 'deactivate' : 'activate';
    const method = location.isActive ? this.locationService.deactivate(location.id) : this.locationService.activate(location.id);

    method.subscribe({
      next: () => {
        this.snackBar.open(`Location ${action}d successfully`, 'Close', { duration: 3000 });
        this.loadLocations();
      },
      error: (error) => {
        console.error(`Error ${action}ing location:`, error);
        this.snackBar.open(`Failed to ${action} location`, 'Close', { duration: 3000 });
      }
    });
  }

  resetForm(): void {
    this.locationForm.reset();
    this.isEditMode = false;
    this.selectedLocationId = null;

    // Check if we have a companyId in the query params
    this.route.queryParams.subscribe(params => {
      const companyId = params['companyId'];
      if (companyId) {
        this.locationForm.patchValue({ companyId: +companyId });
      }
    });
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
  }

  onSortChange(sort: Sort): void {
    // Implement sorting logic if needed
  }

  getCompanyName(companyId: number): string {
    const company = this.companies.find(c => c.id === companyId);
    return company ? company.name : '';
  }
}
