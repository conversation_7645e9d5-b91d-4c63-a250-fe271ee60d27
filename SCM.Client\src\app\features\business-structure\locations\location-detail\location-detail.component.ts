import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';

import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { LocationService } from '../../../../core/services/location.service';
import { StoreService } from '../../../../core/services/store.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { Location } from '../../../../core/models/location.model';
import { StoreList } from '../../../../core/models/store.model';
import { CostCenterList } from '../../../../core/models/cost-center.model';
import { finalize, forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'app-location-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule,
    MatListModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './location-detail.component.html',
  styleUrls: ['./location-detail.component.scss']
})
export class LocationDetailComponent implements OnInit {
  location: Location | null = null;
  stores: StoreList[] = [];
  costCenters: CostCenterList[] = [];
  isLoading = false;
  storeColumns: string[] = ['name', 'code', 'city', 'country', 'isSalesPoint', 'isActive', 'costCentersCount', 'actions'];
  costCenterColumns: string[] = ['name', 'code', 'storeName', 'typeName', 'isSalesPoint', 'isActive', 'actions'];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private locationService: LocationService,
    private storeService: StoreService,
    private costCenterService: CostCenterService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadLocationData();
  }

  loadLocationData(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.snackBar.open('Location ID is missing', 'Close', { duration: 3000 });
      this.router.navigate(['/locations']);
      return;
    }

    this.isLoading = true;

    forkJoin({
      location: this.locationService.getById(+id).pipe(
        catchError(error => {
          console.error('Error loading location:', error);
          this.snackBar.open('Failed to load location details', 'Close', { duration: 3000 });
          return of(null);
        })
      ),
      stores: this.storeService.getByLocation(+id).pipe(
        catchError(error => {
          console.error('Error loading stores:', error);
          return of([]);
        })
      ),
      costCenters: this.costCenterService.getByLocation(+id).pipe(
        catchError(error => {
          console.error('Error loading cost centers:', error);
          return of([]);
        })
      )
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe(result => {
      this.location = result.location;
      this.stores = result.stores;
      this.costCenters = result.costCenters;

      if (!this.location) {
        this.router.navigate(['/locations']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/locations']);
  }

  editLocation(): void {
    if (this.location) {
      this.router.navigate(['/locations/edit', this.location.id]);
    }
  }

  viewStore(id: number): void {
    this.router.navigate(['/stores', id]);
  }

  viewCostCenter(id: number): void {
    this.router.navigate(['/cost-centers', id]);
  }

  addStore(): void {
    if (this.location) {
      this.router.navigate(['/stores/new'], {
        queryParams: { locationId: this.location.id }
      });
    }
  }

  goToCompany(): void {
    if (this.location && this.location.companyId) {
      this.router.navigate(['/companies', this.location.companyId]);
    }
  }
}
