using SCM.Domain.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SCM.Domain.Entities;

public class TransactionHeader : BaseEntity
{
    [Key]
    [Column("TransactionId")]
    public override int Id { get; set; }

    [Required]
    [Column("TransactionNumber")]
    public string TransactionNumber { get; set; } = string.Empty;

    [Column("ProcessId")]
    public int? ProcessId { get; set; }

    [Column("StageTypeId")]
    public int? StageTypeId { get; set; }

    [Required]
    [Column("TransactionTypeId")]
    public int TransactionTypeId { get; set; }

    [Column("SourceCostCenterId")]
    public int? SourceCostCenterId { get; set; }

    [Column("DestinationCostCenterId")]
    public int? DestinationCostCenterId { get; set; }

    [Column("SupplierId")]
    public int? SupplierId { get; set; }

    [Column("ReferenceNumber")]
    public string? ReferenceNumber { get; set; }

    [Required]
    [Column("TransactionDate")]
    public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

    [Column("Notes")]
    public string? Notes { get; set; }

    [Required]
    [Column("SubTotal")]
    public decimal SubTotal { get; set; } = 0;

    [Required]
    [Column("TaxAmount")]
    public decimal TaxAmount { get; set; } = 0;

    [Required]
    [Column("TotalAmount")]
    public decimal TotalAmount { get; set; } = 0;

    [Required]
    [Column("DiscountAmount")]
    public decimal DiscountAmount { get; set; } = 0;

    [Required]
    [Column("DiscountPercentage")]
    public decimal DiscountPercentage { get; set; } = 0;

    [Required]
    [Column("Status")]
    public string Status { get; set; } = "Draft"; // Draft, Pending, Approved, Completed, Cancelled

    [Required]
    [Column("CreatedById")]
    public int CreatedById { get; set; }

    [Column("ApprovedById")]
    public int? ApprovedById { get; set; }

    [Column("ApprovedDate")]
    public DateTime? ApprovedDate { get; set; }

    [Required]
    [Column("CreatedAt")]
    public new DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("UpdatedAt")]
    public new DateTime? UpdatedAt { get; set; }

    [Required]
    [Column("IsActive")]
    public new bool IsActive { get; set; } = true;

    [Column("RelatedTransactionId")]
    public int? RelatedTransactionId { get; set; }

    [Column("RelatedTransactionNumber")]
    public string? RelatedTransactionNumber { get; set; }

    [Required]
    [Column("IsSkippedStep")]
    public bool IsSkippedStep { get; set; } = false;

    // Properties that don't exist in the database but are needed for DTOs
    [NotMapped]
    public int? CustomerId { get; set; }

    [NotMapped]
    public int? CurrencyId { get; set; }

    [NotMapped]
    public string? ExternalReference { get; set; }

    [NotMapped]
    public DateTime? RequiredDate { get; set; }

    [NotMapped]
    public decimal? ExchangeRate { get; set; } = 1;

    [NotMapped]
    public int? CompletedById { get; set; }

    [NotMapped]
    public DateTime? CompletedAt { get; set; }

    // Navigation properties - only keep the essential ones
    [NotMapped]
    public virtual TransactionStageType? StageType { get; set; }

    [NotMapped]
    public virtual CostCenter? SourceCostCenter { get; set; }

    [NotMapped]
    public virtual CostCenter? DestinationCostCenter { get; set; }

    [NotMapped]
    public virtual Supplier? Supplier { get; set; }

    [NotMapped]
    public virtual Customer? Customer { get; set; }

    [NotMapped]
    public virtual Currency? Currency { get; set; }

    // Keep only the Details collection which is essential
    public virtual ICollection<TransactionDetail> Details { get; set; } = new List<TransactionDetail>();
}
