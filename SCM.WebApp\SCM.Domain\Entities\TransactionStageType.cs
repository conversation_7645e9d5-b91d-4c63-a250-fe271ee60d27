using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionStageType : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Sequence { get; set; }

    // Navigation properties
    public virtual ICollection<TransactionStage> TransactionStages { get; set; } = new List<TransactionStage>();
    public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; } = new List<TransactionHeader>();
}
