using SCM.Domain.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SCM.Domain.Entities;

public class TransactionDetail : BaseEntity
{
    [Key]
    [Column("TransactionDetailId")]
    public override int Id { get; set; }

    [Column("TransactionId")]
    public int TransactionId { get; set; }

    [Column("ProductId")]
    public int ProductId { get; set; }

    [Column("BatchId")]
    public int? BatchId { get; set; }

    [Column("UnitId")]
    public int UnitId { get; set; }

    [Column("Quantity")]
    public decimal Quantity { get; set; }

    [Column("UnitPrice")]
    public decimal UnitPrice { get; set; }

    [Column("TaxRate")]
    public decimal? TaxRate { get; set; }

    [Column("TaxId")]
    public int? TaxId { get; set; }

    [Column("TaxAmount")]
    public decimal? TaxAmount { get; set; }

    [Column("DiscountAmount")]
    public decimal? DiscountAmount { get; set; }

    [Column("DiscountPercentage")]
    public decimal? DiscountPercentage { get; set; }

    [Column("LineTotal")]
    public decimal LineTotal { get; set; }

    [Column("Notes")]
    public string? Notes { get; set; }

    [Column("IsRecipe")]
    public bool IsRecipe { get; set; } = false;

    [Column("CreatedAt")]
    public new DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("UpdatedAt")]
    public new DateTime? UpdatedAt { get; set; }

    [Column("IsActive")]
    public new bool IsActive { get; set; } = true;

    [NotMapped]
    public int LineNumber { get; set; }

    // Navigation properties - all marked as NotMapped to avoid shadow properties
    [NotMapped]
    public virtual TransactionHeader Transaction { get; set; } = null!;

    [NotMapped]
    public virtual Product Product { get; set; } = null!;

    [NotMapped]
    public virtual Batch? Batch { get; set; }

    [NotMapped]
    public virtual Unit? Unit { get; set; }

    [NotMapped]
    public virtual Tax? Tax { get; set; }
}
