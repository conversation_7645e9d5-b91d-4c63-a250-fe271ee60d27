{"openapi": "3.0.4", "info": {"title": "SCM API", "version": "v1"}, "paths": {"/api/Auth/test-db": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/test": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}}}}, "/api/Barcodes": {"get": {"tags": ["Barcodes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BarcodeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BarcodeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BarcodeDto"}}}}}}}, "post": {"tags": ["Barcodes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBarcodeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBarcodeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBarcodeDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}}}}}}, "/api/Barcodes/{id}": {"get": {"tags": ["Barcodes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}}}}}, "put": {"tags": ["Barcodes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBarcodeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBarcodeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBarcodeDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Barcodes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Barcodes/value/{value}": {"get": {"tags": ["Barcodes"], "parameters": [{"name": "value", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}}}}}}, "/api/Barcodes/product/{productId}": {"get": {"tags": ["Barcodes"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BarcodeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BarcodeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BarcodeDto"}}}}}}}}, "/api/Barcodes/product/{productId}/primary": {"get": {"tags": ["Barcodes"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BarcodeDto"}}}}}}}, "/api/Barcodes/test-auth": {"get": {"tags": ["Barcodes"], "responses": {"200": {"description": "OK"}}}}, "/api/Brands": {"get": {"tags": ["Brands"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BrandDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BrandDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BrandDto"}}}}}}}, "post": {"tags": ["Brands"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBrandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBrandDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBrandDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BrandDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BrandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BrandDto"}}}}}}}, "/api/Brands/{id}": {"get": {"tags": ["Brands"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BrandDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BrandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BrandDto"}}}}}}, "put": {"tags": ["Brands"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBrandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBrandDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBrandDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Brands"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Brands/test-auth": {"get": {"tags": ["Brands"], "responses": {"200": {"description": "OK"}}}}, "/api/Companies": {"get": {"tags": ["Companies"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDto"}}}}}}}, "post": {"tags": ["Companies"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCompanyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCompanyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCompanyDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}}}}}}, "/api/Companies/{id}": {"get": {"tags": ["Companies"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}}}}}, "put": {"tags": ["Companies"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCompanyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCompanyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCompanyDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Companies"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Companies/{id}/locations": {"get": {"tags": ["Companies"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDto"}}}}}}}, "/api/Companies/test-auth": {"get": {"tags": ["Companies"], "responses": {"200": {"description": "OK"}}}}, "/api/CostCenters": {"get": {"tags": ["CostCenters"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}}}}}, "post": {"tags": ["CostCenters"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCostCenterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCostCenterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCostCenterDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CostCenterDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CostCenterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenterDto"}}}}}}}, "/api/CostCenters/{id}": {"get": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CostCenterDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CostCenterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenterDto"}}}}}}, "put": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCostCenterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCostCenterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCostCenterDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/CostCenters/store/{storeId}": {"get": {"tags": ["CostCenters"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}}}}}}, "/api/CostCenters/location/{locationId}": {"get": {"tags": ["CostCenters"], "parameters": [{"name": "locationId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}}}}}}, "/api/CostCenters/type/{typeId}": {"get": {"tags": ["CostCenters"], "parameters": [{"name": "typeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}}}}}}, "/api/CostCenters/sales-points": {"get": {"tags": ["CostCenters"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}}}}}}}}, "/api/CostCenters/{id}/activate": {"put": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/CostCenters/{id}/deactivate": {"put": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/CostCenters/test-auth": {"get": {"tags": ["CostCenters"], "responses": {"200": {"description": "OK"}}}}, "/api/CostCenterTypes": {"get": {"tags": ["CostCenterTypes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterTypeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterTypeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterTypeDto"}}}}}}}, "post": {"tags": ["CostCenterTypes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCostCenterTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCostCenterTypeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCostCenterTypeDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}}}}}}, "/api/CostCenterTypes/{id}": {"get": {"tags": ["CostCenterTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}}}}}, "put": {"tags": ["CostCenterTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCostCenterTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCostCenterTypeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCostCenterTypeDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["CostCenterTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/CostCenterTypes/{id}/cost-centers": {"get": {"tags": ["CostCenterTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenterTypeDto"}}}}}}}, "/api/CostCenterTypes/test-auth": {"get": {"tags": ["CostCenterTypes"], "responses": {"200": {"description": "OK"}}}}, "/api/CreditNotes": {"get": {"tags": ["CreditNotes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "post": {"tags": ["CreditNotes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCreditNoteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCreditNoteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCreditNoteDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/CreditNotes/{id}": {"get": {"tags": ["CreditNotes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}, "put": {"tags": ["CreditNotes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCreditNoteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCreditNoteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCreditNoteDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/CreditNotes/{id}/complete": {"post": {"tags": ["CreditNotes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/CreditNotes/{id}/cancel": {"post": {"tags": ["CreditNotes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/CreditNotes/test-auth": {"get": {"tags": ["CreditNotes"], "responses": {"200": {"description": "OK"}}}}, "/api/Departments": {"get": {"tags": ["Departments"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentDto"}}}}}}}, "post": {"tags": ["Departments"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DepartmentDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DepartmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DepartmentDto"}}}}}}}, "/api/Departments/{id}": {"get": {"tags": ["Departments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DepartmentDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DepartmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DepartmentDto"}}}}}}, "put": {"tags": ["Departments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Departments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Departments/test-auth": {"get": {"tags": ["Departments"], "responses": {"200": {"description": "OK"}}}}, "/api/GoodsReceipts": {"get": {"tags": ["GoodsReceipts"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}}}}}, "post": {"tags": ["GoodsReceipts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGoodsReceiptHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateGoodsReceiptHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateGoodsReceiptHeaderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoodsReceiptHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptHeaderDto"}}}}}}}, "/api/GoodsReceipts/{id}": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoodsReceiptHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptHeaderDto"}}}}}}, "put": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGoodsReceiptHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateGoodsReceiptHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateGoodsReceiptHeaderDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/GoodsReceipts/purchase-order/{purchaseOrderId}": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "purchaseOrderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}}}}}}, "/api/GoodsReceipts/supplier/{supplierId}": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "supplierId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}}}}}}, "/api/GoodsReceipts/cost-center/{costCenterId}": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}}}}}}, "/api/GoodsReceipts/status/{status}": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptListDto"}}}}}}}}, "/api/GoodsReceipts/purchase-order/{purchaseOrderId}/for-receiving": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "purchaseOrderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PurchaseOrderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderDto"}}}}}}}, "/api/GoodsReceipts/{id}/complete": {"post": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteGoodsReceiptDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompleteGoodsReceiptDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompleteGoodsReceiptDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/GoodsReceipts/{id}/cancel": {"post": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/GoodsReceipts/{goodsReceiptHeaderId}/details": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "goodsReceiptHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}}}}}}, "post": {"tags": ["GoodsReceipts"], "parameters": [{"name": "goodsReceiptHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGoodsReceiptDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateGoodsReceiptDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateGoodsReceiptDetailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}}}}}}, "/api/GoodsReceipts/details/{id}": {"get": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}}}}}}, "put": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGoodsReceiptDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateGoodsReceiptDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateGoodsReceiptDetailDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["GoodsReceipts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/GoodsReceipts/test-auth": {"get": {"tags": ["GoodsReceipts"], "responses": {"200": {"description": "OK"}}}}, "/api/Locations": {"get": {"tags": ["Locations"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationSimpleDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationSimpleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationSimpleDto"}}}}}}}, "post": {"tags": ["Locations"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLocationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateLocationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateLocationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}}}}}}, "/api/Locations/{id}": {"get": {"tags": ["Locations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}}}}}, "put": {"tags": ["Locations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateLocationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateLocationDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Locations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Locations/company/{companyId}": {"get": {"tags": ["Locations"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationSimpleDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationSimpleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationSimpleDto"}}}}}}}}, "/api/Locations/{id}/stores": {"get": {"tags": ["Locations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}}}}}}, "/api/Locations/{id}/cost-centers": {"get": {"tags": ["Locations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LocationSimpleDto"}}}}}}}, "/api/Locations/{id}/activate": {"put": {"tags": ["Locations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Locations/{id}/deactivate": {"put": {"tags": ["Locations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Locations/test-auth": {"get": {"tags": ["Locations"], "responses": {"200": {"description": "OK"}}}}, "/api/Permissions": {"get": {"tags": ["Permissions"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}}}}}}}}, "/api/Permissions/by-category": {"get": {"tags": ["Permissions"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionCategoryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionCategoryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionCategoryDto"}}}}}}}}, "/api/Permissions/{id}": {"get": {"tags": ["Permissions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}}}}}}, "/api/Permissions/by-name/{name}": {"get": {"tags": ["Permissions"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}}}}}}, "/api/Permissions/initialize": {"post": {"tags": ["Permissions"], "responses": {"200": {"description": "OK"}}}}, "/api/Permissions/test-auth": {"get": {"tags": ["Permissions"], "responses": {"200": {"description": "OK"}}}}, "/api/ProductGroups": {"get": {"tags": ["ProductGroups"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}}}}}, "post": {"tags": ["ProductGroups"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductGroupDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductGroupDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}}}}}}, "/api/ProductGroups/{id}": {"get": {"tags": ["ProductGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}}}}}, "put": {"tags": ["ProductGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductGroupDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductGroupDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ProductGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ProductGroups/department/{departmentId}": {"get": {"tags": ["ProductGroups"], "parameters": [{"name": "departmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}}}}}}, "/api/ProductGroups/{id}/with-subgroups": {"get": {"tags": ["ProductGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductGroupDto"}}}}}}}, "/api/ProductGroups/test-auth": {"get": {"tags": ["ProductGroups"], "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests": {"get": {"tags": ["ProductRequests"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}}}}}, "post": {"tags": ["ProductRequests"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestHeader"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestHeader"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestHeader"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductRequestHeader"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductRequestHeader"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductRequestHeader"}}}}}}}, "/api/ProductRequests/{id}": {"get": {"tags": ["ProductRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductRequestHeader"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductRequestHeader"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductRequestHeader"}}}}}}, "put": {"tags": ["ProductRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestHeader"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestHeader"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestHeader"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ProductRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests/costcenter/{costCenterId}": {"get": {"tags": ["ProductRequests"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}}}}}}, "/api/ProductRequests/status/{status}": {"get": {"tags": ["ProductRequests"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestListItem"}}}}}}}}, "/api/ProductRequests/submit": {"post": {"tags": ["ProductRequests"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitProductRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubmitProductRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubmitProductRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests/approve": {"post": {"tags": ["ProductRequests"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApproveProductRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApproveProductRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApproveProductRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests/reject": {"post": {"tags": ["ProductRequests"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectProductRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RejectProductRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RejectProductRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests/complete": {"post": {"tags": ["ProductRequests"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteProductRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompleteProductRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompleteProductRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests/cancel/{id}": {"post": {"tags": ["ProductRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests/{productRequestId}/details": {"get": {"tags": ["ProductRequests"], "parameters": [{"name": "productRequestId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestDetail"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestDetail"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestDetail"}}}}}}}, "post": {"tags": ["ProductRequests"], "parameters": [{"name": "productRequestId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestDetail"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestDetail"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestDetail"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductRequestDetail"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductRequestDetail"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductRequestDetail"}}}}}}}, "/api/ProductRequests/details/{id}": {"put": {"tags": ["ProductRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestDetail"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestDetail"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestDetail"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ProductRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ProductRequests/test-auth": {"get": {"tags": ["ProductRequests"], "responses": {"200": {"description": "OK"}}}}, "/api/Products": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}}}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products/code/{code}": {"get": {"tags": ["Products"], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}}}}, "/api/Products/department/{departmentId}": {"get": {"tags": ["Products"], "parameters": [{"name": "departmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}}}}}}, "/api/Products/group/{groupId}": {"get": {"tags": ["Products"], "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}}}}}}, "/api/Products/subgroup/{subGroupId}": {"get": {"tags": ["Products"], "parameters": [{"name": "subGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}}}}}}, "/api/Products/search": {"get": {"tags": ["Products"], "parameters": [{"name": "term", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}}}}}}, "/api/Products/import": {"post": {"tags": ["Products"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductImportDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductImportDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductImportDto"}}}}}}}, "/api/Products/import/template": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK"}}}}, "/api/Products/test-auth": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK"}}}}, "/api/ProductSubGroups": {"get": {"tags": ["ProductSubGroups"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}}}}}, "post": {"tags": ["ProductSubGroups"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductSubGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductSubGroupDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductSubGroupDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductSubGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductSubGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}}}}}, "/api/ProductSubGroups/{id}": {"get": {"tags": ["ProductSubGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductSubGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductSubGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}}}}, "put": {"tags": ["ProductSubGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductSubGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductSubGroupDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductSubGroupDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ProductSubGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ProductSubGroups/group/{groupId}": {"get": {"tags": ["ProductSubGroups"], "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroupDto"}}}}}}}}, "/api/ProductSubGroups/test-auth": {"get": {"tags": ["ProductSubGroups"], "responses": {"200": {"description": "OK"}}}}, "/api/PurchaseOrders": {"get": {"tags": ["PurchaseOrders"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "post": {"tags": ["PurchaseOrders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/PurchaseOrders/{id}": {"get": {"tags": ["PurchaseOrders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}, "put": {"tags": ["PurchaseOrders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductOrderDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PurchaseOrders/supplier/{supplierId}": {"get": {"tags": ["PurchaseOrders"], "parameters": [{"name": "supplierId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/PurchaseOrders/costcenter/{costCenterId}": {"get": {"tags": ["PurchaseOrders"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/PurchaseOrders/status/{status}": {"get": {"tags": ["PurchaseOrders"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/PurchaseOrders/from-request/{productRequestId}": {"post": {"tags": ["PurchaseOrders"], "parameters": [{"name": "productRequestId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/PurchaseOrders/{id}/submit": {"put": {"tags": ["PurchaseOrders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PurchaseOrders/{id}/approve": {"put": {"tags": ["PurchaseOrders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PurchaseOrders/{id}/reject": {"put": {"tags": ["PurchaseOrders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectReasonDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RejectReasonDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RejectReasonDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PurchaseOrders/{id}/cancel": {"put": {"tags": ["PurchaseOrders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelReasonDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CancelReasonDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CancelReasonDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PurchaseOrders/test-auth": {"get": {"tags": ["PurchaseOrders"], "responses": {"200": {"description": "OK"}}}}, "/api/Receiving": {"get": {"tags": ["Receiving"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "post": {"tags": ["Receiving"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Receiving/{id}": {"get": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}, "put": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Receiving/from-order/{productOrderId}": {"post": {"tags": ["Receiving"], "parameters": [{"name": "productOrderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Receiving/{id}/submit": {"put": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Receiving/{id}/approve": {"put": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Receiving/{id}/reject": {"put": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Receiving/{id}/complete": {"put": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receiving/{id}/cancel": {"put": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Receiving/test-auth": {"get": {"tags": ["Receiving"], "responses": {"200": {"description": "OK"}}}}, "/api/Recipes": {"get": {"tags": ["Recipes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}}}}}, "post": {"tags": ["Recipes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRecipeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRecipeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRecipeDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RecipeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RecipeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RecipeDto"}}}}}}}, "/api/Recipes/{id}": {"get": {"tags": ["Recipes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RecipeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RecipeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RecipeDto"}}}}}}, "put": {"tags": ["Recipes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRecipeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRecipeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRecipeDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Recipes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Recipes/product/{productId}": {"get": {"tags": ["Recipes"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}}}}}}, "/api/Recipes/active": {"get": {"tags": ["Recipes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}}}}}}, "/api/Recipes/subrecipes": {"get": {"tags": ["Recipes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeListDto"}}}}}}}}, "/api/Recipes/{recipeId}/ingredients": {"post": {"tags": ["Recipes"], "parameters": [{"name": "recipeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRecipeIngredientDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRecipeIngredientDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRecipeIngredientDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RecipeIngredientDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RecipeIngredientDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RecipeIngredientDto"}}}}}}}, "/api/Recipes/ingredients/{id}": {"put": {"tags": ["Recipes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRecipeIngredientDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRecipeIngredientDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRecipeIngredientDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Recipes/{recipeId}/ingredients/{ingredientId}": {"delete": {"tags": ["Recipes"], "parameters": [{"name": "recipeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "ingredientId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Recipes/{id}/calculate-cost": {"get": {"tags": ["Recipes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "number", "format": "double"}}, "application/json": {"schema": {"type": "number", "format": "double"}}, "text/json": {"schema": {"type": "number", "format": "double"}}}}}}}, "/api/Recipes/test-auth": {"get": {"tags": ["Recipes"], "responses": {"200": {"description": "OK"}}}}, "/api/Stock": {"get": {"tags": ["Stock"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}}}}}}, "/api/Stock/product/{productId}": {"get": {"tags": ["Stock"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}}}}}}, "/api/Stock/costcenter/{costCenterId}": {"get": {"tags": ["Stock"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}}}}}}, "/api/Stock/item": {"get": {"tags": ["Stock"], "parameters": [{"name": "productId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "costCenterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "batchId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockOnHandDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockOnHandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockOnHandDto"}}}}}}}, "/api/Stock/summary/{costCenterId}": {"get": {"tags": ["Stock"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}}}}}}, "/api/Stock/lowstock": {"get": {"tags": ["Stock"], "parameters": [{"name": "costCenterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}}}}}}, "/api/Stock/overstock": {"get": {"tags": ["Stock"], "parameters": [{"name": "costCenterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}}}}}}, "/api/Stock/reorder": {"get": {"tags": ["Stock"], "parameters": [{"name": "costCenterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandSummaryDto"}}}}}}}}, "/api/Stock/expiring/{daysToExpiry}": {"get": {"tags": ["Stock"], "parameters": [{"name": "daysToExpiry", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "costCenterId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHandDto"}}}}}}}}, "/api/Stock/adjust": {"post": {"tags": ["Stock"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Stock/test-auth": {"get": {"tags": ["Stock"], "responses": {"200": {"description": "OK"}}}}, "/api/StockAdjustments": {"get": {"tags": ["StockAdjustments"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}}}}}, "post": {"tags": ["StockAdjustments"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockAdjustmentHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockAdjustmentHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockAdjustmentHeaderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}}}}}, "/api/StockAdjustments/{id}": {"get": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}}}}, "put": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockAdjustmentHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockAdjustmentHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockAdjustmentHeaderDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockAdjustments/cost-center/{costCenterId}": {"get": {"tags": ["StockAdjustments"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}}}}}}, "/api/StockAdjustments/status/{status}": {"get": {"tags": ["StockAdjustments"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentHeaderDto"}}}}}}}}, "/api/StockAdjustments/{id}/complete": {"post": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteStockAdjustmentDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompleteStockAdjustmentDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompleteStockAdjustmentDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockAdjustments/{id}/cancel": {"post": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockAdjustments/{stockAdjustmentHeaderId}/details": {"get": {"tags": ["StockAdjustments"], "parameters": [{"name": "stockAdjustmentHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}}}}}}, "post": {"tags": ["StockAdjustments"], "parameters": [{"name": "stockAdjustmentHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockAdjustmentDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockAdjustmentDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockAdjustmentDetailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}}}}}}, "/api/StockAdjustments/details/{id}": {"get": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}}}}}}, "put": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockAdjustmentDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockAdjustmentDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockAdjustmentDetailDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockAdjustments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockAdjustments/test-auth": {"get": {"tags": ["StockAdjustments"], "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests": {"get": {"tags": ["StockRequests"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}}}}}, "post": {"tags": ["StockRequests"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockRequestHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockRequestHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockRequestHeaderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}}}}}, "/api/StockRequests/{id}": {"get": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}}}}, "put": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockRequestHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockRequestHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockRequestHeaderDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests/from-cost-center/{costCenterId}": {"get": {"tags": ["StockRequests"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}}}}}}, "/api/StockRequests/to-cost-center/{costCenterId}": {"get": {"tags": ["StockRequests"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}}}}}}, "/api/StockRequests/status/{status}": {"get": {"tags": ["StockRequests"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestHeaderDto"}}}}}}}}, "/api/StockRequests/{id}/submit": {"post": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitStockRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubmitStockRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubmitStockRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests/{id}/approve": {"post": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApproveStockRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApproveStockRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApproveStockRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests/{id}/reject": {"post": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectStockRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RejectStockRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RejectStockRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests/{id}/complete": {"post": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteStockRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompleteStockRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompleteStockRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests/{id}/cancel": {"post": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests/{stockRequestHeaderId}/details": {"get": {"tags": ["StockRequests"], "parameters": [{"name": "stockRequestHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestDetailDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestDetailDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestDetailDto"}}}}}}}, "post": {"tags": ["StockRequests"], "parameters": [{"name": "stockRequestHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockRequestDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockRequestDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockRequestDetailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockRequestDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockRequestDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockRequestDetailDto"}}}}}}}, "/api/StockRequests/details/{id}": {"get": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockRequestDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockRequestDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockRequestDetailDto"}}}}}}, "put": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockRequestDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockRequestDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockRequestDetailDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockRequests/test-auth": {"get": {"tags": ["StockRequests"], "responses": {"200": {"description": "OK"}}}}, "/api/StockTakes": {"get": {"tags": ["StockTakes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}}}}}, "post": {"tags": ["StockTakes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockTakeHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockTakeHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockTakeHeaderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}}}}}, "/api/StockTakes/{id}": {"get": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}}}}, "put": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTakeHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTakeHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockTakeHeaderDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockTakes/costcenter/{costCenterId}": {"get": {"tags": ["StockTakes"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}}}}}}, "/api/StockTakes/status/{status}": {"get": {"tags": ["StockTakes"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeaderDto"}}}}}}}}, "/api/StockTakes/{id}/complete": {"post": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteStockTakeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompleteStockTakeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompleteStockTakeDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockTakes/{id}/cancel": {"post": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockTakes/{stockTakeHeaderId}/details": {"get": {"tags": ["StockTakes"], "parameters": [{"name": "stockTakeHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetailDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetailDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetailDto"}}}}}}}}, "/api/StockTakes/details/{id}": {"get": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTakeDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTakeDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTakeDetailDto"}}}}}}, "put": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTakeDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTakeDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockTakeDetailDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockTakes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockTakes/details": {"post": {"tags": ["StockTakes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockTakeDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockTakeDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockTakeDetailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTakeDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTakeDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTakeDetailDto"}}}}}}}, "/api/StockTakes/{stockTakeHeaderId}/generate-details": {"post": {"tags": ["StockTakes"], "parameters": [{"name": "stockTakeHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockTakes/test-auth": {"get": {"tags": ["StockTakes"], "responses": {"200": {"description": "OK"}}}}, "/api/StockTransfers": {"get": {"tags": ["StockTransfers"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}}}}}, "post": {"tags": ["StockTransfers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockTransferHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockTransferHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockTransferHeaderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}}}}}, "/api/StockTransfers/{id}": {"get": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}}}}, "put": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTransferHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTransferHeaderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockTransferHeaderDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockTransfers/from-cost-center/{costCenterId}": {"get": {"tags": ["StockTransfers"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}}}}}}, "/api/StockTransfers/to-cost-center/{costCenterId}": {"get": {"tags": ["StockTransfers"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}}}}}}, "/api/StockTransfers/status/{status}": {"get": {"tags": ["StockTransfers"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferHeaderDto"}}}}}}}}, "/api/StockTransfers/{id}/complete": {"post": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteStockTransferDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompleteStockTransferDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CompleteStockTransferDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockTransfers/{id}/cancel": {"post": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockTransfers/{stockTransferHeaderId}/details": {"get": {"tags": ["StockTransfers"], "parameters": [{"name": "stockTransferHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferDetailDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferDetailDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferDetailDto"}}}}}}}, "post": {"tags": ["StockTransfers"], "parameters": [{"name": "stockTransferHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStockTransferDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStockTransferDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStockTransferDetailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTransferDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTransferDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTransferDetailDto"}}}}}}}, "/api/StockTransfers/details/{id}": {"get": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockTransferDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockTransferDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockTransferDetailDto"}}}}}}, "put": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTransferDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStockTransferDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStockTransferDetailDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["StockTransfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockTransfers/test-auth": {"get": {"tags": ["StockTransfers"], "responses": {"200": {"description": "OK"}}}}, "/api/Stores": {"get": {"tags": ["Stores"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}}}}}, "post": {"tags": ["Stores"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStoreDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStoreDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStoreDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StoreDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StoreDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreDto"}}}}}}}, "/api/Stores/{id}": {"get": {"tags": ["Stores"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StoreDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StoreDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreDto"}}}}}}, "put": {"tags": ["Stores"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStoreDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStoreDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStoreDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Stores"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Stores/location/{locationId}": {"get": {"tags": ["Stores"], "parameters": [{"name": "locationId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}}}}}}, "/api/Stores/company/{companyId}": {"get": {"tags": ["Stores"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}}}}}}}}, "/api/Stores/{id}/cost-centers": {"get": {"tags": ["Stores"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StoreDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StoreDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreDto"}}}}}}}, "/api/Stores/{id}/activate": {"put": {"tags": ["Stores"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Stores/{id}/deactivate": {"put": {"tags": ["Stores"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Stores/test-auth": {"get": {"tags": ["Stores"], "responses": {"200": {"description": "OK"}}}}, "/api/Suppliers": {"get": {"tags": ["Suppliers"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierListDto"}}}}}}}, "post": {"tags": ["Suppliers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSupplierDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateSupplierDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateSupplierDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}}}}}}, "/api/Suppliers/{id}": {"get": {"tags": ["Suppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}}}}}, "put": {"tags": ["Suppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSupplierDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSupplierDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSupplierDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Suppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Suppliers/name/{name}": {"get": {"tags": ["Suppliers"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SupplierDto"}}}}}}}, "/api/Suppliers/search": {"get": {"tags": ["Suppliers"], "parameters": [{"name": "term", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierListDto"}}}}}}}}, "/api/Suppliers/test-auth": {"get": {"tags": ["Suppliers"], "responses": {"200": {"description": "OK"}}}}, "/api/Taxes": {"get": {"tags": ["Taxes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaxDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaxDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaxDto"}}}}}}}, "post": {"tags": ["Taxes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTaxDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTaxDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTaxDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TaxDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TaxDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}}}}, "/api/Taxes/{id}": {"get": {"tags": ["Taxes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TaxDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TaxDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}}}, "put": {"tags": ["Taxes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaxDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTaxDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTaxDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Taxes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Taxes/default": {"get": {"tags": ["Taxes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TaxDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TaxDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}}}}, "/api/Taxes/test-auth": {"get": {"tags": ["Taxes"], "responses": {"200": {"description": "OK"}}}}, "/api/Test/database": {"get": {"tags": ["Test"], "responses": {"200": {"description": "OK"}}}}, "/api/Test/test-auth": {"get": {"tags": ["Test"], "responses": {"200": {"description": "OK"}}}}, "/api/Transactions": {"get": {"tags": ["Transactions"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/Transactions/{id}": {"get": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Transactions/stage/{stageTypeId}": {"get": {"tags": ["Transactions"], "parameters": [{"name": "stageTypeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/Transactions/status/{status}": {"get": {"tags": ["Transactions"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/Transactions/costcenter/{costCenterId}": {"get": {"tags": ["Transactions"], "parameters": [{"name": "costCenterId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/Transactions/supplier/{supplierId}": {"get": {"tags": ["Transactions"], "parameters": [{"name": "supplierId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}}, "/api/Transactions/product-request": {"post": {"tags": ["Transactions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Transactions/product-request/{id}": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-request/{id}/submit": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-request/{id}/approve": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-request/{id}/reject": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-request/{id}/cancel": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-order": {"post": {"tags": ["Transactions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Transactions/product-order/from-request/{productRequestId}": {"post": {"tags": ["Transactions"], "parameters": [{"name": "productRequestId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductOrderDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Transactions/product-order/{id}": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductOrderDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-order/{id}/submit": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-order/{id}/approve": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-order/{id}/reject": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/product-order/{id}/cancel": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/receiving": {"post": {"tags": ["Transactions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Transactions/receiving/from-order/{productOrderId}": {"post": {"tags": ["Transactions"], "parameters": [{"name": "productOrderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReceivingDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Transactions/receiving/{id}": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/receiving/{id}/detail/{detailId}": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "detailId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateReceivingDetailDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/receiving/{id}/submit": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/receiving/{id}/approve": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/receiving/{id}/reject": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/receiving/{id}/complete": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/receiving/{id}/cancel": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/credit-note": {"post": {"tags": ["Transactions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCreditNoteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCreditNoteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCreditNoteDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeaderDto"}}}}}}}, "/api/Transactions/credit-note/{id}": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCreditNoteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCreditNoteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCreditNoteDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/credit-note/{id}/complete": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/credit-note/{id}/cancel": {"put": {"tags": ["Transactions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Transactions/test-auth": {"get": {"tags": ["Transactions"], "responses": {"200": {"description": "OK"}}}}, "/api/UnitGroups": {"get": {"tags": ["UnitGroups"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitGroupDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitGroupDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitGroupDto"}}}}}}}, "post": {"tags": ["UnitGroups"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUnitGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUnitGroupDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUnitGroupDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}}}}}}, "/api/UnitGroups/{id}": {"get": {"tags": ["UnitGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}}}}}, "put": {"tags": ["UnitGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUnitGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUnitGroupDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUnitGroupDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["UnitGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UnitGroups/{id}/with-units": {"get": {"tags": ["UnitGroups"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnitGroupDto"}}}}}}}, "/api/UnitGroups/test-auth": {"get": {"tags": ["UnitGroups"], "responses": {"200": {"description": "OK"}}}}, "/api/Units": {"get": {"tags": ["Units"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}}}}}, "post": {"tags": ["Units"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUnitDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUnitDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUnitDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnitDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}}}}}}, "/api/Units/{id}": {"get": {"tags": ["Units"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnitDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}}}}}, "put": {"tags": ["Units"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUnitDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUnitDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUnitDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Units"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Units/by-unit-group/{unitGroupId}": {"get": {"tags": ["Units"], "parameters": [{"name": "unitGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}}}}}}, "/api/Units/import": {"post": {"tags": ["Units"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnitImportDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnitImportDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnitImportDto"}}}}}}}, "/api/Units/import/template": {"get": {"tags": ["Units"], "responses": {"200": {"description": "OK"}}}}, "/api/Units/test-auth": {"get": {"tags": ["Units"], "responses": {"200": {"description": "OK"}}}}, "/api/UserPermissions/current": {"get": {"tags": ["UserPermissions"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserPermissionsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserPermissionsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserPermissionsDto"}}}}}}}, "/api/UserPermissions/check/{permission}": {"get": {"tags": ["UserPermissions"], "parameters": [{"name": "permission", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/UserPermissions/test-auth": {"get": {"tags": ["UserPermissions"], "responses": {"200": {"description": "OK"}}}}, "/api/Users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserListDto"}}}}}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}}, "/api/Users/<USER>/{username}": {"get": {"tags": ["Users"], "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Users/<USER>/change-password": {"post": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Users/<USER>/reset-password": {"post": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Users/<USER>/validate-password": {"post": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"ApproveProductRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApproveStockRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthResponse": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "isAdmin": {"type": "boolean"}}, "additionalProperties": false}, "BarcodeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "barcodeValue": {"type": "string", "nullable": true}, "barcodeType": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "isPrimary": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BrandDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CancelReasonDto": {"type": "object", "properties": {"reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanyDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "taxNumber": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "locationsCount": {"type": "integer", "format": "int32"}, "locations": {"type": "array", "items": {"$ref": "#/components/schemas/LocationDto"}, "nullable": true}}, "additionalProperties": false}, "CompleteGoodsReceiptDto": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompleteProductRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompleteStockAdjustmentDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompleteStockRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompleteStockTakeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompleteStockTransferDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CostCenterDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "storeId": {"type": "integer", "format": "int32"}, "storeName": {"type": "string", "nullable": true}, "locationName": {"type": "string", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "typeId": {"type": "integer", "format": "int32", "nullable": true}, "typeName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "autoTransfer": {"type": "boolean"}, "isSalesPoint": {"type": "boolean"}, "abbreviation": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CostCenterTypeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "costCenters": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}, "nullable": true}}, "additionalProperties": false}, "CreateBarcodeDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "barcodeValue": {"type": "string", "nullable": true}, "barcodeType": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "isPrimary": {"type": "boolean"}}, "additionalProperties": false}, "CreateBrandDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateCompanyDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "taxNumber": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateCostCenterDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "storeId": {"type": "integer", "format": "int32"}, "typeId": {"type": "integer", "format": "int32", "nullable": true}, "description": {"type": "string", "nullable": true}, "autoTransfer": {"type": "boolean"}, "isSalesPoint": {"type": "boolean"}, "abbreviation": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateCostCenterTypeDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateCreditNoteDetailDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double", "nullable": true}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "notes": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateCreditNoteDto": {"type": "object", "properties": {"sourceCostCenterId": {"type": "integer", "format": "int32"}, "supplierId": {"type": "integer", "format": "int32", "nullable": true}, "relatedTransactionId": {"type": "integer", "format": "int32", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "referenceNumber": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateCreditNoteDetailDto"}, "nullable": true}}, "additionalProperties": false}, "CreateDepartmentDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateGoodsReceiptDetailDto": {"required": ["productId", "receivedQuantity", "unitPrice"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "purchaseOrderDetailId": {"type": "integer", "format": "int32", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "orderedQuantity": {"type": "number", "format": "double"}, "receivedQuantity": {"minimum": 0.01, "type": "number", "format": "double"}, "unitPrice": {"minimum": 0, "type": "number", "format": "double"}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateGoodsReceiptHeaderDto": {"required": ["costCenterId", "details", "receiptDate", "supplierId"], "type": "object", "properties": {"purchaseOrderId": {"type": "integer", "format": "int32", "nullable": true}, "supplierId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "receiptDate": {"type": "string", "format": "date-time"}, "deliveryNoteNumber": {"type": "string", "nullable": true}, "invoiceNumber": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateGoodsReceiptDetailDto"}}}, "additionalProperties": false}, "CreateLocationDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "companyId": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "brandId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "subGroupId": {"type": "integer", "format": "int32", "nullable": true}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "salesPrice": {"type": "number", "format": "double", "nullable": true}, "minStock": {"type": "number", "format": "double", "nullable": true}, "maxStock": {"type": "number", "format": "double", "nullable": true}, "reorderPoint": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isStockItem": {"type": "boolean"}, "isRecipe": {"type": "boolean"}, "hasExpiry": {"type": "boolean"}, "isProduction": {"type": "boolean"}, "isSaleable": {"type": "boolean"}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "salesUnitId": {"type": "integer", "format": "int32", "nullable": true}, "salesUnitConversionFactor": {"type": "number", "format": "double", "nullable": true}, "allowDiscount": {"type": "boolean"}}, "additionalProperties": false}, "CreateProductGroupDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateProductOrderDto": {"type": "object", "properties": {"supplierId": {"type": "integer", "format": "int32"}, "sourceCostCenterId": {"type": "integer", "format": "int32"}, "transactionDate": {"type": "string", "format": "date-time"}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "referenceNumber": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTransactionDetailDto"}, "nullable": true}, "updatePrices": {"type": "boolean"}}, "additionalProperties": false}, "CreateProductRequestDetail": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "nullable": true}, "deliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductRequestDto": {"type": "object", "properties": {"sourceCostCenterId": {"type": "integer", "format": "int32"}, "transactionDate": {"type": "string", "format": "date-time"}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "referenceNumber": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTransactionDetailDto"}, "nullable": true}}, "additionalProperties": false}, "CreateProductRequestHeader": {"type": "object", "properties": {"costCenterId": {"type": "integer", "format": "int32"}, "requestDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateProductRequestDetail"}, "nullable": true}}, "additionalProperties": false}, "CreateProductSubGroupDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateReceivingDto": {"type": "object", "properties": {"supplierId": {"type": "integer", "format": "int32"}, "sourceCostCenterId": {"type": "integer", "format": "int32"}, "transactionDate": {"type": "string", "format": "date-time"}, "invoiceNumber": {"type": "string", "nullable": true}, "deliveryNoteNumber": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "referenceNumber": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTransactionDetailDto"}, "nullable": true}}, "additionalProperties": false}, "CreateRecipeDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "productId": {"type": "integer", "format": "int32"}, "yield": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isSubRecipe": {"type": "boolean"}, "isActive": {"type": "boolean"}, "ingredients": {"type": "array", "items": {"$ref": "#/components/schemas/CreateRecipeIngredientDto"}, "nullable": true}}, "additionalProperties": false}, "CreateRecipeIngredientDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "notes": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "wastagePercentage": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "CreateStockAdjustmentDetailDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "currentQuantity": {"type": "number", "format": "double"}, "adjustmentQuantity": {"type": "number", "format": "double"}, "newQuantity": {"type": "number", "format": "double"}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateStockAdjustmentHeaderDto": {"type": "object", "properties": {"costCenterId": {"type": "integer", "format": "int32"}, "adjustmentDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStockAdjustmentDetailDto"}, "nullable": true}}, "additionalProperties": false}, "CreateStockRequestDetailDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "nullable": true}, "deliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateStockRequestHeaderDto": {"type": "object", "properties": {"fromCostCenterId": {"type": "integer", "format": "int32"}, "toCostCenterId": {"type": "integer", "format": "int32"}, "requestDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStockRequestDetailDto"}, "nullable": true}}, "additionalProperties": false}, "CreateStockTakeDetailDto": {"type": "object", "properties": {"stockTakeHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "systemQuantity": {"type": "number", "format": "double"}, "countedQuantity": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateStockTakeHeaderDto": {"type": "object", "properties": {"costCenterId": {"type": "integer", "format": "int32"}, "stockTakeDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateStockTransferDetailDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateStockTransferHeaderDto": {"type": "object", "properties": {"fromCostCenterId": {"type": "integer", "format": "int32"}, "toCostCenterId": {"type": "integer", "format": "int32"}, "transferDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStockTransferDetailDto"}, "nullable": true}}, "additionalProperties": false}, "CreateStoreDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "locationId": {"type": "integer", "format": "int32"}, "isSalesPoint": {"type": "boolean"}, "logoPath": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateSupplierDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "taxNumber": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "bankAccount": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateTaxDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "rate": {"type": "number", "format": "double"}, "isDefault": {"type": "boolean"}}, "additionalProperties": false}, "CreateTransactionDetailDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double", "nullable": true}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "discountPercentage": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUnitDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "abbreviation": {"type": "string", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "conversionFactor": {"type": "number", "format": "double", "nullable": true}, "baseConversionFactor": {"type": "number", "format": "double"}, "isBaseUnit": {"type": "boolean"}}, "additionalProperties": false}, "CreateUnitGroupDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUserDto": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "roleId": {"type": "integer", "format": "int32", "nullable": true}, "password": {"type": "string", "nullable": true}, "isAdmin": {"type": "boolean"}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DepartmentDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "productGroupCount": {"type": "integer", "format": "int32"}, "productGroups": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}, "nullable": true}}, "additionalProperties": false}, "GoodsReceiptDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "goodsReceiptHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "purchaseOrderDetailId": {"type": "integer", "format": "int32", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "orderedQuantity": {"type": "number", "format": "double"}, "receivedQuantity": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "totalPrice": {"type": "number", "format": "double"}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GoodsReceiptHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "documentNumber": {"type": "string", "nullable": true}, "purchaseOrderId": {"type": "integer", "format": "int32", "nullable": true}, "purchaseOrderNumber": {"type": "string", "nullable": true}, "supplierId": {"type": "integer", "format": "int32"}, "supplierName": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "receiptDate": {"type": "string", "format": "date-time"}, "deliveryNoteNumber": {"type": "string", "nullable": true}, "invoiceNumber": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "receivedById": {"type": "integer", "format": "int32", "nullable": true}, "receivedByName": {"type": "string", "nullable": true}, "approvedById": {"type": "integer", "format": "int32", "nullable": true}, "approvedByName": {"type": "string", "nullable": true}, "approvedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsReceiptDetailDto"}, "nullable": true}}, "additionalProperties": false}, "GoodsReceiptListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "documentNumber": {"type": "string", "nullable": true}, "purchaseOrderNumber": {"type": "string", "nullable": true}, "supplierName": {"type": "string", "nullable": true}, "costCenterName": {"type": "string", "nullable": true}, "receiptDate": {"type": "string", "format": "date-time"}, "invoiceNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "receivedByName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LocationDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "companyId": {"type": "integer", "format": "int32"}, "companyName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "storesCount": {"type": "integer", "format": "int32"}, "stores": {"type": "array", "items": {"$ref": "#/components/schemas/StoreDto"}, "nullable": true}, "costCenters": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}, "nullable": true}}, "additionalProperties": false}, "LocationSimpleDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "companyId": {"type": "integer", "format": "int32"}, "companyName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "storesCount": {"type": "integer", "format": "int32"}, "costCentersCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PermissionCategoryDto": {"type": "object", "properties": {"category": {"type": "string", "nullable": true}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}, "nullable": true}}, "additionalProperties": false}, "PermissionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "brandId": {"type": "integer", "format": "int32", "nullable": true}, "brandName": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "unitGroupName": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "groupName": {"type": "string", "nullable": true}, "subGroupId": {"type": "integer", "format": "int32", "nullable": true}, "subGroupName": {"type": "string", "nullable": true}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "averageCost": {"type": "number", "format": "double", "nullable": true}, "salesPrice": {"type": "number", "format": "double", "nullable": true}, "minStock": {"type": "number", "format": "double", "nullable": true}, "maxStock": {"type": "number", "format": "double", "nullable": true}, "reorderPoint": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isStockItem": {"type": "boolean"}, "isRecipe": {"type": "boolean"}, "hasExpiry": {"type": "boolean"}, "isProduction": {"type": "boolean"}, "isSaleable": {"type": "boolean"}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "taxName": {"type": "string", "nullable": true}, "taxRate": {"type": "number", "format": "double", "nullable": true}, "salesUnitId": {"type": "integer", "format": "int32", "nullable": true}, "salesUnitName": {"type": "string", "nullable": true}, "salesUnitConversionFactor": {"type": "number", "format": "double", "nullable": true}, "allowDiscount": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "barcodes": {"type": "array", "items": {"$ref": "#/components/schemas/BarcodeDto"}, "nullable": true}}, "additionalProperties": false}, "ProductGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "subGroups": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroupDto"}, "nullable": true}, "subGroupCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductImportDto": {"type": "object", "properties": {"totalRows": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "errorCount": {"type": "integer", "format": "int32"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImportError"}, "nullable": true}, "successfulProducts": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProductImportError": {"type": "object", "properties": {"rowNumber": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductRequestDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productRequestHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "nullable": true}, "deliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductRequestHeader": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "referenceNumber": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "requestDate": {"type": "string", "format": "date-time"}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "submittedById": {"type": "integer", "format": "int32", "nullable": true}, "submittedByName": {"type": "string", "nullable": true}, "submittedAt": {"type": "string", "format": "date-time", "nullable": true}, "approvedById": {"type": "integer", "format": "int32", "nullable": true}, "approvedByName": {"type": "string", "nullable": true}, "approvedAt": {"type": "string", "format": "date-time", "nullable": true}, "approvalNotes": {"type": "string", "nullable": true}, "rejectedById": {"type": "integer", "format": "int32", "nullable": true}, "rejectedByName": {"type": "string", "nullable": true}, "rejectedAt": {"type": "string", "format": "date-time", "nullable": true}, "rejectionReason": {"type": "string", "nullable": true}, "completedById": {"type": "integer", "format": "int32", "nullable": true}, "completedByName": {"type": "string", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "completionNotes": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestDetail"}, "nullable": true}}, "additionalProperties": false}, "ProductRequestListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "referenceNumber": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "requestDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProductSubGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "groupName": {"type": "string", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "PurchaseOrderDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "purchaseOrderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "totalPrice": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PurchaseOrderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "documentNumber": {"type": "string", "nullable": true}, "supplierId": {"type": "integer", "format": "int32"}, "supplierName": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "orderDate": {"type": "string", "format": "date-time"}, "expectedDeliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "createdBy": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/PurchaseOrderDetailDto"}, "nullable": true}}, "additionalProperties": false}, "RecipeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "yield": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "cost": {"type": "number", "format": "double", "nullable": true}, "isSubRecipe": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "ingredients": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeIngredientDto"}, "nullable": true}}, "additionalProperties": false}, "RecipeIngredientDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "recipeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "cost": {"type": "number", "format": "double", "nullable": true}, "totalCost": {"type": "number", "format": "double", "nullable": true}, "wastagePercentage": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "RecipeListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "yield": {"type": "number", "format": "double"}, "unitName": {"type": "string", "nullable": true}, "cost": {"type": "number", "format": "double", "nullable": true}, "ingredientCount": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "RegisterRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RejectProductRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RejectReasonDto": {"type": "object", "properties": {"reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RejectStockRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StockAdjustmentDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "stockAdjustmentHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "currentQuantity": {"type": "number", "format": "double"}, "adjustmentQuantity": {"type": "number", "format": "double"}, "newQuantity": {"type": "number", "format": "double"}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StockAdjustmentDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StockAdjustmentHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "referenceNumber": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "adjustmentDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "completedById": {"type": "integer", "format": "int32", "nullable": true}, "completedByName": {"type": "string", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/StockAdjustmentDetailDto"}, "nullable": true}}, "additionalProperties": false}, "StockOnHandDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32"}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "lastUpdated": {"type": "string", "format": "date-time"}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StockOnHandSummaryDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "totalQuantity": {"type": "number", "format": "double"}, "averageCostPrice": {"type": "number", "format": "double", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "minStock": {"type": "number", "format": "double", "nullable": true}, "maxStock": {"type": "number", "format": "double", "nullable": true}, "reorderPoint": {"type": "number", "format": "double", "nullable": true}, "isLowStock": {"type": "boolean"}, "isOverStock": {"type": "boolean"}, "needsReorder": {"type": "boolean"}}, "additionalProperties": false}, "StockRequestDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "stockRequestHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "nullable": true}, "total": {"type": "number", "format": "double", "nullable": true}, "deliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StockRequestHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "referenceNumber": {"type": "string", "nullable": true}, "fromCostCenterId": {"type": "integer", "format": "int32"}, "fromCostCenterName": {"type": "string", "nullable": true}, "toCostCenterId": {"type": "integer", "format": "int32"}, "toCostCenterName": {"type": "string", "nullable": true}, "requestDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "approvedById": {"type": "integer", "format": "int32", "nullable": true}, "approvedByName": {"type": "string", "nullable": true}, "approvedAt": {"type": "string", "format": "date-time", "nullable": true}, "completedById": {"type": "integer", "format": "int32", "nullable": true}, "completedByName": {"type": "string", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/StockRequestDetailDto"}, "nullable": true}}, "additionalProperties": false}, "StockTakeDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "stockTakeHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "systemQuantity": {"type": "number", "format": "double"}, "countedQuantity": {"type": "number", "format": "double"}, "variance": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StockTakeHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "referenceNumber": {"type": "string", "nullable": true}, "costCenterId": {"type": "integer", "format": "int32"}, "costCenterName": {"type": "string", "nullable": true}, "stockTakeDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "completedById": {"type": "integer", "format": "int32", "nullable": true}, "completedByName": {"type": "string", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetailDto"}, "nullable": true}}, "additionalProperties": false}, "StockTransferDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "stockTransferHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StockTransferHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "referenceNumber": {"type": "string", "nullable": true}, "fromCostCenterId": {"type": "integer", "format": "int32"}, "fromCostCenterName": {"type": "string", "nullable": true}, "toCostCenterId": {"type": "integer", "format": "int32"}, "toCostCenterName": {"type": "string", "nullable": true}, "transferDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "completedById": {"type": "integer", "format": "int32", "nullable": true}, "completedByName": {"type": "string", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/StockTransferDetailDto"}, "nullable": true}}, "additionalProperties": false}, "StoreDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "locationId": {"type": "integer", "format": "int32"}, "locationName": {"type": "string", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "isSalesPoint": {"type": "boolean"}, "logoPath": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "costCentersCount": {"type": "integer", "format": "int32"}, "costCenters": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterDto"}, "nullable": true}}, "additionalProperties": false}, "SubmitProductRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SubmitStockRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SupplierDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "taxNumber": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "bankAccount": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SupplierListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "taxNumber": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "TaxDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "rate": {"type": "number", "format": "double"}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TransactionDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "transactionHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double", "nullable": true}, "taxRate": {"type": "number", "format": "double", "nullable": true}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "taxName": {"type": "string", "nullable": true}, "taxAmount": {"type": "number", "format": "double", "nullable": true}, "discountPercent": {"type": "number", "format": "double", "nullable": true}, "discountAmount": {"type": "number", "format": "double", "nullable": true}, "totalAmount": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "lineNumber": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TransactionHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "referenceNumber": {"type": "string", "nullable": true}, "transactionTypeId": {"type": "integer", "format": "int32"}, "transactionTypeName": {"type": "string", "nullable": true}, "transactionProcessId": {"type": "integer", "format": "int32"}, "transactionProcessName": {"type": "string", "nullable": true}, "stageTypeId": {"type": "integer", "format": "int32", "nullable": true}, "stageTypeName": {"type": "string", "nullable": true}, "sourceCostCenterId": {"type": "integer", "format": "int32"}, "sourceCostCenterName": {"type": "string", "nullable": true}, "destinationCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "destinationCostCenterName": {"type": "string", "nullable": true}, "supplierId": {"type": "integer", "format": "int32", "nullable": true}, "supplierName": {"type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double", "nullable": true}, "taxAmount": {"type": "number", "format": "double", "nullable": true}, "discountAmount": {"type": "number", "format": "double", "nullable": true}, "netAmount": {"type": "number", "format": "double", "nullable": true}, "createdById": {"type": "integer", "format": "int32", "nullable": true}, "createdByName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "approvedById": {"type": "integer", "format": "int32", "nullable": true}, "approvedByName": {"type": "string", "nullable": true}, "approvedAt": {"type": "string", "format": "date-time", "nullable": true}, "completedById": {"type": "integer", "format": "int32", "nullable": true}, "completedByName": {"type": "string", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "relatedTransactionId": {"type": "integer", "format": "int32", "nullable": true}, "relatedTransactionNumber": {"type": "string", "nullable": true}, "isSkippedStep": {"type": "boolean"}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionDetailDto"}, "nullable": true}}, "additionalProperties": false}, "UnitDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "abbreviation": {"type": "string", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "unitGroupName": {"type": "string", "nullable": true}, "conversionFactor": {"type": "number", "format": "double", "nullable": true}, "baseConversionFactor": {"type": "number", "format": "double"}, "isBaseUnit": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UnitGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "units": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}, "nullable": true}, "unitCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UnitImportDto": {"type": "object", "properties": {"totalRows": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "errorCount": {"type": "integer", "format": "int32"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/UnitImportError"}, "nullable": true}, "successfulUnits": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UnitImportError": {"type": "object", "properties": {"rowNumber": {"type": "integer", "format": "int32"}, "unitName": {"type": "string", "nullable": true}, "unitAbbreviation": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateBarcodeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "barcodeValue": {"type": "string", "nullable": true}, "barcodeType": {"type": "string", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "isPrimary": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateBrandDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateCompanyDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "taxNumber": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateCostCenterDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "storeId": {"type": "integer", "format": "int32"}, "typeId": {"type": "integer", "format": "int32", "nullable": true}, "description": {"type": "string", "nullable": true}, "autoTransfer": {"type": "boolean"}, "isSalesPoint": {"type": "boolean"}, "abbreviation": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateCostCenterTypeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateCreditNoteDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "sourceCostCenterId": {"type": "integer", "format": "int32"}, "supplierId": {"type": "integer", "format": "int32", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "referenceNumber": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateDepartmentDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateGoodsReceiptDetailDto": {"required": ["id", "productId", "receivedQuantity", "unitPrice"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "receivedQuantity": {"minimum": 0.01, "type": "number", "format": "double"}, "unitPrice": {"minimum": 0, "type": "number", "format": "double"}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateGoodsReceiptHeaderDto": {"required": ["costCenterId", "id", "receiptDate", "supplierId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "purchaseOrderId": {"type": "integer", "format": "int32", "nullable": true}, "supplierId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "receiptDate": {"type": "string", "format": "date-time"}, "deliveryNoteNumber": {"type": "string", "nullable": true}, "invoiceNumber": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateLocationDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "companyId": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateProductDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "brandId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "subGroupId": {"type": "integer", "format": "int32", "nullable": true}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "salesPrice": {"type": "number", "format": "double", "nullable": true}, "minStock": {"type": "number", "format": "double", "nullable": true}, "maxStock": {"type": "number", "format": "double", "nullable": true}, "reorderPoint": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isStockItem": {"type": "boolean"}, "isRecipe": {"type": "boolean"}, "hasExpiry": {"type": "boolean"}, "isProduction": {"type": "boolean"}, "isSaleable": {"type": "boolean"}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "salesUnitId": {"type": "integer", "format": "int32", "nullable": true}, "salesUnitConversionFactor": {"type": "number", "format": "double", "nullable": true}, "allowDiscount": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateProductGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateProductOrderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "supplierId": {"type": "integer", "format": "int32"}, "sourceCostCenterId": {"type": "integer", "format": "int32"}, "transactionDate": {"type": "string", "format": "date-time"}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductRequestDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "nullable": true}, "deliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductRequestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "sourceCostCenterId": {"type": "integer", "format": "int32"}, "transactionDate": {"type": "string", "format": "date-time"}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductRequestHeader": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "requestDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateProductSubGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateReceivingDetailDto": {"type": "object", "properties": {"quantity": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitPrice": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateReceivingDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "supplierId": {"type": "integer", "format": "int32"}, "sourceCostCenterId": {"type": "integer", "format": "int32"}, "transactionDate": {"type": "string", "format": "date-time"}, "invoiceNumber": {"type": "string", "nullable": true}, "deliveryNoteNumber": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateRecipeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "productId": {"type": "integer", "format": "int32"}, "yield": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isSubRecipe": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateRecipeIngredientDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "recipeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "notes": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "wastagePercentage": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "UpdateStockAdjustmentDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "currentQuantity": {"type": "number", "format": "double"}, "adjustmentQuantity": {"type": "number", "format": "double"}, "newQuantity": {"type": "number", "format": "double"}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStockAdjustmentHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "adjustmentDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStockRequestDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "nullable": true}, "deliveryDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStockRequestHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fromCostCenterId": {"type": "integer", "format": "int32"}, "toCostCenterId": {"type": "integer", "format": "int32"}, "requestDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStockTakeDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "stockTakeHeaderId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "systemQuantity": {"type": "number", "format": "double"}, "countedQuantity": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStockTakeHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "stockTakeDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStockTransferDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStockTransferHeaderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fromCostCenterId": {"type": "integer", "format": "int32"}, "toCostCenterId": {"type": "integer", "format": "int32"}, "transferDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStoreDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "locationId": {"type": "integer", "format": "int32"}, "isSalesPoint": {"type": "boolean"}, "logoPath": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateSupplierDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "contactPerson": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "taxNumber": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "bankAccount": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateTaxDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "rate": {"type": "number", "format": "double"}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateUnitDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "abbreviation": {"type": "string", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "conversionFactor": {"type": "number", "format": "double", "nullable": true}, "baseConversionFactor": {"type": "number", "format": "double"}, "isBaseUnit": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateUnitGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateUserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "roleId": {"type": "integer", "format": "int32", "nullable": true}, "isAdmin": {"type": "boolean"}, "isActive": {"type": "boolean"}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "roleId": {"type": "integer", "format": "int32", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "isAdmin": {"type": "boolean"}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "UserListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UserPermissionsDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "canViewTransactions": {"type": "boolean"}, "canCreateTransactions": {"type": "boolean"}, "canEditTransactions": {"type": "boolean"}, "canDeleteTransactions": {"type": "boolean"}, "canApproveTransactions": {"type": "boolean"}, "hasFullReceivePermission": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}