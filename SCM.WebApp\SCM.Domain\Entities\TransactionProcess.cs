using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class TransactionProcess : BaseEntity
{
    public string ProcessNumber { get; set; } = string.Empty; // Maps to ProcessNumber in DB

    // Name property returns ProcessNumber for compatibility with code
    public string Name
    {
        get => ProcessNumber;
        set => ProcessNumber = value;
    }

    public string? Description { get; set; }
    public int? CreatedById { get; set; } // Added to match DB

    // Navigation properties
    public virtual User? CreatedBy { get; set; }
    public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; } = new List<TransactionHeader>();
}
