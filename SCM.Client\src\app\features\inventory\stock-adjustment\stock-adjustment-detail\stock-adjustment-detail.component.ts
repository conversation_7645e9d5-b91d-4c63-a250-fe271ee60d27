import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { StockAdjustmentService } from '../../../../core/services/stock-adjustment.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductService } from '../../../../core/services/product.service';
import { StockService } from '../../../../core/services/stock.service';
import { UnitService } from '../../../../core/services/unit.service';
import { 
  StockAdjustmentHeader, 
  StockAdjustmentDetail, 
  CreateStockAdjustmentHeader, 
  CreateStockAdjustmentDetail,
  AdjustmentReason
} from '../../../../core/models/stock-adjustment.model';
import { CostCenter } from '../../../../core/models/cost-center.model';
import { Product } from '../../../../core/models/product.model';
import { StockOnHand } from '../../../../core/models/stock.model';
import { Unit } from '../../../../core/models/unit.model';
import { Observable, forkJoin, of, map, startWith, switchMap, finalize } from 'rxjs';

@Component({
  selector: 'app-stock-adjustment-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatAutocompleteModule,
    MatTooltipModule,
    MatDividerModule
  ],
  templateUrl: './stock-adjustment-detail.component.html',
  styleUrls: ['./stock-adjustment-detail.component.scss']
})
export class StockAdjustmentDetailComponent implements OnInit {
  adjustmentForm!: FormGroup;
  isLoading = false;
  isEditMode = false;
  stockAdjustmentId: number | null = null;
  stockAdjustment: StockAdjustmentHeader | null = null;
  
  costCenters: CostCenter[] = [];
  products: Product[] = [];
  units: Unit[] = [];
  
  adjustmentReasons = Object.values(AdjustmentReason);
  
  displayedColumns: string[] = [
    'productName',
    'currentQuantity',
    'adjustmentQuantity',
    'newQuantity',
    'reason',
    'actions'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private stockAdjustmentService: StockAdjustmentService,
    private costCenterService: CostCenterService,
    private productService: ProductService,
    private stockService: StockService,
    private unitService: UnitService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadData();
    
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.stockAdjustmentId = +id;
        this.isEditMode = true;
        this.loadStockAdjustment(this.stockAdjustmentId);
      }
    });
  }

  get detailsFormArray(): FormArray {
    return this.adjustmentForm.get('details') as FormArray;
  }

  initForm(): void {
    this.adjustmentForm = this.fb.group({
      costCenterId: ['', Validators.required],
      adjustmentDate: [new Date(), Validators.required],
      notes: [''],
      details: this.fb.array([])
    });
  }

  loadData(): void {
    this.isLoading = true;
    
    forkJoin({
      costCenters: this.costCenterService.getAllCostCenters(),
      products: this.productService.getAllProducts(),
      units: this.unitService.getAllUnits()
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe({
      next: (data) => {
        this.costCenters = data.costCenters;
        this.products = data.products;
        this.units = data.units;
        
        // Add an empty detail if creating a new adjustment
        if (!this.isEditMode) {
          this.addDetail();
        }
      },
      error: (error) => {
        console.error('Error loading data', error);
        this.snackBar.open('Error loading data. Please try again.', 'Close', {
          duration: 5000
        });
      }
    });
  }

  loadStockAdjustment(id: number): void {
    this.isLoading = true;
    
    this.stockAdjustmentService.getStockAdjustmentById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (stockAdjustment) => {
          this.stockAdjustment = stockAdjustment;
          
          // Patch form values
          this.adjustmentForm.patchValue({
            costCenterId: stockAdjustment.costCenterId,
            adjustmentDate: new Date(stockAdjustment.adjustmentDate),
            notes: stockAdjustment.notes
          });
          
          // Clear existing details
          while (this.detailsFormArray.length) {
            this.detailsFormArray.removeAt(0);
          }
          
          // Add details
          if (stockAdjustment.details && stockAdjustment.details.length > 0) {
            stockAdjustment.details.forEach(detail => {
              this.addDetail(detail);
            });
          } else {
            this.addDetail();
          }
          
          // Disable form if not in Draft status
          if (stockAdjustment.status !== 'Draft') {
            this.adjustmentForm.disable();
          }
        },
        error: (error) => {
          console.error('Error loading stock adjustment', error);
          this.snackBar.open('Error loading stock adjustment. Please try again.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  addDetail(detail?: StockAdjustmentDetail): void {
    const detailForm = this.fb.group({
      id: [detail?.id || 0],
      productId: [detail?.productId || '', Validators.required],
      batchId: [detail?.batchId || null],
      unitId: [detail?.unitId || null],
      currentQuantity: [detail?.currentQuantity || 0, Validators.required],
      adjustmentQuantity: [detail?.adjustmentQuantity || 0, Validators.required],
      newQuantity: [detail?.newQuantity || 0, Validators.required],
      costPrice: [detail?.costPrice || null],
      reason: [detail?.reason || '', Validators.required],
      notes: [detail?.notes || '']
    });
    
    // Add listeners for quantity changes
    detailForm.get('currentQuantity')?.valueChanges.subscribe(() => this.calculateNewQuantity(detailForm));
    detailForm.get('adjustmentQuantity')?.valueChanges.subscribe(() => this.calculateNewQuantity(detailForm));
    
    this.detailsFormArray.push(detailForm);
  }

  removeDetail(index: number): void {
    this.detailsFormArray.removeAt(index);
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.name : '';
  }

  getUnitName(unitId: number): string {
    const unit = this.units.find(u => u.id === unitId);
    return unit ? unit.name : '';
  }

  getCostCenterName(costCenterId: number): string {
    const costCenter = this.costCenters.find(cc => cc.id === costCenterId);
    return costCenter ? costCenter.name : '';
  }

  checkStockAvailability(productId: number, index: number): void {
    const costCenterId = this.adjustmentForm.get('costCenterId')?.value;
    
    if (productId && costCenterId) {
      this.isLoading = true;
      
      this.stockService.getStockByProductAndCostCenter(productId, costCenterId)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (stock) => {
            const currentQuantity = stock ? stock.quantity : 0;
            const detailForm = this.detailsFormArray.at(index) as FormGroup;
            
            // Set the current quantity
            detailForm.get('currentQuantity')?.setValue(currentQuantity);
            
            // Set the unit to match the product's default unit
            const product = this.products.find(p => p.id === productId);
            if (product && product.unitId) {
              detailForm.get('unitId')?.setValue(product.unitId);
            }
            
            // Set the cost price
            detailForm.get('costPrice')?.setValue(stock ? stock.costPrice : null);
          },
          error: (error) => {
            console.error('Error checking stock availability', error);
          }
        });
    }
  }

  calculateNewQuantity(detailForm: FormGroup): void {
    const currentQuantity = detailForm.get('currentQuantity')?.value || 0;
    const adjustmentQuantity = detailForm.get('adjustmentQuantity')?.value || 0;
    const newQuantity = currentQuantity + adjustmentQuantity;
    
    detailForm.get('newQuantity')?.setValue(newQuantity);
    
    // Validate that new quantity is not negative
    if (newQuantity < 0) {
      detailForm.get('adjustmentQuantity')?.setErrors({ negativeStock: true });
    }
  }

  saveStockAdjustment(): void {
    if (this.adjustmentForm.invalid) {
      this.markFormGroupTouched(this.adjustmentForm);
      this.snackBar.open('Please fix the errors in the form before saving.', 'Close', {
        duration: 5000
      });
      return;
    }
    
    this.isLoading = true;
    
    if (this.isEditMode && this.stockAdjustmentId) {
      // Update existing stock adjustment
      const updateData = {
        id: this.stockAdjustmentId,
        ...this.adjustmentForm.value,
        status: 'Draft'
      };
      
      this.stockAdjustmentService.updateStockAdjustment(this.stockAdjustmentId, updateData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock adjustment updated successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-adjustments']);
          },
          error: (error) => {
            console.error('Error updating stock adjustment', error);
            this.snackBar.open('Error updating stock adjustment: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    } else {
      // Create new stock adjustment
      const createData: CreateStockAdjustmentHeader = {
        ...this.adjustmentForm.value
      };
      
      this.stockAdjustmentService.createStockAdjustment(createData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (result) => {
            this.snackBar.open('Stock adjustment created successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-adjustments']);
          },
          error: (error) => {
            console.error('Error creating stock adjustment', error);
            this.snackBar.open('Error creating stock adjustment: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  completeStockAdjustment(): void {
    if (!this.stockAdjustmentId) return;
    
    if (confirm('Are you sure you want to complete this stock adjustment? This will update inventory levels.')) {
      this.isLoading = true;
      
      this.stockAdjustmentService.completeStockAdjustment(this.stockAdjustmentId, { id: this.stockAdjustmentId })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock adjustment completed successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-adjustments']);
          },
          error: (error) => {
            console.error('Error completing stock adjustment', error);
            this.snackBar.open('Error completing stock adjustment: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancelStockAdjustment(): void {
    if (!this.stockAdjustmentId) return;
    
    if (confirm('Are you sure you want to cancel this stock adjustment?')) {
      this.isLoading = true;
      
      this.stockAdjustmentService.cancelStockAdjustment(this.stockAdjustmentId)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock adjustment cancelled successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-adjustments']);
          },
          error: (error) => {
            console.error('Error cancelling stock adjustment', error);
            this.snackBar.open('Error cancelling stock adjustment: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancel(): void {
    this.router.navigate(['/inventory/stock-adjustments']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        for (let i = 0; i < control.length; i++) {
          const arrayControl = control.at(i);
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        }
      }
    });
  }
}
