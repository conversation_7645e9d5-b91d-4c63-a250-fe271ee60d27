.page-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 999;
}

.tab-content {
  padding: 20px 0;
}

.page-subheader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.form-row mat-form-field {
  flex: 1;
  min-width: 200px;
}

.filter-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.filter-row mat-form-field {
  width: 300px;
}

.full-width {
  width: 100%;
}

.reference-number {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 150px;
}

.reference-value {
  font-weight: bold;
  font-size: 1.2em;
  color: #1976d2;
}

.table-container {
  margin: 20px 0;
  overflow-x: auto;
  border-radius: 4px;
}

.no-data-message {
  padding: 20px;
  text-align: center;
  color: #757575;
}

table {
  width: 100%;
}

.table-form-field {
  width: 100%;
  margin: 0;
  font-size: 14px;
}

.table-form-field ::ng-deep .mat-mdc-form-field-infix {
  padding: 8px 0;
  width: auto;
}

.table-form-field ::ng-deep .mat-mdc-text-field-wrapper {
  padding: 0 8px;
}

.negative-diff {
  color: #f44336;
  font-weight: 500;
}

.positive-diff {
  color: #4caf50;
  font-weight: 500;
}

.history-table {
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .page-subheader {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: 10px;
  }

  .form-row {
    flex-direction: column;
    align-items: stretch;
  }

  .reference-number {
    align-items: flex-start;
    margin-top: 10px;
  }
}
