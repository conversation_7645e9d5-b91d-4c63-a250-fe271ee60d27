.container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 999;
}

.form-card {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;

  mat-form-field {
    flex: 1;
    min-width: 200px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.full-width {
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px 0 16px;

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }
}

.detail-actions {
  display: flex;
  justify-content: flex-end;
}

.no-items {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
  color: #666;
  margin-bottom: 20px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.status-info {
  margin-top: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-draft {
  background-color: #e0e0e0;
  color: #616161;
}

.status-submitted {
  background-color: #bbdefb;
  color: #1976d2;
}

.status-approved {
  background-color: #c8e6c9;
  color: #388e3c;
}

.status-rejected {
  background-color: #ffcdd2;
  color: #d32f2f;
}

.status-completed {
  background-color: #c8e6c9;
  color: #388e3c;
}

.status-cancelled {
  background-color: #ffcdd2;
  color: #d32f2f;
}

.status-details {
  font-size: 14px;
  color: #666;

  p {
    margin: 0;
  }
}
