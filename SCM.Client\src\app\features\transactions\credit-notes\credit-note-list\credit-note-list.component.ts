import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { CreditNoteService } from '../../../../core/services/credit-note.service';
import { CreditNoteListItem, CreditNoteStatus } from '../../../../core/models/credit-note.model';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-credit-note-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatMenuModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatDividerModule
  ],
  templateUrl: './credit-note-list.component.html',
  styleUrls: ['./credit-note-list.component.scss']
})
export class CreditNoteListComponent implements OnInit {
  creditNotes: CreditNoteListItem[] = [];
  loading = false;
  displayedColumns: string[] = [
    'transactionNumber',
    'referenceNumber',
    'supplierName',
    'sourceCostCenterName',
    'transactionDate',
    'status',
    'reason',
    'totalAmount',
    'relatedTransactionNumber',
    'actions'
  ];

  constructor(
    private creditNoteService: CreditNoteService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadCreditNotes();
  }

  loadCreditNotes(): void {
    this.loading = true;
    this.creditNoteService.getAll().subscribe({
      next: (creditNotes) => {
        this.creditNotes = creditNotes;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading credit notes:', error);
        this.snackBar.open('Error loading credit notes', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case CreditNoteStatus.Draft:
        return 'primary';
      case CreditNoteStatus.Completed:
        return 'accent';
      case CreditNoteStatus.Cancelled:
        return 'warn';
      default:
        return '';
    }
  }

  completeCreditNote(id: number): void {
    if (confirm('Are you sure you want to complete this credit note? This action cannot be undone.')) {
      this.creditNoteService.complete(id).subscribe({
        next: () => {
          this.snackBar.open('Credit note completed successfully', 'Close', { duration: 3000 });
          this.loadCreditNotes();
        },
        error: (error) => {
          console.error('Error completing credit note:', error);
          this.snackBar.open('Error completing credit note', 'Close', { duration: 3000 });
        }
      });
    }
  }

  cancelCreditNote(id: number): void {
    const reason = prompt('Please provide a reason for cancellation:');
    if (reason !== null) {
      this.creditNoteService.cancel(id, reason).subscribe({
        next: () => {
          this.snackBar.open('Credit note cancelled successfully', 'Close', { duration: 3000 });
          this.loadCreditNotes();
        },
        error: (error) => {
          console.error('Error cancelling credit note:', error);
          this.snackBar.open('Error cancelling credit note', 'Close', { duration: 3000 });
        }
      });
    }
  }

  deleteCreditNote(id: number): void {
    if (confirm('Are you sure you want to delete this credit note? This action cannot be undone.')) {
      this.creditNoteService.delete(id).subscribe({
        next: () => {
          this.snackBar.open('Credit note deleted successfully', 'Close', { duration: 3000 });
          this.loadCreditNotes();
        },
        error: (error) => {
          console.error('Error deleting credit note:', error);
          this.snackBar.open('Error deleting credit note', 'Close', { duration: 3000 });
        }
      });
    }
  }
}
