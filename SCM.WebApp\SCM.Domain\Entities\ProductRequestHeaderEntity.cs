using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class ProductRequestHeaderEntity : BaseEntity
{
    public string ReferenceNumber { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public DateTime RequestDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft"; // Draft, Submitted, Approved, Rejected, Completed, Cancelled
    
    public int? CreatedById { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public int? SubmittedById { get; set; }
    public DateTime? SubmittedAt { get; set; }
    
    public int? ApprovedById { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovalNotes { get; set; }
    
    public int? RejectedById { get; set; }
    public DateTime? RejectedAt { get; set; }
    public string? RejectionReason { get; set; }
    
    public int? CompletedById { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletionNotes { get; set; }
    
    public DateTime? UpdatedAt { get; set; }
    
    // Navigation properties
    public virtual CostCenter CostCenter { get; set; } = null!;
    public virtual User? CreatedBy { get; set; }
    public virtual User? SubmittedBy { get; set; }
    public virtual User? ApprovedBy { get; set; }
    public virtual User? RejectedBy { get; set; }
    public virtual User? CompletedBy { get; set; }
    public virtual ICollection<ProductRequestDetailEntity> Details { get; set; } = new List<ProductRequestDetailEntity>();
}
