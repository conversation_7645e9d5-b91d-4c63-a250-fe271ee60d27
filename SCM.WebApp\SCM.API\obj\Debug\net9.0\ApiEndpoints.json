[{"ContainingType": "SCM.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SCM.API.Models.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.API.Models.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SCM.API.Models.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.API.Models.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SCM.API.Models.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.AuthController", "Method": "Test", "RelativePath": "api/Auth/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.AuthController", "Method": "TestDatabase", "RelativePath": "api/Auth/test-db", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "GetAll", "RelativePath": "api/Barcodes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.BarcodeDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "Create", "RelativePath": "api/Barcodes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createBarcodeDto", "Type": "SCM.Application.DTOs.CreateBarcodeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.BarcodeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "GetById", "RelativePath": "api/Barcodes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.BarcodeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "Update", "RelativePath": "api/Barcodes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateBarcodeDto", "Type": "SCM.Application.DTOs.UpdateBarcodeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "Delete", "RelativePath": "api/Barcodes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "GetByProductId", "RelativePath": "api/Barcodes/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.BarcodeDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "GetPrimaryByProductId", "RelativePath": "api/Barcodes/product/{productId}/primary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.BarcodeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Barcodes/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.BarcodesController", "Method": "GetByValue", "RelativePath": "api/Barcodes/value/{value}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "value", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.BarcodeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BrandsController", "Method": "GetAll", "RelativePath": "api/Brands", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.BrandDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BrandsController", "Method": "Create", "RelativePath": "api/Brands", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createBrandDto", "Type": "SCM.Application.DTOs.CreateBrandDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.BrandDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BrandsController", "Method": "GetById", "RelativePath": "api/Brands/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.BrandDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.BrandsController", "Method": "Update", "RelativePath": "api/Brands/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateBrandDto", "Type": "SCM.Application.DTOs.UpdateBrandDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.BrandsController", "Method": "Delete", "RelativePath": "api/Brands/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Brands/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CompaniesController", "Method": "GetAll", "RelativePath": "api/Companies", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.CompanyDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CompaniesController", "Method": "Create", "RelativePath": "api/Companies", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCompanyDto", "Type": "SCM.Application.DTOs.CreateCompanyDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CompanyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CompaniesController", "Method": "GetById", "RelativePath": "api/Companies/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CompanyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CompaniesController", "Method": "Update", "RelativePath": "api/Companies/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateCompanyDto", "Type": "SCM.Application.DTOs.UpdateCompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CompaniesController", "Method": "Delete", "RelativePath": "api/Companies/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CompaniesController", "Method": "GetWithLocations", "RelativePath": "api/Companies/{id}/locations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CompanyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Companies/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "GetAll", "RelativePath": "api/CostCenters", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.CostCenterDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "Create", "RelativePath": "api/CostCenters", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCostCenterDto", "Type": "SCM.Application.DTOs.CreateCostCenterDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CostCenterDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "GetById", "RelativePath": "api/CostCenters/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CostCenterDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "Update", "RelativePath": "api/CostCenters/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateCostCenterDto", "Type": "SCM.Application.DTOs.UpdateCostCenterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "Delete", "RelativePath": "api/CostCenters/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "Activate", "RelativePath": "api/CostCenters/{id}/activate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "Deactivate", "RelativePath": "api/CostCenters/{id}/deactivate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "GetByLocationId", "RelativePath": "api/CostCenters/location/{locationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "locationId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.CostCenterDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "GetSalesPoints", "RelativePath": "api/CostCenters/sales-points", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.CostCenterDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "GetByStoreId", "RelativePath": "api/CostCenters/store/{storeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.CostCenterDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/CostCenters/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCentersController", "Method": "GetByTypeId", "RelativePath": "api/CostCenters/type/{typeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "typeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.CostCenterDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCenterTypesController", "Method": "GetAll", "RelativePath": "api/CostCenterTypes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.CostCenterTypeDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCenterTypesController", "Method": "Create", "RelativePath": "api/CostCenterTypes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCostCenterTypeDto", "Type": "SCM.Application.DTOs.CreateCostCenterTypeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CostCenterTypeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCenterTypesController", "Method": "GetById", "RelativePath": "api/CostCenterTypes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CostCenterTypeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CostCenterTypesController", "Method": "Update", "RelativePath": "api/CostCenterTypes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateCostCenterTypeDto", "Type": "SCM.Application.DTOs.UpdateCostCenterTypeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCenterTypesController", "Method": "Delete", "RelativePath": "api/CostCenterTypes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CostCenterTypesController", "Method": "GetWithCostCenters", "RelativePath": "api/CostCenterTypes/{id}/cost-centers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.CostCenterTypeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/CostCenterTypes/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CreditNotesController", "Method": "GetAll", "RelativePath": "api/CreditNotes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CreditNotesController", "Method": "Create", "RelativePath": "api/CreditNotes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCreditNoteDto", "Type": "SCM.Application.DTOs.CreateCreditNoteDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CreditNotesController", "Method": "GetById", "RelativePath": "api/CreditNotes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.CreditNotesController", "Method": "Update", "RelativePath": "api/CreditNotes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateCreditNoteDto", "Type": "SCM.Application.DTOs.UpdateCreditNoteDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CreditNotesController", "Method": "Cancel", "RelativePath": "api/CreditNotes/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "reason", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.CreditNotesController", "Method": "Complete", "RelativePath": "api/CreditNotes/{id}/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/CreditNotes/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.DepartmentsController", "Method": "GetAll", "RelativePath": "api/Departments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.DepartmentDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.DepartmentsController", "Method": "Create", "RelativePath": "api/Departments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDepartmentDto", "Type": "SCM.Application.DTOs.CreateDepartmentDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.DepartmentDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.DepartmentsController", "Method": "GetById", "RelativePath": "api/Departments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.DepartmentDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.DepartmentsController", "Method": "Update", "RelativePath": "api/Departments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDepartmentDto", "Type": "SCM.Application.DTOs.UpdateDepartmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.DepartmentsController", "Method": "Delete", "RelativePath": "api/Departments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Departments/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetAll", "RelativePath": "api/GoodsReceipts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.GoodsReceiptListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "Create", "RelativePath": "api/GoodsReceipts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createGoodsReceiptHeaderDto", "Type": "SCM.Application.DTOs.CreateGoodsReceiptHeaderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.GoodsReceiptHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetDetails", "RelativePath": "api/GoodsReceipts/{goodsReceiptHeaderId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "goodsReceiptHeaderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.GoodsReceiptDetailDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "CreateDetail", "RelativePath": "api/GoodsReceipts/{goodsReceiptHeaderId}/details", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "goodsReceiptHeaderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createGoodsReceiptDetailDto", "Type": "SCM.Application.DTOs.CreateGoodsReceiptDetailDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.GoodsReceiptDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetById", "RelativePath": "api/GoodsReceipts/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.GoodsReceiptHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "Update", "RelativePath": "api/GoodsReceipts/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateGoodsReceiptHeaderDto", "Type": "SCM.Application.DTOs.UpdateGoodsReceiptHeaderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "Delete", "RelativePath": "api/GoodsReceipts/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "Cancel", "RelativePath": "api/GoodsReceipts/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "Complete", "RelativePath": "api/GoodsReceipts/{id}/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "completeGoodsReceiptDto", "Type": "SCM.Application.DTOs.CompleteGoodsReceiptDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetByCostCenterId", "RelativePath": "api/GoodsReceipts/cost-center/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.GoodsReceiptListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetDetailById", "RelativePath": "api/GoodsReceipts/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.GoodsReceiptDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "UpdateDetail", "RelativePath": "api/GoodsReceipts/details/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateGoodsReceiptDetailDto", "Type": "SCM.Application.DTOs.UpdateGoodsReceiptDetailDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "DeleteDetail", "RelativePath": "api/GoodsReceipts/details/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetByPurchaseOrderId", "RelativePath": "api/GoodsReceipts/purchase-order/{purchaseOrderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "purchaseOrderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.GoodsReceiptListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetPurchaseOrderForReceiving", "RelativePath": "api/GoodsReceipts/purchase-order/{purchaseOrderId}/for-receiving", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "purchaseOrderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.PurchaseOrderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetByStatus", "RelativePath": "api/GoodsReceipts/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.GoodsReceiptListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.GoodsReceiptsController", "Method": "GetBySupplierId", "RelativePath": "api/GoodsReceipts/supplier/{supplierId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "supplierId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.GoodsReceiptListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/GoodsReceipts/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "GetAll", "RelativePath": "api/Locations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.API.DTOs.LocationSimpleDto, SCM.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "Create", "RelativePath": "api/Locations", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createLocationDto", "Type": "SCM.Application.DTOs.CreateLocationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.API.DTOs.LocationSimpleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "GetById", "RelativePath": "api/Locations/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.API.DTOs.LocationSimpleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "Update", "RelativePath": "api/Locations/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateLocationDto", "Type": "SCM.Application.DTOs.UpdateLocationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "Delete", "RelativePath": "api/Locations/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "Activate", "RelativePath": "api/Locations/{id}/activate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "GetWithCostCenters", "RelativePath": "api/Locations/{id}/cost-centers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.API.DTOs.LocationSimpleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "Deactivate", "RelativePath": "api/Locations/{id}/deactivate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "GetWithStores", "RelativePath": "api/Locations/{id}/stores", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.API.DTOs.LocationSimpleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.LocationsController", "Method": "GetByCompanyId", "RelativePath": "api/Locations/company/{companyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.API.DTOs.LocationSimpleDto, SCM.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Locations/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.PermissionsController", "Method": "GetAll", "RelativePath": "api/Permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.PermissionDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PermissionsController", "Method": "GetById", "RelativePath": "api/Permissions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.PermissionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PermissionsController", "Method": "GetByCategory", "RelativePath": "api/Permissions/by-category", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.PermissionCategoryDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PermissionsController", "Method": "GetByName", "RelativePath": "api/Permissions/by-name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.PermissionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PermissionsController", "Method": "InitializeDefaultPermissions", "RelativePath": "api/Permissions/initialize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Permissions/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductGroupsController", "Method": "GetAll", "RelativePath": "api/ProductGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductGroupDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductGroupsController", "Method": "Create", "RelativePath": "api/ProductGroups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createProductGroupDto", "Type": "SCM.Application.DTOs.CreateProductGroupDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductGroupsController", "Method": "GetById", "RelativePath": "api/ProductGroups/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductGroupsController", "Method": "Update", "RelativePath": "api/ProductGroups/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateProductGroupDto", "Type": "SCM.Application.DTOs.UpdateProductGroupDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductGroupsController", "Method": "Delete", "RelativePath": "api/ProductGroups/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductGroupsController", "Method": "GetWithSubGroups", "RelativePath": "api/ProductGroups/{id}/with-subgroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductGroupsController", "Method": "GetByDepartmentId", "RelativePath": "api/ProductGroups/department/{departmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "departmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductGroupDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/ProductGroups/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "GetAll", "RelativePath": "api/ProductRequests", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductRequestListItem, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Create", "RelativePath": "api/ProductRequests", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createProductRequest", "Type": "SCM.Application.DTOs.CreateProductRequestHeader", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductRequestHeader", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "GetById", "RelativePath": "api/ProductRequests/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductRequestHeader", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Update", "RelativePath": "api/ProductRequests/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateProductRequest", "Type": "SCM.Application.DTOs.UpdateProductRequestHeader", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Delete", "RelativePath": "api/ProductRequests/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "GetDetails", "RelativePath": "api/ProductRequests/{productRequestId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productRequestId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductRequestDetail, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "AddDetail", "RelativePath": "api/ProductRequests/{productRequestId}/details", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productRequestId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createDetail", "Type": "SCM.Application.DTOs.CreateProductRequestDetail", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductRequestDetail", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Approve", "RelativePath": "api/ProductRequests/approve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "approveDto", "Type": "SCM.Application.DTOs.ApproveProductRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Cancel", "RelativePath": "api/ProductRequests/cancel/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Complete", "RelativePath": "api/ProductRequests/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "completeDto", "Type": "SCM.Application.DTOs.CompleteProductRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "GetByCostCenter", "RelativePath": "api/ProductRequests/costcenter/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductRequestListItem, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "UpdateDetail", "RelativePath": "api/ProductRequests/details/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDetail", "Type": "SCM.Application.DTOs.UpdateProductRequestDetail", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "DeleteDetail", "RelativePath": "api/ProductRequests/details/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Reject", "RelativePath": "api/ProductRequests/reject", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "rejectDto", "Type": "SCM.Application.DTOs.RejectProductRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "GetByStatus", "RelativePath": "api/ProductRequests/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductRequestListItem, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductRequestsController", "Method": "Submit", "RelativePath": "api/ProductRequests/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "submitDto", "Type": "SCM.Application.DTOs.SubmitProductRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/ProductRequests/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "GetAll", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "Create", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createProductDto", "Type": "SCM.Application.DTOs.CreateProductDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "GetById", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "Update", "RelativePath": "api/Products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateProductDto", "Type": "SCM.Application.DTOs.UpdateProductDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "Delete", "RelativePath": "api/Products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "GetByCode", "RelativePath": "api/Products/code/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "GetByDepartmentId", "RelativePath": "api/Products/department/{departmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "departmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "GetByGroupId", "RelativePath": "api/Products/group/{groupId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "ImportProducts", "RelativePath": "api/Products/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductImportDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "DownloadTemplate", "RelativePath": "api/Products/import/template", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "Search", "RelativePath": "api/Products/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "term", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductsController", "Method": "GetBySubGroupId", "RelativePath": "api/Products/subgroup/{subGroupId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "subGroupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Products/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductSubGroupsController", "Method": "GetAll", "RelativePath": "api/ProductSubGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductSubGroupDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductSubGroupsController", "Method": "Create", "RelativePath": "api/ProductSubGroups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createProductSubGroupDto", "Type": "SCM.Application.DTOs.CreateProductSubGroupDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductSubGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductSubGroupsController", "Method": "GetById", "RelativePath": "api/ProductSubGroups/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.ProductSubGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ProductSubGroupsController", "Method": "Update", "RelativePath": "api/ProductSubGroups/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateProductSubGroupDto", "Type": "SCM.Application.DTOs.UpdateProductSubGroupDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductSubGroupsController", "Method": "Delete", "RelativePath": "api/ProductSubGroups/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ProductSubGroupsController", "Method": "GetByGroupId", "RelativePath": "api/ProductSubGroups/group/{groupId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.ProductSubGroupDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/ProductSubGroups/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "GetAll", "RelativePath": "api/PurchaseOrders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "Create", "RelativePath": "api/PurchaseOrders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createProductOrderDto", "Type": "SCM.Application.DTOs.CreateProductOrderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "GetById", "RelativePath": "api/PurchaseOrders/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "Update", "RelativePath": "api/PurchaseOrders/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateProductOrderDto", "Type": "SCM.Application.DTOs.UpdateProductOrderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "Approve", "RelativePath": "api/PurchaseOrders/{id}/approve", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "Cancel", "RelativePath": "api/PurchaseOrders/{id}/cancel", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "cancelReasonDto", "Type": "SCM.API.Controllers.CancelReasonDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "Reject", "RelativePath": "api/PurchaseOrders/{id}/reject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "rejectReasonDto", "Type": "SCM.API.Controllers.RejectReasonDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "Submit", "RelativePath": "api/PurchaseOrders/{id}/submit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "GetByCostCenter", "RelativePath": "api/PurchaseOrders/costcenter/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "CreateFromRequest", "RelativePath": "api/PurchaseOrders/from-request/{productRequestId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productRequestId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createProductOrderDto", "Type": "SCM.Application.DTOs.CreateProductOrderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "GetByStatus", "RelativePath": "api/PurchaseOrders/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.PurchaseOrdersController", "Method": "GetBySupplier", "RelativePath": "api/PurchaseOrders/supplier/{supplierId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "supplierId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/PurchaseOrders/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "GetAll", "RelativePath": "api/Receiving", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "Create", "RelativePath": "api/Receiving", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createReceivingDto", "Type": "SCM.Application.DTOs.CreateReceivingDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "GetById", "RelativePath": "api/Receiving/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "Update", "RelativePath": "api/Receiving/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateReceivingDto", "Type": "SCM.Application.DTOs.UpdateReceivingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "Approve", "RelativePath": "api/Receiving/{id}/approve", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "CancelReceiving", "RelativePath": "api/Receiving/{id}/cancel", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "Complete", "RelativePath": "api/Receiving/{id}/complete", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "Reject", "RelativePath": "api/Receiving/{id}/reject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "Submit", "RelativePath": "api/Receiving/{id}/submit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ReceivingController", "Method": "CreateFromOrder", "RelativePath": "api/Receiving/from-order/{productOrderId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productOrderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createReceivingDto", "Type": "SCM.Application.DTOs.CreateReceivingDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Receiving/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "GetAll", "RelativePath": "api/Recipes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.RecipeListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "Create", "RelativePath": "api/Recipes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createRecipeDto", "Type": "SCM.Application.DTOs.CreateRecipeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.RecipeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "GetById", "RelativePath": "api/Recipes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.RecipeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "Update", "RelativePath": "api/Recipes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateRecipeDto", "Type": "SCM.Application.DTOs.UpdateRecipeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "Delete", "RelativePath": "api/Recipes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "CalculateCost", "RelativePath": "api/Recipes/{id}/calculate-cost", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Decimal", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "AddIngredient", "RelativePath": "api/Recipes/{recipeId}/ingredients", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "recipeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createRecipeIngredientDto", "Type": "SCM.Application.DTOs.CreateRecipeIngredientDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.RecipeIngredientDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "RemoveIngredient", "RelativePath": "api/Recipes/{recipeId}/ingredients/{ingredientId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "recipeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "ingredientId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "GetActive", "RelativePath": "api/Recipes/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.RecipeListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "UpdateIngredient", "RelativePath": "api/Recipes/ingredients/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateRecipeIngredientDto", "Type": "SCM.Application.DTOs.UpdateRecipeIngredientDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "GetByProductId", "RelativePath": "api/Recipes/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.RecipeListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.RecipesController", "Method": "GetSubRecipes", "RelativePath": "api/Recipes/subrecipes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.RecipeListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Recipes/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetAll", "RelativePath": "api/Stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "AdjustStock", "RelativePath": "api/Stock/adjust", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockAdjustmentDto", "Type": "SCM.Application.DTOs.StockAdjustmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetByCostCenterId", "RelativePath": "api/Stock/costcenter/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetExpiringStock", "RelativePath": "api/Stock/expiring/{daysToExpiry}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "daysToExpiry", "Type": "System.Int32", "IsRequired": true}, {"Name": "costCenterId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetStockItem", "RelativePath": "api/Stock/item", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": false}, {"Name": "costCenterId", "Type": "System.Int32", "IsRequired": false}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockOnHandDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetLowStock", "RelativePath": "api/Stock/lowstock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandSummaryDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetOverStock", "RelativePath": "api/Stock/overstock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandSummaryDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetByProductId", "RelativePath": "api/Stock/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetReorderItems", "RelativePath": "api/Stock/reorder", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandSummaryDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockController", "Method": "GetSummary", "RelativePath": "api/Stock/summary/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockOnHandSummaryDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Stock/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "GetAll", "RelativePath": "api/StockAdjustments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockAdjustmentHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "Create", "RelativePath": "api/StockAdjustments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createStockAdjustmentHeaderDto", "Type": "SCM.Application.DTOs.CreateStockAdjustmentHeaderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockAdjustmentHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "GetById", "RelativePath": "api/StockAdjustments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockAdjustmentHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "Update", "RelativePath": "api/StockAdjustments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockAdjustmentHeaderDto", "Type": "SCM.Application.DTOs.UpdateStockAdjustmentHeaderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "Delete", "RelativePath": "api/StockAdjustments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "Cancel", "RelativePath": "api/StockAdjustments/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "Complete", "RelativePath": "api/StockAdjustments/{id}/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "completeStockAdjustmentDto", "Type": "SCM.Application.DTOs.CompleteStockAdjustmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "GetDetails", "RelativePath": "api/StockAdjustments/{stockAdjustmentHeaderId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockAdjustmentHeaderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockAdjustmentDetailDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "CreateDetail", "RelativePath": "api/StockAdjustments/{stockAdjustmentHeaderId}/details", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockAdjustmentHeaderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createStockAdjustmentDetailDto", "Type": "SCM.Application.DTOs.CreateStockAdjustmentDetailDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockAdjustmentDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "GetByCostCenterId", "RelativePath": "api/StockAdjustments/cost-center/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockAdjustmentHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "GetDetailById", "RelativePath": "api/StockAdjustments/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockAdjustmentDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "UpdateDetail", "RelativePath": "api/StockAdjustments/details/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockAdjustmentDetailDto", "Type": "SCM.Application.DTOs.UpdateStockAdjustmentDetailDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "DeleteDetail", "RelativePath": "api/StockAdjustments/details/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockAdjustmentsController", "Method": "GetByStatus", "RelativePath": "api/StockAdjustments/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockAdjustmentHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/StockAdjustments/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "GetAll", "RelativePath": "api/StockRequests", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockRequestHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Create", "RelativePath": "api/StockRequests", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createStockRequestHeaderDto", "Type": "SCM.Application.DTOs.CreateStockRequestHeaderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockRequestHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "GetById", "RelativePath": "api/StockRequests/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockRequestHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Update", "RelativePath": "api/StockRequests/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockRequestHeaderDto", "Type": "SCM.Application.DTOs.UpdateStockRequestHeaderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Delete", "RelativePath": "api/StockRequests/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Approve", "RelativePath": "api/StockRequests/{id}/approve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "approveStockRequestDto", "Type": "SCM.Application.DTOs.ApproveStockRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Cancel", "RelativePath": "api/StockRequests/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Complete", "RelativePath": "api/StockRequests/{id}/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "completeStockRequestDto", "Type": "SCM.Application.DTOs.CompleteStockRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Reject", "RelativePath": "api/StockRequests/{id}/reject", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "rejectStockRequestDto", "Type": "SCM.Application.DTOs.RejectStockRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "Submit", "RelativePath": "api/StockRequests/{id}/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "submitStockRequestDto", "Type": "SCM.Application.DTOs.SubmitStockRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "GetDetails", "RelativePath": "api/StockRequests/{stockRequestHeaderId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockRequestHeaderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockRequestDetailDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "CreateDetail", "RelativePath": "api/StockRequests/{stockRequestHeaderId}/details", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockRequestHeaderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createStockRequestDetailDto", "Type": "SCM.Application.DTOs.CreateStockRequestDetailDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockRequestDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "GetDetailById", "RelativePath": "api/StockRequests/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockRequestDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "UpdateDetail", "RelativePath": "api/StockRequests/details/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockRequestDetailDto", "Type": "SCM.Application.DTOs.UpdateStockRequestDetailDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "DeleteDetail", "RelativePath": "api/StockRequests/details/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "GetByFromCostCenterId", "RelativePath": "api/StockRequests/from-cost-center/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockRequestHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "GetByStatus", "RelativePath": "api/StockRequests/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockRequestHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/StockRequests/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockRequestsController", "Method": "GetByToCostCenterId", "RelativePath": "api/StockRequests/to-cost-center/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockRequestHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "GetAll", "RelativePath": "api/StockTakes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTakeHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "Create", "RelativePath": "api/StockTakes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createStockTakeHeaderDto", "Type": "SCM.Application.DTOs.CreateStockTakeHeaderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTakeHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "GetById", "RelativePath": "api/StockTakes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTakeHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "Update", "RelativePath": "api/StockTakes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockTakeHeaderDto", "Type": "SCM.Application.DTOs.UpdateStockTakeHeaderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "Delete", "RelativePath": "api/StockTakes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "Cancel", "RelativePath": "api/StockTakes/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "Complete", "RelativePath": "api/StockTakes/{id}/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "completeStockTakeDto", "Type": "SCM.Application.DTOs.CompleteStockTakeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "GetDetails", "RelativePath": "api/StockTakes/{stockTakeHeaderId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockTakeHeaderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTakeDetailDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "GenerateDetails", "RelativePath": "api/StockTakes/{stockTakeHeaderId}/generate-details", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockTakeHeaderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "GetByCostCenterId", "RelativePath": "api/StockTakes/costcenter/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTakeHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "CreateDetail", "RelativePath": "api/StockTakes/details", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createStockTakeDetailDto", "Type": "SCM.Application.DTOs.CreateStockTakeDetailDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTakeDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "GetDetailById", "RelativePath": "api/StockTakes/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTakeDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "UpdateDetail", "RelativePath": "api/StockTakes/details/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockTakeDetailDto", "Type": "SCM.Application.DTOs.UpdateStockTakeDetailDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "DeleteDetail", "RelativePath": "api/StockTakes/details/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTakesController", "Method": "GetByStatus", "RelativePath": "api/StockTakes/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTakeHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/StockTakes/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "GetAll", "RelativePath": "api/StockTransfers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTransferHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "Create", "RelativePath": "api/StockTransfers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createStockTransferHeaderDto", "Type": "SCM.Application.DTOs.CreateStockTransferHeaderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTransferHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "GetById", "RelativePath": "api/StockTransfers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTransferHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "Update", "RelativePath": "api/StockTransfers/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockTransferHeaderDto", "Type": "SCM.Application.DTOs.UpdateStockTransferHeaderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "Delete", "RelativePath": "api/StockTransfers/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "Cancel", "RelativePath": "api/StockTransfers/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "Complete", "RelativePath": "api/StockTransfers/{id}/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "completeStockTransferDto", "Type": "SCM.Application.DTOs.CompleteStockTransferDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "GetDetails", "RelativePath": "api/StockTransfers/{stockTransferHeaderId}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockTransferHeaderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTransferDetailDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "CreateDetail", "RelativePath": "api/StockTransfers/{stockTransferHeaderId}/details", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockTransferHeaderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createStockTransferDetailDto", "Type": "SCM.Application.DTOs.CreateStockTransferDetailDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTransferDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "GetDetailById", "RelativePath": "api/StockTransfers/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StockTransferDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "UpdateDetail", "RelativePath": "api/StockTransfers/details/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStockTransferDetailDto", "Type": "SCM.Application.DTOs.UpdateStockTransferDetailDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "DeleteDetail", "RelativePath": "api/StockTransfers/details/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "GetByFromCostCenterId", "RelativePath": "api/StockTransfers/from-cost-center/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTransferHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "GetByStatus", "RelativePath": "api/StockTransfers/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTransferHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/StockTransfers/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StockTransfersController", "Method": "GetByCostCenterId", "RelativePath": "api/StockTransfers/to-cost-center/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StockTransferHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "GetAll", "RelativePath": "api/Stores", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StoreDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "Create", "RelativePath": "api/Stores", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createStoreDto", "Type": "SCM.Application.DTOs.CreateStoreDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StoreDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "GetById", "RelativePath": "api/Stores/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StoreDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "Update", "RelativePath": "api/Stores/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateStoreDto", "Type": "SCM.Application.DTOs.UpdateStoreDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "Delete", "RelativePath": "api/Stores/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "Activate", "RelativePath": "api/Stores/{id}/activate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "GetWithCostCenters", "RelativePath": "api/Stores/{id}/cost-centers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.StoreDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "Deactivate", "RelativePath": "api/Stores/{id}/deactivate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "GetByCompanyId", "RelativePath": "api/Stores/company/{companyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StoreDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.StoresController", "Method": "GetByLocationId", "RelativePath": "api/Stores/location/{locationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "locationId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.StoreDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Stores/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.SuppliersController", "Method": "GetAll", "RelativePath": "api/Suppliers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.SupplierListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.SuppliersController", "Method": "Create", "RelativePath": "api/Suppliers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createSupplierDto", "Type": "SCM.Application.DTOs.CreateSupplierDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.SupplierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.SuppliersController", "Method": "GetById", "RelativePath": "api/Suppliers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.SupplierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.SuppliersController", "Method": "Update", "RelativePath": "api/Suppliers/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateSupplierDto", "Type": "SCM.Application.DTOs.UpdateSupplierDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.SuppliersController", "Method": "Delete", "RelativePath": "api/Suppliers/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.SuppliersController", "Method": "GetByName", "RelativePath": "api/Suppliers/name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.SupplierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.SuppliersController", "Method": "Search", "RelativePath": "api/Suppliers/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "term", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.SupplierListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Suppliers/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TaxesController", "Method": "GetAll", "RelativePath": "api/Taxes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TaxDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TaxesController", "Method": "Create", "RelativePath": "api/Taxes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createTaxDto", "Type": "SCM.Application.DTOs.CreateTaxDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TaxDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TaxesController", "Method": "GetById", "RelativePath": "api/Taxes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TaxDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TaxesController", "Method": "Update", "RelativePath": "api/Taxes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateTaxDto", "Type": "SCM.Application.DTOs.UpdateTaxDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TaxesController", "Method": "Delete", "RelativePath": "api/Taxes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TaxesController", "Method": "GetDefault", "RelativePath": "api/Taxes/default", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TaxDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Taxes/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TestController", "Method": "TestDatabaseConnection", "RelativePath": "api/Test/database", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Test/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "GetAll", "RelativePath": "api/Transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "GetById", "RelativePath": "api/Transactions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "GetByCostCenterId", "RelativePath": "api/Transactions/costcenter/{costCenterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenterId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CreateCreditNote", "RelativePath": "api/Transactions/credit-note", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCreditNoteDto", "Type": "SCM.Application.DTOs.CreateCreditNoteDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "UpdateCreditNote", "RelativePath": "api/Transactions/credit-note/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateCreditNoteDto", "Type": "SCM.Application.DTOs.UpdateCreditNoteDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CancelCreditNote", "RelativePath": "api/Transactions/credit-note/{id}/cancel", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CompleteCreditNote", "RelativePath": "api/Transactions/credit-note/{id}/complete", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CreateProductOrder", "RelativePath": "api/Transactions/product-order", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createProductOrderDto", "Type": "SCM.Application.DTOs.CreateProductOrderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "UpdateProductOrder", "RelativePath": "api/Transactions/product-order/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateProductOrderDto", "Type": "SCM.Application.DTOs.UpdateProductOrderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "ApproveProductOrder", "RelativePath": "api/Transactions/product-order/{id}/approve", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CancelProductOrder", "RelativePath": "api/Transactions/product-order/{id}/cancel", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "RejectProductOrder", "RelativePath": "api/Transactions/product-order/{id}/reject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "SubmitProductOrder", "RelativePath": "api/Transactions/product-order/{id}/submit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CreateProductOrderFromRequest", "RelativePath": "api/Transactions/product-order/from-request/{productRequestId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productRequestId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createProductOrderDto", "Type": "SCM.Application.DTOs.CreateProductOrderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CreateProductRequest", "RelativePath": "api/Transactions/product-request", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createProductRequestDto", "Type": "SCM.Application.DTOs.CreateProductRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "UpdateProductRequest", "RelativePath": "api/Transactions/product-request/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateProductRequestDto", "Type": "SCM.Application.DTOs.UpdateProductRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "ApproveProductRequest", "RelativePath": "api/Transactions/product-request/{id}/approve", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CancelProductRequest", "RelativePath": "api/Transactions/product-request/{id}/cancel", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "RejectProductRequest", "RelativePath": "api/Transactions/product-request/{id}/reject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "SubmitProductRequest", "RelativePath": "api/Transactions/product-request/{id}/submit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CreateReceiving", "RelativePath": "api/Transactions/receiving", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createReceivingDto", "Type": "SCM.Application.DTOs.CreateReceivingDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "UpdateReceiving", "RelativePath": "api/Transactions/receiving/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateReceivingDto", "Type": "SCM.Application.DTOs.UpdateReceivingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "ApproveReceiving", "RelativePath": "api/Transactions/receiving/{id}/approve", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CancelReceiving", "RelativePath": "api/Transactions/receiving/{id}/cancel", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CompleteReceiving", "RelativePath": "api/Transactions/receiving/{id}/complete", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "UpdateReceivingDetail", "RelativePath": "api/Transactions/receiving/{id}/detail/{detailId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "detailId", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateReceivingDetailDto", "Type": "SCM.Application.DTOs.UpdateReceivingDetailDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "RejectReceiving", "RelativePath": "api/Transactions/receiving/{id}/reject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "SubmitReceiving", "RelativePath": "api/Transactions/receiving/{id}/submit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "CreateReceivingFromOrder", "RelativePath": "api/Transactions/receiving/from-order/{productOrderId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productOrderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "createReceivingDto", "Type": "SCM.Application.DTOs.CreateReceivingDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.TransactionHeaderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "GetByStageTypeId", "RelativePath": "api/Transactions/stage/{stageTypeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stageTypeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "GetByStatus", "RelativePath": "api/Transactions/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.TransactionsController", "Method": "GetBySupplierId", "RelativePath": "api/Transactions/supplier/{supplierId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "supplierId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.TransactionHeaderDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Transactions/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UnitGroupsController", "Method": "GetAll", "RelativePath": "api/UnitGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.UnitGroupDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitGroupsController", "Method": "Create", "RelativePath": "api/UnitGroups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createUnitGroupDto", "Type": "SCM.Application.DTOs.CreateUnitGroupDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UnitGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitGroupsController", "Method": "GetById", "RelativePath": "api/UnitGroups/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UnitGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitGroupsController", "Method": "Update", "RelativePath": "api/UnitGroups/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateUnitGroupDto", "Type": "SCM.Application.DTOs.UpdateUnitGroupDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UnitGroupsController", "Method": "Delete", "RelativePath": "api/UnitGroups/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UnitGroupsController", "Method": "GetWithUnits", "RelativePath": "api/UnitGroups/{id}/with-units", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UnitGroupDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/UnitGroups/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "GetAll", "RelativePath": "api/Units", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.UnitDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "Create", "RelativePath": "api/Units", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createUnitDto", "Type": "SCM.Application.DTOs.CreateUnitDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UnitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "GetById", "RelativePath": "api/Units/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UnitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "Update", "RelativePath": "api/Units/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateUnitDto", "Type": "SCM.Application.DTOs.UpdateUnitDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "Delete", "RelativePath": "api/Units/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "GetByUnitGroupId", "RelativePath": "api/Units/by-unit-group/{unitGroupId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "unitGroupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.UnitDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "ImportUnits", "RelativePath": "api/Units/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UnitImportDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UnitsController", "Method": "DownloadTemplate", "RelativePath": "api/Units/import/template", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Units/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UserPermissionsController", "Method": "CheckPermission", "RelativePath": "api/UserPermissions/check/{permission}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "permission", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UserPermissionsController", "Method": "GetCurrentUserPermissions", "RelativePath": "api/UserPermissions/current", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "SCM.API.Controllers.UserPermissionsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/UserPermissions/test-auth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "GetAll", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SCM.Application.DTOs.UserListDto, SCM.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "Create", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createUserDto", "Type": "SCM.Application.DTOs.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "GetById", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "Update", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateUserDto", "Type": "SCM.Application.DTOs.UpdateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "Delete", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "ChangePassword", "RelativePath": "api/Users/<USER>/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "changePasswordDto", "Type": "SCM.Application.DTOs.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "ResetPassword", "RelativePath": "api/Users/<USER>/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "resetPasswordDto", "Type": "SCM.Application.DTOs.ResetPasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "ValidatePassword", "RelativePath": "api/Users/<USER>/validate-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "password", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.UsersController", "Method": "GetByUsername", "RelativePath": "api/Users/<USER>/{username}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SCM.Application.DTOs.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SCM.API.Controllers.ApiControllerBase", "Method": "TestAuth", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]