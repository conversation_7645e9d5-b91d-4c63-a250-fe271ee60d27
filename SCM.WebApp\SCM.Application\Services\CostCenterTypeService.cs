using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class CostCenterTypeService : ICostCenterTypeService
{
    private readonly IRepository<CostCenterType> _costCenterTypeRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public CostCenterTypeService(
        IRepository<CostCenterType> costCenterTypeRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _costCenterTypeRepository = costCenterTypeRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CostCenterTypeDto>> GetAllCostCenterTypesAsync()
    {
        var costCenterTypes = await _dbContext.CostCenterTypes
            .ToListAsync();

        return _mapper.Map<IEnumerable<CostCenterTypeDto>>(costCenterTypes);
    }

    public async Task<CostCenterTypeDto?> GetCostCenterTypeByIdAsync(int id)
    {
        var costCenterType = await _costCenterTypeRepository.GetByIdAsync(id);
        return costCenterType != null ? _mapper.Map<CostCenterTypeDto>(costCenterType) : null;
    }

    public async Task<CostCenterTypeDto?> GetCostCenterTypeWithCostCentersAsync(int id)
    {
        var costCenterType = await _dbContext.CostCenterTypes
            .Include(ct => ct.CostCenters)
                .ThenInclude(cc => cc.Store)
                    .ThenInclude(s => s.Location)
                        .ThenInclude(l => l.Company)
            .FirstOrDefaultAsync(ct => ct.Id == id);

        return costCenterType != null ? _mapper.Map<CostCenterTypeDto>(costCenterType) : null;
    }

    public async Task<CostCenterTypeDto> CreateCostCenterTypeAsync(CreateCostCenterTypeDto createCostCenterTypeDto)
    {
        // Check if cost center type with the same name already exists
        var existingCostCenterType = await _dbContext.CostCenterTypes
            .FirstOrDefaultAsync(ct => ct.Name.ToLower() == createCostCenterTypeDto.Name.ToLower());

        if (existingCostCenterType != null)
            throw new InvalidOperationException($"Cost center type with name '{createCostCenterTypeDto.Name}' already exists.");

        var costCenterType = _mapper.Map<CostCenterType>(createCostCenterTypeDto);
        costCenterType.IsActive = true;
        costCenterType.CreatedAt = DateTime.UtcNow;

        await _costCenterTypeRepository.AddAsync(costCenterType);

        return _mapper.Map<CostCenterTypeDto>(costCenterType);
    }

    public async Task UpdateCostCenterTypeAsync(UpdateCostCenterTypeDto updateCostCenterTypeDto)
    {
        var costCenterType = await _costCenterTypeRepository.GetByIdAsync(updateCostCenterTypeDto.Id);
        if (costCenterType == null)
            throw new KeyNotFoundException($"Cost center type with ID {updateCostCenterTypeDto.Id} not found.");

        // Check if name is being changed and if the new name already exists
        if (costCenterType.Name != updateCostCenterTypeDto.Name)
        {
            var existingCostCenterType = await _dbContext.CostCenterTypes
                .FirstOrDefaultAsync(ct => ct.Name.ToLower() == updateCostCenterTypeDto.Name.ToLower() && ct.Id != updateCostCenterTypeDto.Id);

            if (existingCostCenterType != null)
                throw new InvalidOperationException($"Cost center type with name '{updateCostCenterTypeDto.Name}' already exists.");
        }

        _mapper.Map(updateCostCenterTypeDto, costCenterType);
        costCenterType.UpdatedAt = DateTime.UtcNow;

        await _costCenterTypeRepository.UpdateAsync(costCenterType);
    }

    public async Task DeleteCostCenterTypeAsync(int id)
    {
        var costCenterType = await _costCenterTypeRepository.GetByIdAsync(id);
        if (costCenterType == null)
            throw new KeyNotFoundException($"Cost center type with ID {id} not found.");

        // Check if cost center type is used in any cost centers
        var hasCostCenters = await _dbContext.CostCenters.AnyAsync(cc => cc.TypeId == id);
        if (hasCostCenters)
            throw new InvalidOperationException("Cannot delete cost center type that is used by cost centers.");

        // If no related entities, mark as inactive instead of deleting
        costCenterType.IsActive = false;
        costCenterType.UpdatedAt = DateTime.UtcNow;

        await _costCenterTypeRepository.UpdateAsync(costCenterType);
    }
}
