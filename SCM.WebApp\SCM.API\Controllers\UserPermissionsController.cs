using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SCM.Application.Services;

namespace SCM.API.Controllers;

[Authorize]
public class UserPermissionsController : ApiControllerBase
{
    private readonly IUserPermissionService _userPermissionService;
    private readonly ILogger<UserPermissionsController> _logger;

    public UserPermissionsController(IUserPermissionService userPermissionService, ILogger<UserPermissionsController> logger)
    {
        _userPermissionService = userPermissionService;
        _logger = logger;
    }

    [HttpGet("current")]
    public async Task<ActionResult<UserPermissionsDto>> GetCurrentUserPermissions()
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            
            var permissions = new UserPermissionsDto
            {
                UserId = userId,
                CanViewTransactions = await _userPermissionService.UserCanViewTransactionsAsync(userId),
                CanCreateTransactions = await _userPermissionService.UserCanCreateTransactionsAsync(userId),
                CanEditTransactions = await _userPermissionService.UserCanEditTransactionsAsync(userId),
                CanDeleteTransactions = await _userPermissionService.UserCanDeleteTransactionsAsync(userId),
                CanApproveTransactions = await _userPermissionService.UserCanApproveTransactionsAsync(userId),
                HasFullReceivePermission = await _userPermissionService.UserHasFullReceivePermissionAsync(userId)
            };

            return Ok(permissions);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when getting user permissions");
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user permissions");
            return StatusCode(500, "An error occurred while retrieving user permissions");
        }
    }

    [HttpGet("check/{permission}")]
    public async Task<ActionResult<bool>> CheckPermission(string permission)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            bool hasPermission = await _userPermissionService.UserHasPermissionAsync(userId, permission);
            return Ok(hasPermission);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when checking permission {Permission}", permission);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Permission}", permission);
            return StatusCode(500, "An error occurred while checking permission");
        }
    }
}

public class UserPermissionsDto
{
    public int UserId { get; set; }
    public bool CanViewTransactions { get; set; }
    public bool CanCreateTransactions { get; set; }
    public bool CanEditTransactions { get; set; }
    public bool CanDeleteTransactions { get; set; }
    public bool CanApproveTransactions { get; set; }
    public bool HasFullReceivePermission { get; set; }
}
