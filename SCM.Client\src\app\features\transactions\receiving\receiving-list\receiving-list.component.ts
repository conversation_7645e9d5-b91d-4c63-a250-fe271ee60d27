import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatTableModule, MatTable } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TransactionService } from '../../../../core/services/transaction.service';
import { ErrorService } from '../../../../core/services/error.service';
import { TransactionHeader } from '../../../../core/models/transaction.model';
import { finalize, catchError, of } from 'rxjs';
import { SelectPurchaseOrderDialogComponent } from '../select-purchase-order-dialog/select-purchase-order-dialog.component';

@Component({
  selector: 'app-receiving-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatChipsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
    MatDividerModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './receiving-list.component.html',
  styleUrls: ['./receiving-list.component.scss']
})
export class ReceivingListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'transactionNumber',
    'referenceNumber',
    'supplierName',
    'costCenterName',
    'transactionDate',
    'status',
    'totalAmount',
    'actions'
  ];

  receivings: TransactionHeader[] = [];
  filteredReceivings: TransactionHeader[] = [];
  isLoading = false;
  searchTerm = '';
  selectedStatus = '';

  statusOptions = ['All', 'Draft', 'Submitted', 'Approved', 'Completed', 'Cancelled', 'Rejected'];

  constructor(
    private transactionService: TransactionService,
    private router: Router,
    private snackBar: MatSnackBar,
    private errorService: ErrorService,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.loadReceivings();
  }

  loadReceivings(): void {
    this.isLoading = true;

    // Use the dedicated receiving endpoint
    this.transactionService.getAllReceivings()
      .pipe(
        finalize(() => this.isLoading = false),
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      )
      .subscribe(receivings => {
        this.receivings = receivings;
        this.filteredReceivings = [...this.receivings];
        this.applyFilters();
      });
  }

  applyFilters(): void {
    let filtered = [...this.receivings];

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(receiving =>
        receiving.transactionNumber?.toLowerCase().includes(searchLower) ||
        receiving.referenceNumber?.toLowerCase().includes(searchLower) ||
        receiving.supplierName?.toLowerCase().includes(searchLower) ||
        receiving.sourceCostCenterName?.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (this.selectedStatus && this.selectedStatus !== 'All') {
      filtered = filtered.filter(receiving =>
        receiving.status === this.selectedStatus
      );
    }

    this.filteredReceivings = filtered;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.filteredReceivings = [...this.receivings];
  }

  createReceiving(): void {
    this.router.navigate(['/transactions/receiving/new']);
  }

  createFromPurchaseOrder(): void {
    this.isLoading = true;

    // Get approved transactions and filter for purchase orders
    this.transactionService.getByStatus('Approved')
      .pipe(
        finalize(() => this.isLoading = false),
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      )
      .subscribe((transactions: TransactionHeader[]) => {
        // Filter for purchase orders (assuming stage type name contains "Order")
        const purchaseOrders = transactions.filter(t =>
          t.stageTypeName?.toLowerCase().includes('order') ||
          t.transactionNumber?.startsWith('PO-')
        );

        if (purchaseOrders.length === 0) {
          this.snackBar.open('No approved purchase orders found', 'Close', { duration: 3000 });
          return;
        }

        const dialogRef = this.dialog.open(SelectPurchaseOrderDialogComponent, {
          width: '800px',
          data: { purchaseOrders: purchaseOrders }
        });

        dialogRef.afterClosed().subscribe(selectedOrder => {
          if (selectedOrder) {
            this.router.navigate(['/transactions/receiving/from-order', selectedOrder.id]);
          }
        });
      });
  }

  viewReceiving(receiving: TransactionHeader): void {
    this.router.navigate([`/transactions/receiving/${receiving.id}`]);
  }

  completeReceiving(id: number): void {
    if (confirm('Are you sure you want to complete this receiving? This will update inventory levels and cannot be undone.')) {
      console.log(`DEBUG Frontend: Attempting to complete receiving ${id}`);
      this.isLoading = true;
      this.transactionService.completeReceiving(id)
        .pipe(finalize(() => {
          console.log(`DEBUG Frontend: Complete request finished for receiving ${id}`);
          this.isLoading = false;
        }))
        .subscribe({
          next: () => {
            console.log(`DEBUG Frontend: Successfully completed receiving ${id}`);
            this.snackBar.open('Receiving completed successfully. Inventory has been updated.', 'Close', { duration: 3000 });
            this.loadReceivings();
          },
          error: (error) => {
            console.error(`DEBUG Frontend: Error completing receiving ${id}:`, error);
            this.errorService.handleError(error);
          }
        });
    }
  }

  cancelReceiving(id: number): void {
    if (confirm('Are you sure you want to cancel this receiving?')) {
      this.isLoading = true;
      this.transactionService.cancelReceiving(id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Receiving cancelled successfully', 'Close', { duration: 3000 });
            this.loadReceivings();
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  editReceiving(receiving: TransactionHeader): void {
    this.router.navigate([`/transactions/receiving/${receiving.id}/edit`]);
  }

  submitReceiving(id: number): void {
    if (confirm('Are you sure you want to submit this receiving for approval?')) {
      this.isLoading = true;
      this.transactionService.submitReceiving(id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Receiving submitted successfully', 'Close', { duration: 3000 });
            this.loadReceivings();
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  approveReceiving(receiving: TransactionHeader): void {
    this.isLoading = true;

    this.transactionService.approveReceiving(receiving.id, '')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.snackBar.open(`Receiving ${receiving.transactionNumber} approved successfully.`, 'Close', { duration: 5000 });
          this.loadReceivings();
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  rejectReceiving(receiving: TransactionHeader): void {
    const reason = prompt('Please enter a reason for rejection:');
    if (reason) {
      this.isLoading = true;

      this.transactionService.rejectReceiving(receiving.id, reason)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Receiving rejected successfully', 'Close', { duration: 3000 });
            this.loadReceivings();
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  createCreditNote(receiving: TransactionHeader): void {
    // Navigate to credit note creation with receiving transaction pre-selected
    this.router.navigate(['/transactions/credit-notes/new'], {
      queryParams: {
        fromReceiving: receiving.id,
        receivingNumber: receiving.transactionNumber
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Draft': return 'status-draft';
      case 'Submitted': return 'status-submitted';
      case 'Approved': return 'status-approved';
      case 'Completed': return 'status-completed';
      case 'Cancelled': return 'status-cancelled';
      case 'Rejected': return 'status-rejected';
      default: return '';
    }
  }
}
