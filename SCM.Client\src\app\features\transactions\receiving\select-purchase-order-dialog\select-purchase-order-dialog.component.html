<h2 mat-dialog-title>Select Purchase Order</h2>
<div mat-dialog-content>
  <p>Select an approved purchase order to create a receiving:</p>

  <mat-form-field appearance="outline" class="search-field">
    <mat-label>Search</mat-label>
    <input matInput (keyup)="onSearch($event)" [value]="searchTerm" placeholder="Search by reference number or supplier">
    <mat-icon matSuffix>search</mat-icon>
  </mat-form-field>

  <div class="table-container">
    <table mat-table [dataSource]="filteredOrders">
      <!-- Reference Number Column -->
      <ng-container matColumnDef="referenceNumber">
        <th mat-header-cell *matHeaderCellDef>Reference Number</th>
        <td mat-cell *matCellDef="let order">
          {{order.referenceNumber || ('PO-' + order.id.toString().padStart(5, '0'))}}
        </td>
      </ng-container>

      <!-- Supplier Column -->
      <ng-container matColumnDef="supplierName">
        <th mat-header-cell *matHeaderCellDef>Supplier</th>
        <td mat-cell *matCellDef="let order">
          {{order.supplierName || 'N/A'}}
        </td>
      </ng-container>

      <!-- Cost Center Column -->
      <ng-container matColumnDef="costCenterName">
        <th mat-header-cell *matHeaderCellDef>Cost Center</th>
        <td mat-cell *matCellDef="let order">
          {{order.sourceCostCenterName || 'N/A'}}
        </td>
      </ng-container>

      <!-- Order Date Column -->
      <ng-container matColumnDef="transactionDate">
        <th mat-header-cell *matHeaderCellDef>Order Date</th>
        <td mat-cell *matCellDef="let order">{{order.transactionDate | date:'mediumDate'}}</td>
      </ng-container>

      <!-- Total Amount Column -->
      <ng-container matColumnDef="totalAmount">
        <th mat-header-cell *matHeaderCellDef>Total Amount</th>
        <td mat-cell *matCellDef="let order">{{order.totalAmount | currency}}</td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let order">
          <button mat-raised-button color="primary" (click)="selectOrder(order)">
            Select
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <div *ngIf="filteredOrders.length === 0" class="no-data-message">
      No approved purchase orders found.
    </div>
  </div>
</div>
<div mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
</div>
