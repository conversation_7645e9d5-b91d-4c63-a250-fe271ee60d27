using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class CompanyService : ICompanyService
{
    private readonly IRepository<Company> _companyRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public CompanyService(
        IRepository<Company> companyRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _companyRepository = companyRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CompanyDto>> GetAllCompaniesAsync()
    {
        var companies = await _dbContext.Companies
            .OrderBy(c => c.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<CompanyDto>>(companies);
    }

    public async Task<CompanyDto?> GetCompanyByIdAsync(int id)
    {
        var company = await _dbContext.Companies
            .FirstOrDefaultAsync(c => c.Id == id);

        return company != null ? _mapper.Map<CompanyDto>(company) : null;
    }

    public async Task<CompanyDto?> GetCompanyWithLocationsAsync(int id)
    {
        var company = await _dbContext.Companies
            .Include(c => c.Locations)
            .FirstOrDefaultAsync(c => c.Id == id);

        return company != null ? _mapper.Map<CompanyDto>(company) : null;
    }

    public async Task<CompanyDto> CreateCompanyAsync(CreateCompanyDto createCompanyDto)
    {
        // Check if company with the same name already exists
        var existingCompany = await _dbContext.Companies
            .FirstOrDefaultAsync(c => c.Name.ToLower() == createCompanyDto.Name.ToLower());

        if (existingCompany != null)
            throw new InvalidOperationException($"Company with name '{createCompanyDto.Name}' already exists.");

        var company = _mapper.Map<Company>(createCompanyDto);
        company.IsActive = true;
        company.CreatedAt = DateTime.UtcNow;

        await _companyRepository.AddAsync(company);

        return _mapper.Map<CompanyDto>(company);
    }

    public async Task UpdateCompanyAsync(UpdateCompanyDto updateCompanyDto)
    {
        var company = await _companyRepository.GetByIdAsync(updateCompanyDto.Id);
        if (company == null)
            throw new KeyNotFoundException($"Company with ID {updateCompanyDto.Id} not found.");

        // Check if company with the same name already exists (excluding current company)
        var existingCompany = await _dbContext.Companies
            .FirstOrDefaultAsync(c => c.Name.ToLower() == updateCompanyDto.Name.ToLower() && c.Id != updateCompanyDto.Id);

        if (existingCompany != null)
            throw new InvalidOperationException($"Company with name '{updateCompanyDto.Name}' already exists.");

        _mapper.Map(updateCompanyDto, company);
        company.UpdatedAt = DateTime.UtcNow;

        await _companyRepository.UpdateAsync(company);
    }

    public async Task DeleteCompanyAsync(int id)
    {
        var company = await _dbContext.Companies
            .Include(c => c.Locations)
            .FirstOrDefaultAsync(c => c.Id == id);

        if (company == null)
            throw new KeyNotFoundException($"Company with ID {id} not found.");

        if (company.Locations.Any())
            throw new InvalidOperationException("Cannot delete company that has locations. Delete the locations first.");

        await _companyRepository.DeleteAsync(company);
    }
}
