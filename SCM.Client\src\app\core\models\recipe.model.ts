export interface Recipe {
  id: number;
  name: string;
  productId: number;
  productName: string;
  productCode: string;
  yield: number;
  unitId?: number;
  unitName?: string;
  instructions?: string;
  notes?: string;
  cost?: number;
  isSubRecipe: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  ingredients: RecipeIngredient[];
}

export interface RecipeIngredient {
  id: number;
  recipeId: number;
  productId: number;
  productName: string;
  productCode: string;
  quantity: number;
  unitId?: number;
  unitName?: string;
  notes?: string;
  sequence: number;
  cost?: number;
  totalCost?: number;
  wastagePercentage?: number;
}

export interface RecipeList {
  id: number;
  name: string;
  productId: number;
  productName: string;
  productCode: string;
  yield: number;
  unitName?: string;
  cost?: number;
  ingredientCount: number;
  isActive: boolean;
  createdAt: Date;
}

export interface CreateRecipe {
  name: string;
  productId: number;
  yield: number;
  unitId?: number;
  instructions?: string;
  notes?: string;
  isSubRecipe: boolean;
  isActive: boolean;
  ingredients: CreateRecipeIngredient[];
}

export interface CreateRecipeIngredient {
  productId: number;
  quantity: number;
  unitId?: number;
  notes?: string;
  sequence: number;
  wastagePercentage?: number;
}

export interface UpdateRecipe {
  id: number;
  name: string;
  productId: number;
  yield: number;
  unitId?: number;
  instructions?: string;
  notes?: string;
  isSubRecipe: boolean;
  isActive: boolean;
}

export interface UpdateRecipeIngredient {
  id: number;
  recipeId: number;
  productId: number;
  quantity: number;
  unitId?: number;
  notes?: string;
  sequence: number;
  wastagePercentage?: number;
}
