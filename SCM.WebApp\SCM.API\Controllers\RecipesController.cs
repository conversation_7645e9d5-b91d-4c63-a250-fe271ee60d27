using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class RecipesController : ApiControllerBase
{
    private readonly IRecipeService _recipeService;

    public RecipesController(IRecipeService recipeService)
    {
        _recipeService = recipeService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<RecipeListDto>>> GetAll()
    {
        var recipes = await _recipeService.GetAllRecipesAsync();
        return Ok(recipes);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<RecipeDto>> GetById(int id)
    {
        var recipe = await _recipeService.GetRecipeByIdAsync(id);
        if (recipe == null)
            return NotFound();

        return Ok(recipe);
    }

    [HttpGet("product/{productId}")]
    public async Task<ActionResult<IEnumerable<RecipeListDto>>> GetByProductId(int productId)
    {
        var recipes = await _recipeService.GetRecipesByProductIdAsync(productId);
        return Ok(recipes);
    }

    [HttpGet("active")]
    public async Task<ActionResult<IEnumerable<RecipeListDto>>> GetActive()
    {
        var recipes = await _recipeService.GetActiveRecipesAsync();
        return Ok(recipes);
    }

    [HttpGet("subrecipes")]
    public async Task<ActionResult<IEnumerable<RecipeListDto>>> GetSubRecipes()
    {
        var recipes = await _recipeService.GetSubRecipesAsync();
        return Ok(recipes);
    }

    [HttpPost]
    public async Task<ActionResult<RecipeDto>> Create(CreateRecipeDto createRecipeDto)
    {
        try
        {
            var recipe = await _recipeService.CreateRecipeAsync(createRecipeDto);
            return CreatedAtAction(nameof(GetById), new { id = recipe.Id }, recipe);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateRecipeDto updateRecipeDto)
    {
        if (id != updateRecipeDto.Id)
            return BadRequest();

        try
        {
            await _recipeService.UpdateRecipeAsync(updateRecipeDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _recipeService.DeleteRecipeAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{recipeId}/ingredients")]
    public async Task<ActionResult<RecipeIngredientDto>> AddIngredient(int recipeId, CreateRecipeIngredientDto createRecipeIngredientDto)
    {
        try
        {
            var ingredient = await _recipeService.AddIngredientAsync(recipeId, createRecipeIngredientDto);
            return Ok(ingredient);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("ingredients/{id}")]
    public async Task<IActionResult> UpdateIngredient(int id, UpdateRecipeIngredientDto updateRecipeIngredientDto)
    {
        if (id != updateRecipeIngredientDto.Id)
            return BadRequest();

        try
        {
            await _recipeService.UpdateIngredientAsync(updateRecipeIngredientDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{recipeId}/ingredients/{ingredientId}")]
    public async Task<IActionResult> RemoveIngredient(int recipeId, int ingredientId)
    {
        try
        {
            await _recipeService.RemoveIngredientAsync(recipeId, ingredientId);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpGet("{id}/calculate-cost")]
    public async Task<ActionResult<decimal>> CalculateCost(int id)
    {
        try
        {
            var cost = await _recipeService.CalculateRecipeCostAsync(id);
            return Ok(cost);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
