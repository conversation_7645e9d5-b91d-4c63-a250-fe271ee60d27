using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class CreditNotesController : ApiControllerBase
{
    private readonly ITransactionService _transactionService;

    public CreditNotesController(ITransactionService transactionService)
    {
        _transactionService = transactionService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetAll()
    {
        // Get all transactions that are credit notes (stage type = CreditNote)
        var stageTypes = await _transactionService.GetAllTransactionStageTypesAsync();
        var creditNoteStageType = stageTypes.FirstOrDefault(st => st.Name == "CreditNote");

        if (creditNoteStageType == null)
            return Ok(new List<TransactionHeaderDto>());

        var creditNotes = await _transactionService.GetTransactionsByStageTypeIdAsync(creditNoteStageType.StageTypeId);
        return Ok(creditNotes);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<TransactionHeaderDto>> GetById(int id)
    {
        var creditNote = await _transactionService.GetTransactionByIdAsync(id);
        if (creditNote == null)
            return NotFound();

        // Verify it's a credit note
        var stageTypes = await _transactionService.GetAllTransactionStageTypesAsync();
        var creditNoteStageType = stageTypes.FirstOrDefault(st => st.Name == "CreditNote");

        if (creditNoteStageType == null || creditNote.StageTypeId != creditNoteStageType.StageTypeId)
            return NotFound();

        return Ok(creditNote);
    }

    [HttpPost]
    public async Task<ActionResult<TransactionHeaderDto>> Create(CreateCreditNoteDto createCreditNoteDto)
    {
        try
        {
            // TODO: Get user ID from current user
            var creditNote = await _transactionService.CreateCreditNoteAsync(createCreditNoteDto, 1);
            return CreatedAtAction(nameof(GetById), new { id = creditNote.Id }, creditNote);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("from-receiving/{receivingId}")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateFromReceiving(int receivingId, CreateCreditNoteDto createCreditNoteDto)
    {
        try
        {
            // TODO: Get user ID from current user
            var creditNote = await _transactionService.CreateCreditNoteFromReceivingAsync(receivingId, createCreditNoteDto, 1);
            return CreatedAtAction(nameof(GetById), new { id = creditNote.Id }, creditNote);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateCreditNoteDto updateCreditNoteDto)
    {
        if (id != updateCreditNoteDto.Id)
            return BadRequest("ID mismatch");

        try
        {
            await _transactionService.UpdateCreditNoteAsync(id, updateCreditNoteDto);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("{id}/complete")]
    public async Task<IActionResult> Complete(int id)
    {
        try
        {
            // TODO: Get user ID from current user
            await _transactionService.CompleteCreditNoteAsync(id, 1);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("{id}/cancel")]
    public async Task<IActionResult> Cancel(int id, [FromBody] string? reason = null)
    {
        try
        {
            await _transactionService.CancelCreditNoteAsync(id, reason);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
