.container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 999;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  mat-form-field {
    flex: 1;
    min-width: 200px;
  }
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  
  .stock-transfer-table {
    width: 100%;
  }
  
  .mat-column-referenceNumber {
    min-width: 120px;
  }
  
  .mat-column-fromCostCenterName,
  .mat-column-toCostCenterName {
    min-width: 150px;
  }
  
  .mat-column-transferDate {
    min-width: 180px;
  }
  
  .mat-column-status {
    min-width: 100px;
  }
  
  .mat-column-actions {
    min-width: 150px;
    text-align: right;
  }
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-draft {
  background-color: #e0e0e0;
  color: #616161;
}

.status-pending {
  background-color: #bbdefb;
  color: #1976d2;
}

.status-completed {
  background-color: #c8e6c9;
  color: #388e3c;
}

.status-cancelled {
  background-color: #ffcdd2;
  color: #d32f2f;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
}
