using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

[Authorize]
public class TransactionsController : ApiControllerBase
{
    private readonly ITransactionService _transactionService;
    private readonly ILogger<TransactionsController> _logger;

    public TransactionsController(
        ITransactionService transactionService,
        ILogger<TransactionsController> logger)
    {
        _transactionService = transactionService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetAll()
    {
        try
        {
            var transactions = await _transactionService.GetAllTransactionsAsync();
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all transactions");
            return StatusCode(500, "An error occurred while retrieving transactions");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<TransactionHeaderDto>> GetById(int id)
    {
        try
        {
            var transaction = await _transactionService.GetTransactionByIdAsync(id);
            if (transaction == null)
                return NotFound();

            return Ok(transaction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction with ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the transaction");
        }
    }

    // Removed GetByProcessId method as we no longer use ProcessId

    [HttpGet("stage/{stageTypeId}")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetByStageTypeId(int stageTypeId)
    {
        try
        {
            var transactions = await _transactionService.GetTransactionsByStageTypeIdAsync(stageTypeId);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transactions for stage type ID {StageTypeId}", stageTypeId);
            return StatusCode(500, "An error occurred while retrieving transactions");
        }
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetByStatus(string status)
    {
        try
        {
            var transactions = await _transactionService.GetTransactionsByStatusAsync(status);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transactions with status {Status}", status);
            return StatusCode(500, "An error occurred while retrieving transactions");
        }
    }

    [HttpGet("costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetByCostCenterId(int costCenterId)
    {
        try
        {
            var transactions = await _transactionService.GetTransactionsByCostCenterAsync(costCenterId);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transactions for cost center ID {CostCenterId}", costCenterId);
            return StatusCode(500, "An error occurred while retrieving transactions");
        }
    }

    [HttpGet("supplier/{supplierId}")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetBySupplierId(int supplierId)
    {
        try
        {
            var transactions = await _transactionService.GetTransactionsBySupplierAsync(supplierId);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transactions for supplier ID {SupplierId}", supplierId);
            return StatusCode(500, "An error occurred while retrieving transactions");
        }
    }

    // Product Request endpoints
    [HttpPost("product-request")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateProductRequest(CreateProductRequestDto createProductRequestDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateProductRequestAsync(createProductRequestDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating product request");
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product request");
            return StatusCode(500, "An error occurred while creating the product request");
        }
    }

    [HttpPut("product-request/{id}")]
    public async Task<ActionResult> UpdateProductRequest(int id, UpdateProductRequestDto updateProductRequestDto)
    {
        try
        {
            await _transactionService.UpdateProductRequestAsync(id, updateProductRequestDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product request with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the product request");
        }
    }

    [HttpPut("product-request/{id}/submit")]
    public async Task<ActionResult> SubmitProductRequest(int id, [FromBody] object data)
    {
        try
        {
            string notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.SubmitProductRequestAsync(id, notes);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting product request with ID {Id}", id);
            return StatusCode(500, "An error occurred while submitting the product request");
        }
    }

    [HttpPut("product-request/{id}/approve")]
    public async Task<ActionResult> ApproveProductRequest(int id, [FromBody] object data)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();

            string? notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString() ?? "{}");
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.ApproveProductRequestAsync(id, userId, notes);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when approving product request {Id}", id);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving product request with ID {Id}", id);
            return StatusCode(500, "An error occurred while approving the product request");
        }
    }

    [HttpPut("product-request/{id}/reject")]
    public async Task<ActionResult> RejectProductRequest(int id, [FromBody] object data)
    {
        try
        {
            if (data == null)
            {
                return BadRequest("Rejection reason is required");
            }

            var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
            if (!jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
            {
                return BadRequest("Rejection reason is required");
            }

            string reason = reasonElement.GetString();
            if (string.IsNullOrEmpty(reason))
            {
                return BadRequest("Rejection reason is required");
            }

            await _transactionService.RejectProductRequestAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting product request with ID {Id}", id);
            return StatusCode(500, "An error occurred while rejecting the product request");
        }
    }

    [HttpPut("product-request/{id}/cancel")]
    public async Task<ActionResult> CancelProductRequest(int id, [FromBody] object data)
    {
        try
        {
            string reason = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
                {
                    reason = reasonElement.GetString();
                }
            }

            await _transactionService.CancelProductRequestAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling product request with ID {Id}", id);
            return StatusCode(500, "An error occurred while cancelling the product request");
        }
    }

    // Product Order endpoints
    [HttpPost("product-order")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateProductOrder(CreateProductOrderDto createProductOrderDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateProductOrderAsync(createProductOrderDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating product order");
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product order");
            return StatusCode(500, "An error occurred while creating the product order");
        }
    }

    [HttpPost("product-order/from-request/{productRequestId}")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateProductOrderFromRequest(int productRequestId, CreateProductOrderDto createProductOrderDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateProductOrderFromRequestAsync(productRequestId, createProductOrderDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating product order from request {ProductRequestId}", productRequestId);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product order from request with ID {ProductRequestId}", productRequestId);
            return StatusCode(500, "An error occurred while creating the product order");
        }
    }

    [HttpPut("product-order/{id}")]
    public async Task<ActionResult> UpdateProductOrder(int id, UpdateProductOrderDto updateProductOrderDto)
    {
        try
        {
            await _transactionService.UpdateProductOrderAsync(id, updateProductOrderDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the product order");
        }
    }

    [HttpPut("product-order/{id}/submit")]
    public async Task<ActionResult> SubmitProductOrder(int id, [FromBody] object data)
    {
        try
        {
            string notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.SubmitProductOrderAsync(id, notes);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while submitting the product order");
        }
    }

    [HttpPut("product-order/{id}/approve")]
    public async Task<ActionResult> ApproveProductOrder(int id, [FromBody] object data)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();

            string? notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString() ?? "{}");
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.ApproveProductOrderAsync(id, userId, notes);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when approving product order {Id}", id);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while approving the product order");
        }
    }

    [HttpPut("product-order/{id}/reject")]
    public async Task<ActionResult> RejectProductOrder(int id, [FromBody] object data)
    {
        try
        {
            if (data == null)
            {
                return BadRequest("Rejection reason is required");
            }

            var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
            if (!jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
            {
                return BadRequest("Rejection reason is required");
            }

            string reason = reasonElement.GetString();
            if (string.IsNullOrEmpty(reason))
            {
                return BadRequest("Rejection reason is required");
            }

            await _transactionService.RejectProductOrderAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while rejecting the product order");
        }
    }

    [HttpPut("product-order/{id}/cancel")]
    public async Task<ActionResult> CancelProductOrder(int id, [FromBody] object data)
    {
        try
        {
            string reason = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
                {
                    reason = reasonElement.GetString();
                }
            }

            await _transactionService.CancelProductOrderAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling product order with ID {Id}", id);
            return StatusCode(500, "An error occurred while cancelling the product order");
        }
    }

    // Receiving endpoints
    [HttpPost("receiving")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateReceiving(CreateReceivingDto createReceivingDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateReceivingAsync(createReceivingDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating receiving");
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating receiving");
            return StatusCode(500, "An error occurred while creating the receiving");
        }
    }

    [HttpPost("receiving/from-order/{productOrderId}")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateReceivingFromOrder(int productOrderId, CreateReceivingDto createReceivingDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateReceivingFromOrderAsync(productOrderId, createReceivingDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating receiving from order {ProductOrderId}", productOrderId);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating receiving from order with ID {ProductOrderId}", productOrderId);
            return StatusCode(500, "An error occurred while creating the receiving");
        }
    }

    [HttpPut("receiving/{id}")]
    public async Task<ActionResult> UpdateReceiving(int id, UpdateReceivingDto updateReceivingDto)
    {
        try
        {
            await _transactionService.UpdateReceivingAsync(id, updateReceivingDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the receiving");
        }
    }

    [HttpPut("receiving/{id}/detail/{detailId}")]
    public async Task<ActionResult> UpdateReceivingDetail(int id, int detailId, UpdateReceivingDetailDto updateReceivingDetailDto)
    {
        try
        {
            await _transactionService.UpdateReceivingDetailAsync(id, detailId, updateReceivingDetailDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating receiving detail with ID {DetailId} for receiving with ID {Id}", detailId, id);
            return StatusCode(500, "An error occurred while updating the receiving detail");
        }
    }

    [HttpPut("receiving/{id}/submit")]
    public async Task<ActionResult> SubmitReceiving(int id, [FromBody] object data)
    {
        try
        {
            string notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.SubmitReceivingAsync(id, notes);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while submitting the receiving");
        }
    }

    [HttpPut("receiving/{id}/approve")]
    public async Task<ActionResult> ApproveReceiving(int id, [FromBody] object data)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();

            string notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.ApproveReceivingAsync(id, userId, notes);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when approving receiving {Id}", id);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while approving the receiving");
        }
    }

    [HttpPut("receiving/{id}/reject")]
    public async Task<ActionResult> RejectReceiving(int id, [FromBody] object data)
    {
        try
        {
            if (data == null)
            {
                return BadRequest("Rejection reason is required");
            }

            var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
            if (!jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
            {
                return BadRequest("Rejection reason is required");
            }

            string reason = reasonElement.GetString();
            if (string.IsNullOrEmpty(reason))
            {
                return BadRequest("Rejection reason is required");
            }

            await _transactionService.RejectReceivingAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while rejecting the receiving");
        }
    }

    // DISABLED - Use ReceivingController.Complete instead
    // [HttpPut("receiving/{id}/complete")]
    // public async Task<ActionResult> CompleteReceiving(int id)

    [HttpPut("receiving/{id}/cancel")]
    public async Task<ActionResult> CancelReceiving(int id, [FromBody] object data)
    {
        try
        {
            string reason = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
                {
                    reason = reasonElement.GetString();
                }
            }

            await _transactionService.CancelReceivingAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while cancelling the receiving");
        }
    }

    // Credit Note endpoints
    [HttpPost("credit-note")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateCreditNote(CreateCreditNoteDto createCreditNoteDto)
    {
        try
        {
            // Get the current user ID (for now using a default value of 1)
            int userId = 1; // TODO: Get from authenticated user

            var transaction = await _transactionService.CreateCreditNoteAsync(createCreditNoteDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating credit note");
            return StatusCode(500, "An error occurred while creating the credit note");
        }
    }

    [HttpPut("credit-note/{id}")]
    public async Task<ActionResult> UpdateCreditNote(int id, UpdateCreditNoteDto updateCreditNoteDto)
    {
        try
        {
            await _transactionService.UpdateCreditNoteAsync(id, updateCreditNoteDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating credit note with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the credit note");
        }
    }

    [HttpPut("credit-note/{id}/complete")]
    public async Task<ActionResult> CompleteCreditNote(int id)
    {
        try
        {
            // Get the current user ID (for now using a default value of 1)
            int userId = 1; // TODO: Get from authenticated user

            await _transactionService.CompleteCreditNoteAsync(id, userId);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing credit note with ID {Id}", id);
            return StatusCode(500, "An error occurred while completing the credit note");
        }
    }

    [HttpPut("credit-note/{id}/cancel")]
    public async Task<ActionResult> CancelCreditNote(int id, [FromBody] object data)
    {
        try
        {
            string reason = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString());
                if (jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
                {
                    reason = reasonElement.GetString();
                }
            }

            await _transactionService.CancelCreditNoteAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling credit note with ID {Id}", id);
            return StatusCode(500, "An error occurred while cancelling the credit note");
        }
    }
}
