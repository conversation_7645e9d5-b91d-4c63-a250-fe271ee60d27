<div class="container">
  <div class="header">
    <h1>Stock Adjustments</h1>
    <button mat-raised-button color="primary" (click)="addStockAdjustment()">
      <mat-icon>add</mat-icon> New Stock Adjustment
    </button>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>
  
  <mat-card class="filter-card" *ngIf="!isLoading">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by reference number or cost center">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilter()">
            <mat-option value="">All</mat-option>
            <mat-option *ngFor="let status of statusOptions" [value]="status">
              {{status}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        
        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2" *ngIf="!isLoading">
    <table mat-table [dataSource]="filteredAdjustments" class="stock-adjustment-table" matSort>
      <!-- Reference Number Column -->
      <ng-container matColumnDef="referenceNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Reference #</th>
        <td mat-cell *matCellDef="let adjustment">{{adjustment.referenceNumber}}</td>
      </ng-container>
      
      <!-- Cost Center Column -->
      <ng-container matColumnDef="costCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Cost Center</th>
        <td mat-cell *matCellDef="let adjustment">{{adjustment.costCenterName}}</td>
      </ng-container>
      
      <!-- Adjustment Date Column -->
      <ng-container matColumnDef="adjustmentDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
        <td mat-cell *matCellDef="let adjustment">{{adjustment.adjustmentDate | date:'medium'}}</td>
      </ng-container>
      
      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let adjustment">
          <span class="status-badge" [ngClass]="getStatusClass(adjustment.status)">
            {{adjustment.status}}
          </span>
        </td>
      </ng-container>
      
      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let adjustment">
          <button mat-icon-button color="primary" (click)="viewStockAdjustment(adjustment)" matTooltip="View Details">
            <mat-icon>visibility</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="adjustment.status === 'Draft'" (click)="editStockAdjustment(adjustment)" matTooltip="Edit">
            <mat-icon>edit</mat-icon>
          </button>
          
          <button mat-icon-button color="accent" *ngIf="adjustment.status === 'Draft'" (click)="completeStockAdjustment(adjustment)" matTooltip="Complete Adjustment">
            <mat-icon>check_circle</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" *ngIf="adjustment.status === 'Draft'" (click)="cancelStockAdjustment(adjustment)" matTooltip="Cancel Adjustment">
            <mat-icon>cancel</mat-icon>
          </button>
          
          <button mat-icon-button color="warn" *ngIf="adjustment.status === 'Draft'" (click)="deleteStockAdjustment(adjustment)" matTooltip="Delete">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      
      <!-- Row shown when there is no matching data -->
      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" colspan="5">
          <div class="no-data">
            <mat-icon>info</mat-icon>
            <span>No stock adjustments found</span>
          </div>
        </td>
      </tr>
    </table>
    
    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
