import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { SupplierService, Supplier, CreateSupplier, UpdateSupplier } from '../../../../core/services/supplier.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-supplier-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatTabsModule,
    MatDividerModule
  ],
  templateUrl: './supplier-detail.component.html',
  styleUrls: ['./supplier-detail.component.scss']
})
export class SupplierDetailComponent implements OnInit {
  supplierForm!: FormGroup;
  isLoading = false;
  isEditMode = false;
  supplierId: number | null = null;
  supplier: Supplier | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private supplierService: SupplierService
  ) { }

  ngOnInit(): void {
    this.initForm();
    
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.supplierId = +id;
        this.isEditMode = true;
        this.loadSupplier(this.supplierId);
      }
    });
  }

  initForm(): void {
    this.supplierForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(150)]],
      contactPerson: ['', Validators.maxLength(150)],
      phone: ['', Validators.maxLength(50)],
      email: ['', [Validators.email, Validators.maxLength(150)]],
      address: ['', Validators.maxLength(255)],
      city: ['', Validators.maxLength(100)],
      state: ['', Validators.maxLength(100)],
      country: ['', Validators.maxLength(100)],
      postalCode: ['', Validators.maxLength(20)],
      taxNumber: ['', Validators.maxLength(50)],
      notes: [''],
      bankAccount: ['', Validators.maxLength(50)],
      bankName: ['', Validators.maxLength(100)],
      isActive: [true]
    });
  }

  loadSupplier(id: number): void {
    this.isLoading = true;
    
    this.supplierService.getSupplierById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (supplier) => {
          this.supplier = supplier;
          this.supplierForm.patchValue(supplier);
        },
        error: (error) => {
          console.error('Error loading supplier', error);
          this.snackBar.open('Error loading supplier. Please try again.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  saveSupplier(): void {
    if (this.supplierForm.invalid) {
      this.markFormGroupTouched(this.supplierForm);
      this.snackBar.open('Please fix the errors in the form before saving.', 'Close', {
        duration: 5000
      });
      return;
    }
    
    this.isLoading = true;
    
    if (this.isEditMode && this.supplierId) {
      // Update existing supplier
      const updateData: UpdateSupplier = {
        id: this.supplierId,
        ...this.supplierForm.value
      };
      
      this.supplierService.updateSupplier(this.supplierId, updateData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Supplier updated successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/procurement/suppliers']);
          },
          error: (error) => {
            console.error('Error updating supplier', error);
            this.snackBar.open('Error updating supplier: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    } else {
      // Create new supplier
      const createData: CreateSupplier = {
        ...this.supplierForm.value
      };
      
      this.supplierService.createSupplier(createData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (result) => {
            this.snackBar.open('Supplier created successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/procurement/suppliers']);
          },
          error: (error) => {
            console.error('Error creating supplier', error);
            this.snackBar.open('Error creating supplier: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancel(): void {
    this.router.navigate(['/procurement/suppliers']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
