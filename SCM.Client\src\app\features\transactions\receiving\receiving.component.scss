.page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h1 {
    margin: 0;
    font-size: 24px;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.receiving-form {
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    
    .form-field {
      flex: 1;
      
      &.full-width {
        width: 100%;
      }
    }
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0 15px;
    
    h2 {
      margin: 0;
      font-size: 18px;
    }
  }
  
  .table-container {
    margin-bottom: 20px;
    overflow-x: auto;
    
    .receiving-items-table {
      width: 100%;
      
      .table-form-field {
        width: 100%;
        margin: 0;
        
        ::ng-deep .mat-mdc-form-field-infix {
          width: auto;
          min-width: 80px;
        }
      }
      
      .mat-column-select {
        width: 60px;
      }
      
      .mat-column-productCode {
        min-width: 150px;
      }
      
      .mat-column-productName {
        min-width: 200px;
      }
      
      .mat-column-unitName {
        min-width: 100px;
      }
      
      .mat-column-orderedQuantity,
      .mat-column-receivedQuantity {
        min-width: 120px;
      }
      
      .mat-column-unitPrice,
      .mat-column-total {
        min-width: 120px;
      }
      
      .mat-column-batchNumber {
        min-width: 120px;
      }
      
      .mat-column-expiryDate {
        min-width: 150px;
      }
      
      .mat-column-actions {
        width: 60px;
      }
    }
  }
  
  .receiving-summary {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    
    .receiving-total {
      font-size: 18px;
      font-weight: 500;
      display: flex;
      gap: 15px;
      
      .total-value {
        font-weight: 700;
        color: #1976d2;
      }
    }
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    
    .header-actions {
      width: 100%;
      justify-content: space-between;
    }
  }
}
