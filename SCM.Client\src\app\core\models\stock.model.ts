export interface StockOnHand {
  id: number;
  productId: number;
  productCode?: string;
  productName?: string;
  costCenterId: number;
  costCenterName?: string;
  quantity: number;
  unitId?: number;
  unitName?: string;
  costPrice?: number;
  lastUpdatedAt: Date;
}

export interface StockAdjustment {
  productId: number;
  costCenterId: number;
  quantity: number;
  reason: string;
  notes?: string;
}

export interface StockTransfer {
  fromCostCenterId: number;
  toCostCenterId: number;
  items: StockTransferItem[];
  notes?: string;
}

export interface StockTransferItem {
  productId: number;
  quantity: number;
}

export interface StockTakeHeader {
  id: number;
  documentNumber: string;
  costCenterId: number;
  costCenterName?: string;
  startDate: Date;
  endDate?: Date;
  status: string;
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt?: Date;
  details?: StockTakeDetail[];
}

export interface CreateStockTakeHeader {
  costCenterId: number;
  notes?: string;
}

export interface StockTakeDetail {
  id: number;
  headerId: number;
  productId: number;
  productCode?: string;
  productName?: string;
  unitId?: number;
  unitName?: string;
  systemQuantity: number;
  actualQuantity: number;
  variance: number;
  notes?: string;
  createdAt: Date;
  updatedAt?: Date;
}

export interface UpdateStockTakeDetail {
  id: number;
  actualQuantity: number;
  notes?: string;
}

export interface StockOnHandSummary {
  productId: number;
  productCode: string;
  productName: string;
  costCenterId: number;
  costCenterName: string;
  totalQuantity: number;
  averageCostPrice?: number;
  unitName?: string;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  isLowStock: boolean;
  isOverStock: boolean;
  needsReorder: boolean;
}

export interface StockRequest {
  id?: number;
  requestDate: Date;
  fromCostCenterId: number;
  toCostCenterId: number;
  remarks?: string;
  refNo?: string;
  isSubmitted?: boolean;
  items: StockRequestItem[];
}

export interface StockRequestItem {
  id?: number;
  requestId?: number;
  productId: number;
  productCode?: string;
  productName?: string;
  unitId?: number;
  unitName?: string;
  quantity: number;
  price?: number;
  total?: number;
  notes?: string;
  deliveryDate?: Date;
}
