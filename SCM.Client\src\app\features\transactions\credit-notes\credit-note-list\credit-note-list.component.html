<div class="credit-note-list-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>Credit Notes</mat-card-title>
      <mat-card-subtitle>Manage credit notes for returned or adjusted goods</mat-card-subtitle>
      <div class="header-actions">
        <button mat-raised-button color="primary" routerLink="/transactions/credit-notes/new">
          <mat-icon>add</mat-icon>
          New Credit Note
        </button>
      </div>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading credit notes...</p>
      </div>

      <div *ngIf="!loading && creditNotes.length === 0" class="no-data">
        <mat-icon>receipt_long</mat-icon>
        <h3>No Credit Notes Found</h3>
        <p>Create your first credit note to get started.</p>
        <button mat-raised-button color="primary" routerLink="/transactions/credit-notes/new">
          <mat-icon>add</mat-icon>
          Create Credit Note
        </button>
      </div>

      <div *ngIf="!loading && creditNotes.length > 0" class="table-container">
        <table mat-table [dataSource]="creditNotes" class="credit-notes-table">
          <!-- Transaction Number Column -->
          <ng-container matColumnDef="transactionNumber">
            <th mat-header-cell *matHeaderCellDef>Transaction #</th>
            <td mat-cell *matCellDef="let creditNote">
              <a [routerLink]="['/transactions/credit-notes', creditNote.id]" class="transaction-link">
                {{ creditNote.transactionNumber }}
              </a>
            </td>
          </ng-container>

          <!-- Reference Number Column -->
          <ng-container matColumnDef="referenceNumber">
            <th mat-header-cell *matHeaderCellDef>Reference #</th>
            <td mat-cell *matCellDef="let creditNote">{{ creditNote.referenceNumber || '-' }}</td>
          </ng-container>

          <!-- Supplier Column -->
          <ng-container matColumnDef="supplierName">
            <th mat-header-cell *matHeaderCellDef>Supplier</th>
            <td mat-cell *matCellDef="let creditNote">{{ creditNote.supplierName || '-' }}</td>
          </ng-container>

          <!-- Cost Center Column -->
          <ng-container matColumnDef="sourceCostCenterName">
            <th mat-header-cell *matHeaderCellDef>Cost Center</th>
            <td mat-cell *matCellDef="let creditNote">{{ creditNote.sourceCostCenterName }}</td>
          </ng-container>

          <!-- Transaction Date Column -->
          <ng-container matColumnDef="transactionDate">
            <th mat-header-cell *matHeaderCellDef>Date</th>
            <td mat-cell *matCellDef="let creditNote">{{ creditNote.transactionDate | date:'short' }}</td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let creditNote">
              <mat-chip [color]="getStatusColor(creditNote.status)" selected>
                {{ creditNote.status }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Reason Column -->
          <ng-container matColumnDef="reason">
            <th mat-header-cell *matHeaderCellDef>Reason</th>
            <td mat-cell *matCellDef="let creditNote">
              <span [matTooltip]="creditNote.reason">{{ creditNote.reason }}</span>
            </td>
          </ng-container>

          <!-- Total Amount Column -->
          <ng-container matColumnDef="totalAmount">
            <th mat-header-cell *matHeaderCellDef>Amount</th>
            <td mat-cell *matCellDef="let creditNote">
              {{ creditNote.totalAmount | currency:'USD':'symbol':'1.2-2' }}
            </td>
          </ng-container>

          <!-- Related Transaction Column -->
          <ng-container matColumnDef="relatedTransactionNumber">
            <th mat-header-cell *matHeaderCellDef>Related Transaction</th>
            <td mat-cell *matCellDef="let creditNote">{{ creditNote.relatedTransactionNumber || '-' }}</td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let creditNote">
              <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #actionMenu="matMenu">
                <button mat-menu-item [routerLink]="['/transactions/credit-notes', creditNote.id]">
                  <mat-icon>visibility</mat-icon>
                  <span>View</span>
                </button>
                <button mat-menu-item 
                        [routerLink]="['/transactions/credit-notes', creditNote.id, 'edit']"
                        [disabled]="creditNote.status !== 'Draft'">
                  <mat-icon>edit</mat-icon>
                  <span>Edit</span>
                </button>
                <button mat-menu-item 
                        (click)="completeCreditNote(creditNote.id)"
                        [disabled]="creditNote.status !== 'Draft'">
                  <mat-icon>check_circle</mat-icon>
                  <span>Complete</span>
                </button>
                <button mat-menu-item 
                        (click)="cancelCreditNote(creditNote.id)"
                        [disabled]="creditNote.status === 'Completed'">
                  <mat-icon>cancel</mat-icon>
                  <span>Cancel</span>
                </button>
                <mat-divider></mat-divider>
                <button mat-menu-item 
                        (click)="deleteCreditNote(creditNote.id)"
                        [disabled]="creditNote.status === 'Completed'"
                        class="delete-action">
                  <mat-icon>delete</mat-icon>
                  <span>Delete</span>
                </button>
              </mat-menu>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </mat-card-content>
  </mat-card>
</div>
