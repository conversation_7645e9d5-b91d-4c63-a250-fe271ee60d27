import { CostCenter } from './cost-center.model';

export interface Store {
  id: number;
  name: string;
  code?: string;
  locationId: number;
  locationName?: string;
  companyName?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
  isSalesPoint: boolean;
  logoUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  costCenters?: CostCenter[];
}

export interface StoreList {
  id: number;
  name: string;
  code?: string;
  locationId: number;
  locationName?: string;
  companyName?: string;
  city?: string;
  country?: string;
  isSalesPoint: boolean;
  isActive: boolean;
  costCentersCount: number;
}

export interface CreateStore {
  name: string;
  code?: string;
  locationId: number;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
  isSalesPoint: boolean;
  logoUrl?: string;
}

export interface UpdateStore {
  id: number;
  name: string;
  code?: string;
  locationId: number;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
  isSalesPoint: boolean;
  logoUrl?: string;
  isActive: boolean;
}
