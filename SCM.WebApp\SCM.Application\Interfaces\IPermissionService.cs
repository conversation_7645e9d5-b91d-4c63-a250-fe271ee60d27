using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IPermissionService
{
    Task<IEnumerable<PermissionDto>> GetAllPermissionsAsync();
    Task<IEnumerable<PermissionCategoryDto>> GetPermissionsByCategoryAsync();
    Task<PermissionDto?> GetPermissionByIdAsync(int id);
    Task<PermissionDto?> GetPermissionByNameAsync(string name);
    Task InitializeDefaultPermissionsAsync();
}
