<div class="import-dialog">
  <h2 mat-dialog-title>Import Products from Excel</h2>
  
  <mat-dialog-content>
    <!-- File Selection Section -->
    <div class="file-section" *ngIf="!importResult">
      <div class="upload-area">
        <mat-icon class="upload-icon">cloud_upload</mat-icon>
        <p>Select an Excel file to import products</p>
        <input type="file" 
               #fileInput 
               accept=".xlsx" 
               (change)="onFileSelected($event)"
               style="display: none;">
        <button mat-raised-button color="primary" (click)="fileInput.click()">
          <mat-icon>attach_file</mat-icon>
          Choose File
        </button>
        <div class="selected-file" *ngIf="selectedFile">
          <mat-icon>description</mat-icon>
          <span>{{ selectedFile.name }}</span>
        </div>
      </div>
      
      <div class="template-section">
        <p>Don't have a template? Download the Excel template to get started.</p>
        <button mat-stroked-button color="accent" (click)="downloadTemplate()">
          <mat-icon>download</mat-icon>
          Download Template
        </button>
      </div>
    </div>

    <!-- Progress Section -->
    <div class="progress-section" *ngIf="isUploading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
      <p>Importing products, please wait...</p>
    </div>

    <!-- Results Section -->
    <div class="results-section" *ngIf="importResult && !isUploading">
      <mat-card class="result-summary">
        <mat-card-header>
          <mat-card-title>Import Results</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-label">Total Rows:</span>
              <span class="stat-value">{{ importResult.totalRows }}</span>
            </div>
            <div class="stat-item success">
              <span class="stat-label">Successful:</span>
              <span class="stat-value">{{ importResult.successCount }}</span>
            </div>
            <div class="stat-item error" *ngIf="importResult.errorCount > 0">
              <span class="stat-label">Errors:</span>
              <span class="stat-value">{{ importResult.errorCount }}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Successful Products -->
      <mat-card *ngIf="importResult.successfulProducts.length > 0" class="success-list">
        <mat-card-header>
          <mat-card-title>Successfully Imported Products</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="product-list">
            <div *ngFor="let product of importResult.successfulProducts" class="product-item">
              <mat-icon class="success-icon">check_circle</mat-icon>
              <span>{{ product }}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Error Details -->
      <mat-card *ngIf="importResult.errors.length > 0" class="error-details">
        <mat-card-header>
          <mat-card-title>Import Errors</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <table mat-table [dataSource]="importResult.errors" class="error-table">
            <ng-container matColumnDef="rowNumber">
              <th mat-header-cell *matHeaderCellDef>Row</th>
              <td mat-cell *matCellDef="let error">{{ error.rowNumber }}</td>
            </ng-container>

            <ng-container matColumnDef="productCode">
              <th mat-header-cell *matHeaderCellDef>Product Code</th>
              <td mat-cell *matCellDef="let error">{{ error.productCode }}</td>
            </ng-container>

            <ng-container matColumnDef="productName">
              <th mat-header-cell *matHeaderCellDef>Product Name</th>
              <td mat-cell *matCellDef="let error">{{ error.productName }}</td>
            </ng-container>

            <ng-container matColumnDef="errorMessage">
              <th mat-header-cell *matHeaderCellDef>Error Message</th>
              <td mat-cell *matCellDef="let error">{{ error.errorMessage }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </mat-card-content>
      </mat-card>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="close()">
      {{ importResult ? 'Close' : 'Cancel' }}
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="importProducts()" 
            [disabled]="!selectedFile || isUploading"
            *ngIf="!importResult">
      <mat-icon>upload</mat-icon>
      Import Products
    </button>
  </mat-dialog-actions>
</div>
