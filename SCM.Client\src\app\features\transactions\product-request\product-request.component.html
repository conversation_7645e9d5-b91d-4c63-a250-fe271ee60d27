<div class="page-container">
  <div class="page-header">
    <h1>
      <ng-container *ngIf="isViewMode">
        Product Request: {{productRequestNo}}
        <span *ngIf="status" class="status-chip" [ngClass]="'status-' + status.toLowerCase()">
          {{status}}
        </span>
      </ng-container>
      <ng-container *ngIf="isEditMode">Edit Product Request: {{productRequestNo}}</ng-container>
      <ng-container *ngIf="!isViewMode && !isEditMode">New Product Request</ng-container>
    </h1>
    <div class="header-actions" *ngIf="!isViewMode">
      <button mat-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back
      </button>
      <button mat-raised-button color="primary" (click)="save()" [disabled]="isLoading">
        <mat-icon>save</mat-icon> Save
      </button>
      <button mat-raised-button color="accent" (click)="submit()" [disabled]="isLoading || !canSubmit()">
        <mat-icon>send</mat-icon> Submit
      </button>
      <button mat-button color="warn" (click)="discard()" [disabled]="isLoading">
        <mat-icon>cancel</mat-icon> Cancel
      </button>
    </div>
    <div class="header-actions" *ngIf="isViewMode">
      <button mat-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back
      </button>
      <button mat-raised-button color="primary" (click)="editProductRequest()" *ngIf="status !== 'Approved'">
        <mat-icon>edit</mat-icon> Edit
      </button>
      <button mat-raised-button color="accent" (click)="createPurchaseOrder()" *ngIf="status === 'Approved'">
        <mat-icon>shopping_cart</mat-icon> Create Purchase Order
      </button>
    </div>
  </div>

  <div class="spinner-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <!-- View Mode Display -->
  <ng-container *ngIf="isViewMode">
    <mat-card>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-row">
            <div class="info-label">Request Number:</div>
            <div class="info-value">{{productRequestNo}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Request Date:</div>
            <div class="info-value">{{productRequestForm.get('requestDate')?.value | date:'mediumDate'}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Cost Center:</div>
            <div class="info-value">
              {{getCostCenterName(productRequestForm.get('costCenterId')?.value)}}
            </div>
          </div>
          <div class="info-row" *ngIf="productRequestForm.get('notes')?.value">
            <div class="info-label">Notes:</div>
            <div class="info-value notes-text">{{productRequestForm.get('notes')?.value}}</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </ng-container>

  <!-- Edit Mode Form -->
  <form [formGroup]="productRequestForm" class="product-request-form" *ngIf="!isViewMode">
    <mat-card>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Request Number</mat-label>
            <input matInput [(ngModel)]="productRequestNo" [ngModelOptions]="{standalone: true}" id="requestNumberField" readonly>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Request Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="requestDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="productRequestForm.get('requestDate')?.hasError('required')">
              Request date is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Cost Center</mat-label>
            <mat-select formControlName="costCenterId">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{costCenter.name}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="productRequestForm.get('costCenterId')?.hasError('required')">
              Cost center is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes</mat-label>
            <textarea matInput formControlName="notes" rows="2"></textarea>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>
  </form>

  <!-- Request Items Section -->
  <div class="section-header">
    <h2>Request Items</h2>
    <button mat-mini-fab color="primary" (click)="addItem()" type="button" *ngIf="!isViewMode">
      <mat-icon>add</mat-icon>
    </button>
  </div>

  <!-- View Mode Table -->
  <div class="table-container mat-elevation-z2" *ngIf="isViewMode">
    <div *ngIf="requestItems.length === 0" class="empty-table-message">
      <p>No items in this product request.</p>
    </div>
    <table mat-table [dataSource]="requestItems" *ngIf="requestItems.length > 0">
      <!-- Product Name Column -->
      <ng-container matColumnDef="productName">
        <th mat-header-cell *matHeaderCellDef>Product</th>
        <td mat-cell *matCellDef="let item">{{item.productName}}</td>
      </ng-container>

      <!-- Unit Column -->
      <ng-container matColumnDef="unitName">
        <th mat-header-cell *matHeaderCellDef>Unit</th>
        <td mat-cell *matCellDef="let item">{{item.unitName}}</td>
      </ng-container>

      <!-- Quantity Column -->
      <ng-container matColumnDef="quantity">
        <th mat-header-cell *matHeaderCellDef>Quantity</th>
        <td mat-cell *matCellDef="let item">{{item.quantity}}</td>
      </ng-container>

      <!-- Price Column -->
      <ng-container matColumnDef="price">
        <th mat-header-cell *matHeaderCellDef>Price</th>
        <td mat-cell *matCellDef="let item">{{item.price | currency}}</td>
      </ng-container>

      <!-- Total Column -->
      <ng-container matColumnDef="total">
        <th mat-header-cell *matHeaderCellDef>Total</th>
        <td mat-cell *matCellDef="let item">{{item.total | currency}}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="viewDisplayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: viewDisplayedColumns;"></tr>
    </table>

    <div class="order-total" *ngIf="requestItems.length > 0">
      <span>Total:</span>
      <span class="total-amount">{{calculateGrandTotal() | currency}}</span>
    </div>
  </div>

    <!-- Edit Mode Table -->
    <div class="table-container mat-elevation-z2" *ngIf="!isViewMode">
      <table mat-table [dataSource]="items.controls">
        <!-- Product Code Column -->
        <ng-container matColumnDef="productCode">
          <th mat-header-cell *matHeaderCellDef>Product Code</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productCode')"
                     [matAutocomplete]="auto" placeholder="Enter product code or name">
              <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayProductFn">
                <mat-option *ngFor="let product of products"
                           [value]="product.code"
                           (click)="selectProduct(i, product)">
                  {{product.code}} - {{product.name}}
                </mat-option>
              </mat-autocomplete>
              <mat-error *ngIf="item.get('productId')?.hasError('required')">
                Product is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Product Name Column -->
        <ng-container matColumnDef="productName">
          <th mat-header-cell *matHeaderCellDef>Product Name</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Unit Column -->
        <ng-container matColumnDef="unitName">
          <th mat-header-cell *matHeaderCellDef>Unit</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <mat-select [formControl]="item.get('unitId')" placeholder="Select unit" (selectionChange)="onUnitChange(i)">
                <mat-option *ngFor="let unit of units" [value]="unit.id">
                  {{unit.name}} ({{unit.abbreviation}})
                </mat-option>
              </mat-select>
              <mat-error *ngIf="item.get('unitId')?.hasError('required')">
                Unit is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Quantity Column -->
        <ng-container matColumnDef="quantity">
          <th mat-header-cell *matHeaderCellDef>Quantity</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('quantity')"
                     (input)="calculateTotal(i)" min="0.01" step="0.01">
              <mat-error *ngIf="item.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="item.get('quantity')?.hasError('min')">
                Quantity must be greater than 0
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Price Column -->
        <ng-container matColumnDef="price">
          <th mat-header-cell *matHeaderCellDef>Price</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('price')"
                     (input)="calculateTotal(i)" min="0" step="0.01">
              <span matTextPrefix>$&nbsp;</span>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Total Column -->
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef>Total</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('total')" readonly>
              <span matTextPrefix>$&nbsp;</span>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Delivery Date Column -->
        <ng-container matColumnDef="deliveryDate">
          <th mat-header-cell *matHeaderCellDef>Delivery Date</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [matDatepicker]="deliveryPicker" [formControl]="item.get('deliveryDate')">
              <mat-datepicker-toggle matSuffix [for]="deliveryPicker"></mat-datepicker-toggle>
              <mat-datepicker #deliveryPicker></mat-datepicker>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <button mat-icon-button color="warn" (click)="removeItem(i)" type="button">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <div *ngIf="items.length === 0" class="empty-table-message">
        <p>No items added. Click the + button to add items.</p>
      </div>

      <div class="order-total" *ngIf="items.length > 0">
        <span>Total:</span>
        <span class="total-amount">{{calculateGrandTotal() | currency}}</span>
      </div>
    </div>
</div>
