import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import {
  ProductRequestHeader,
  ProductRequestListItem,
  CreateProductRequestHeader,
  UpdateProductRequestHeader,
  SubmitProductRequestDto,
  ApproveProductRequestDto,
  RejectProductRequestDto,
  CompleteProductRequestDto,
  UpdateProductRequestDetail,
  CreateProductRequestDetail,
  ProductRequestDetail
} from '../models/product-request.model';

@Injectable({
  providedIn: 'root'
})
export class ProductRequestService {
  private readonly path = 'productrequests';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<ProductRequestListItem[]> {
    return this.apiService.get<ProductRequestListItem[]>(this.path);
  }

  getById(id: number): Observable<ProductRequestHeader> {
    return this.apiService.get<ProductRequestHeader>(`${this.path}/${id}`);
  }

  getByCostCenter(costCenterId: number): Observable<ProductRequestListItem[]> {
    return this.apiService.get<ProductRequestListItem[]>(`${this.path}/costcenter/${costCenterId}`);
  }

  getByStatus(status: string): Observable<ProductRequestListItem[]> {
    return this.apiService.get<ProductRequestListItem[]>(`${this.path}/status/${status}`);
  }

  create(productRequest: CreateProductRequestHeader): Observable<ProductRequestHeader> {
    return this.apiService.post<ProductRequestHeader, CreateProductRequestHeader>(this.path, productRequest);
  }

  update(id: number, productRequest: UpdateProductRequestHeader): Observable<void> {
    return this.apiService.put<void, UpdateProductRequestHeader>(`${this.path}/${id}`, productRequest);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  submit(submitDto: SubmitProductRequestDto): Observable<void> {
    return this.apiService.post<void, SubmitProductRequestDto>(`${this.path}/submit`, submitDto);
  }

  approve(approveDto: ApproveProductRequestDto): Observable<void> {
    return this.apiService.post<void, ApproveProductRequestDto>(`${this.path}/approve`, approveDto);
  }

  reject(rejectDto: RejectProductRequestDto): Observable<void> {
    return this.apiService.post<void, RejectProductRequestDto>(`${this.path}/reject`, rejectDto);
  }

  complete(completeDto: CompleteProductRequestDto): Observable<void> {
    return this.apiService.post<void, CompleteProductRequestDto>(`${this.path}/complete`, completeDto);
  }

  cancel(id: number): Observable<void> {
    return this.apiService.post<void, any>(`${this.path}/cancel/${id}`, {});
  }

  // Detail methods
  getDetails(productRequestId: number): Observable<ProductRequestDetail[]> {
    return this.apiService.get<ProductRequestDetail[]>(`${this.path}/${productRequestId}/details`);
  }

  addDetail(productRequestId: number, detail: CreateProductRequestDetail): Observable<ProductRequestDetail> {
    return this.apiService.post<ProductRequestDetail, CreateProductRequestDetail>(`${this.path}/${productRequestId}/details`, detail);
  }

  updateDetail(id: number, detail: UpdateProductRequestDetail): Observable<void> {
    return this.apiService.put<void, UpdateProductRequestDetail>(`${this.path}/details/${id}`, detail);
  }

  deleteDetail(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/details/${id}`);
  }
}
