import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ErrorService {
  constructor(private snackBar: MatSnackBar) {}

  /**
   * Handle HTTP errors and display appropriate messages
   */
  handleError(error: HttpErrorResponse) {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
      console.error('Client-side error:', error.error.message);
    } else if (error.error instanceof SyntaxError && error.error.message.includes('JSON')) {
      // JSON parsing error
      console.error('JSON parsing error:', error.error);
      errorMessage = 'Error processing server response. Please try again later.';
    } else {
      // Server-side error
      if (error.status === 0) {
        errorMessage = 'Could not connect to the server. Please check your internet connection.';
      } else if (error.status === 400) {
        errorMessage = this.handleBadRequest(error);
      } else if (error.status === 401) {
        errorMessage = 'You are not authorized to perform this action. Please log in again.';
      } else if (error.status === 403) {
        errorMessage = 'You do not have permission to perform this action.';
      } else if (error.status === 404) {
        errorMessage = 'The requested resource was not found.';
      } else if (error.status === 500) {
        errorMessage = 'A server error occurred. Please try again later.';
      } else {
        errorMessage = `Error ${error.status}: ${error.statusText || 'Unknown error'}`;
      }

      console.error('Server-side error:', error);
    }

    // Show error message in snackbar
    this.snackBar.open(errorMessage, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });

    // Return an observable with a user-facing error message
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Handle 400 Bad Request errors
   */
  private handleBadRequest(error: HttpErrorResponse): string {
    try {
      // Check if the error has a message property
      if (error.error && typeof error.error === 'object' && error.error.message) {
        return error.error.message;
      }

      // Check if the error is a string
      if (error.error && typeof error.error === 'string') {
        try {
          // Try to parse it as JSON
          const parsedError = JSON.parse(error.error);
          if (parsedError.message) {
            return parsedError.message;
          }
        } catch (e) {
          // If it's not valid JSON, return the string directly
          return error.error;
        }
      }

      // Check if the error has validation errors
      if (error.error && typeof error.error === 'object' && error.error.errors) {
        const validationErrors = error.error.errors;
        const errorMessages: string[] = [];

        // Extract validation error messages
        for (const key in validationErrors) {
          if (validationErrors.hasOwnProperty(key)) {
            const messages = validationErrors[key];
            if (Array.isArray(messages)) {
              errorMessages.push(...messages);
            } else if (typeof messages === 'string') {
              errorMessages.push(messages);
            }
          }
        }

        if (errorMessages.length > 0) {
          return errorMessages.join('. ');
        }
      }

      // Default message for bad request
      return 'Invalid request. Please check your input and try again.';
    } catch (e) {
      console.error('Error handling bad request:', e);
      return 'Invalid request. Please check your input and try again.';
    }
  }

  /**
   * Show a success message
   */
  showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  /**
   * Show an error message
   */
  showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  /**
   * Show an info message
   */
  showInfo(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000
    });
  }
}
