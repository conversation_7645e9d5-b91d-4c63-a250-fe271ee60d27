<div class="container">
  <div class="header">
    <h1>{{ isEditMode ? 'Edit Stock Request' : 'New Stock Request' }}</h1>
    <div class="header-actions">
      <button mat-button color="warn" (click)="cancel()">
        <mat-icon>close</mat-icon> Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveStockRequest()" [disabled]="requestForm.invalid || isLoading" *ngIf="!isEditMode || (stockRequest?.status === 'Draft')">
        <mat-icon>save</mat-icon> Save
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockRequest?.status === 'Draft'" (click)="submitStockRequest()" [disabled]="isLoading">
        <mat-icon>send</mat-icon> Submit
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockRequest?.status === 'Submitted'" (click)="approveStockRequest()" [disabled]="isLoading">
        <mat-icon>thumb_up</mat-icon> Approve
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && stockRequest?.status === 'Submitted'" (click)="rejectStockRequest()" [disabled]="isLoading">
        <mat-icon>thumb_down</mat-icon> Reject
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockRequest?.status === 'Approved'" (click)="completeStockRequest()" [disabled]="isLoading">
        <mat-icon>check_circle</mat-icon> Complete
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && (stockRequest?.status === 'Draft' || stockRequest?.status === 'Submitted' || stockRequest?.status === 'Approved')" (click)="cancelStockRequest()" [disabled]="isLoading">
        <mat-icon>cancel</mat-icon> Cancel Request
      </button>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <form [formGroup]="requestForm" *ngIf="!isLoading">
    <mat-card class="form-card">
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Request Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="requestDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="requestForm.get('requestDate')?.hasError('required')">
              Request date is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>From Cost Center</mat-label>
            <mat-select formControlName="fromCostCenterId">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{ costCenter.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="requestForm.get('fromCostCenterId')?.hasError('required')">
              From cost center is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>To Cost Center</mat-label>
            <mat-select formControlName="toCostCenterId">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{ costCenter.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="requestForm.get('toCostCenterId')?.hasError('required')">
              To cost center is required
            </mat-error>
            <mat-error *ngIf="requestForm.get('toCostCenterId')?.hasError('sameCostCenter')">
              From and To cost centers cannot be the same
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes</mat-label>
            <textarea matInput formControlName="notes" rows="3"></textarea>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <div class="section-header">
      <h2>Request Items</h2>
      <button mat-mini-fab color="primary" (click)="addDetail()" [disabled]="requestForm.disabled">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div formArrayName="details">
      <mat-card class="form-card" *ngFor="let detailForm of detailsFormArray.controls; let i = index" [formGroupName]="i">
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product</mat-label>
              <mat-select formControlName="productId" (selectionChange)="onProductSelected($event.value, i)">
                <mat-option *ngFor="let product of products" [value]="product.id">
                  {{ product.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="detailForm.get('productId')?.hasError('required')">
                Product is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Quantity</mat-label>
              <input matInput type="number" formControlName="quantity" min="0.01" step="0.01">
              <mat-error *ngIf="detailForm.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="detailForm.get('quantity')?.hasError('min')">
                Quantity must be greater than 0
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Unit</mat-label>
              <mat-select formControlName="unitId">
                <mat-option *ngFor="let unit of units" [value]="unit.id">
                  {{ unit.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Price</mat-label>
              <input matInput type="number" formControlName="price" min="0" step="0.01">
              <span matPrefix>$&nbsp;</span>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Total</mat-label>
              <input matInput type="number" [value]="getTotal(i)" readonly>
              <span matPrefix>$&nbsp;</span>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Delivery Date</mat-label>
              <input matInput [matDatepicker]="deliveryPicker" formControlName="deliveryDate">
              <mat-datepicker-toggle matSuffix [for]="deliveryPicker"></mat-datepicker-toggle>
              <mat-datepicker #deliveryPicker></mat-datepicker>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <input matInput formControlName="notes">
            </mat-form-field>
          </div>

          <div class="detail-actions" *ngIf="!requestForm.disabled">
            <button mat-icon-button color="warn" (click)="removeDetail(i)" matTooltip="Remove Item">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <div class="no-items" *ngIf="detailsFormArray.length === 0">
        <p>No items added to this request. Click the + button to add items.</p>
      </div>
    </div>

    <div class="form-actions">
      <button mat-button color="warn" (click)="cancel()">
        Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveStockRequest()" [disabled]="requestForm.invalid || isLoading" *ngIf="!isEditMode || (stockRequest?.status === 'Draft')">
        Save
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockRequest?.status === 'Draft'" (click)="submitStockRequest()" [disabled]="isLoading">
        Submit
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockRequest?.status === 'Submitted'" (click)="approveStockRequest()" [disabled]="isLoading">
        Approve
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && stockRequest?.status === 'Submitted'" (click)="rejectStockRequest()" [disabled]="isLoading">
        Reject
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockRequest?.status === 'Approved'" (click)="completeStockRequest()" [disabled]="isLoading">
        Complete
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && (stockRequest?.status === 'Draft' || stockRequest?.status === 'Submitted' || stockRequest?.status === 'Approved')" (click)="cancelStockRequest()" [disabled]="isLoading">
        Cancel Request
      </button>
    </div>
  </form>

  <div class="status-info" *ngIf="isEditMode && stockRequest">
    <div class="status-badge" [ngClass]="'status-' + stockRequest.status.toLowerCase()">
      {{ stockRequest.status }}
    </div>
    <div class="status-details">
      <p *ngIf="stockRequest.status === 'Submitted'">
        Submitted by {{ stockRequest.createdByName || 'Unknown' }} on {{ stockRequest.createdAt | date:'medium' }}
      </p>
      <p *ngIf="stockRequest.status === 'Approved'">
        Approved by {{ stockRequest.approvedByName || 'Unknown' }} on {{ stockRequest.approvedAt | date:'medium' }}
      </p>
      <p *ngIf="stockRequest.status === 'Completed'">
        Completed by {{ stockRequest.completedByName || 'Unknown' }} on {{ stockRequest.completedAt | date:'medium' }}
      </p>
      <p *ngIf="stockRequest.status === 'Rejected' || stockRequest.status === 'Cancelled'">
        Updated on {{ stockRequest.updatedAt | date:'medium' }}
      </p>
    </div>
  </div>
</div>
