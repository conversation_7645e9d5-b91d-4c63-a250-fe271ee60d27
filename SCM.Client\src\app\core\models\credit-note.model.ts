export interface CreditNote {
  id: number;
  transactionNumber: string;
  referenceNumber?: string;
  stageTypeId?: number;
  stageTypeName?: string;
  sourceCostCenterId: number;
  sourceCostCenterName: string;
  supplierId?: number;
  supplierName?: string;
  transactionDate: Date;
  status: string;
  reason: string;
  notes?: string;
  totalAmount?: number;
  relatedTransactionId?: number;
  relatedTransactionNumber?: string;
  createdById?: number;
  createdByName?: string;
  createdAt: Date;
  approvedById?: number;
  approvedByName?: string;
  approvedAt?: Date;
  details: CreditNoteDetail[];
}

export interface CreditNoteDetail {
  id: number;
  transactionId: number;
  productId: number;
  productCode: string;
  productName: string;
  batchId?: number;
  batchNumber?: string;
  unitId?: number;
  unitName?: string;
  quantity: number;
  unitPrice?: number;
  lineTotal?: number;
  reason: string;
  notes?: string;
}

export interface CreateCreditNote {
  sourceCostCenterId: number;
  supplierId?: number;
  relatedTransactionId?: number;
  transactionDate: Date;
  referenceNumber?: string;
  reason: string;
  notes?: string;
  details: CreateCreditNoteDetail[];
}

export interface CreateCreditNoteDetail {
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  unitPrice?: number;
  taxId?: number;
  notes?: string;
  reason: string;
}

export interface UpdateCreditNote {
  id: number;
  sourceCostCenterId: number;
  supplierId?: number;
  transactionDate: Date;
  referenceNumber?: string;
  reason: string;
  notes?: string;
}

export interface CreditNoteListItem {
  id: number;
  transactionNumber: string;
  referenceNumber?: string;
  supplierName?: string;
  sourceCostCenterName: string;
  transactionDate: Date;
  status: string;
  reason: string;
  totalAmount?: number;
  relatedTransactionNumber?: string;
  createdByName?: string;
  createdAt: Date;
}

// Credit Note Status Types
export enum CreditNoteStatus {
  Draft = 'Draft',
  Completed = 'Completed',
  Cancelled = 'Cancelled'
}

// Credit Note Reasons
export enum CreditNoteReason {
  DamagedGoods = 'Damaged Goods',
  IncorrectQuantity = 'Incorrect Quantity',
  QualityIssue = 'Quality Issue',
  WrongProduct = 'Wrong Product',
  OverDelivery = 'Over Delivery',
  PriceDiscrepancy = 'Price Discrepancy',
  Other = 'Other'
}
