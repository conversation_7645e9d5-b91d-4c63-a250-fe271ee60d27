import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { StockService } from '../../../../core/services/stock.service';
import { StockTakeHeader } from '../../../../core/models/stock.model';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-stock-take-detail-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    ReactiveFormsModule
  ],
  templateUrl: './stock-take-detail-dialog.component.html',
  styleUrls: ['./stock-take-detail-dialog.component.scss']
})
export class StockTakeDetailDialogComponent implements OnInit {
  stockTake: StockTakeHeader;
  isLoading = false;
  completeForm: FormGroup;
  
  displayedColumns: string[] = [
    'productCode',
    'productName',
    'systemQuantity',
    'actualQuantity',
    'variance',
    'notes'
  ];

  constructor(
    private dialogRef: MatDialogRef<StockTakeDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { stockTake: StockTakeHeader },
    private fb: FormBuilder,
    private stockService: StockService,
    private snackBar: MatSnackBar
  ) {
    this.stockTake = data.stockTake;
    this.completeForm = this.fb.group({
      notes: ['', Validators.maxLength(500)]
    });
  }

  ngOnInit(): void {
    // If the stock take doesn't have details, load them
    if (!this.stockTake.details || this.stockTake.details.length === 0) {
      this.loadStockTakeDetails();
    }
  }

  loadStockTakeDetails(): void {
    this.isLoading = true;
    this.stockService.getStockTakeById(this.stockTake.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (stockTake) => {
          this.stockTake = stockTake;
        },
        error: (error) => {
          console.error('Error loading stock take details', error);
          this.snackBar.open('Error loading stock take details', 'Close', {
            duration: 3000
          });
        }
      });
  }

  completeStockTake(): void {
    if (this.completeForm.valid) {
      this.isLoading = true;
      
      this.stockService.completeStockTake(this.stockTake.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock take completed successfully', 'Close', {
              duration: 3000
            });
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error completing stock take', error);
            this.snackBar.open('Error completing stock take', 'Close', {
              duration: 3000
            });
          }
        });
    }
  }

  cancelStockTake(): void {
    if (confirm('Are you sure you want to cancel this stock take? This action cannot be undone.')) {
      this.isLoading = true;
      
      this.stockService.cancelStockTake(this.stockTake.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock take cancelled', 'Close', {
              duration: 3000
            });
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error cancelling stock take', error);
            this.snackBar.open('Error cancelling stock take', 'Close', {
              duration: 3000
            });
          }
        });
    }
  }

  close(): void {
    this.dialogRef.close();
  }

  getTotalVariance(): number {
    return this.stockTake.details?.reduce((total, detail) => total + detail.variance, 0) || 0;
  }

  getPositiveVarianceCount(): number {
    return this.stockTake.details?.filter(detail => detail.variance > 0).length || 0;
  }

  getNegativeVarianceCount(): number {
    return this.stockTake.details?.filter(detail => detail.variance < 0).length || 0;
  }

  getZeroVarianceCount(): number {
    return this.stockTake.details?.filter(detail => detail.variance === 0).length || 0;
  }
}
