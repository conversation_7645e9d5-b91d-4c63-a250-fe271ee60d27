namespace SCM.Domain.Entities;

public class StockOnHand
{
    public int StockId { get; set; }
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? BaseQuantity { get; set; }
    public int? UnitId { get; set; }
    public decimal? AverageCost { get; set; }
    public decimal? CostPrice { get; set; }
    public decimal? ReturnVariance { get; set; }
    public decimal? NetCost { get; set; }
    public decimal? SalesPrice { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual CostCenter CostCenter { get; set; } = null!;
    public virtual Unit? Unit { get; set; }
}
