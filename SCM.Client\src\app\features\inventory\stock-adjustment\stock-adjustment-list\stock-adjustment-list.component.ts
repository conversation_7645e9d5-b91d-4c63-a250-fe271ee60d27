import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StockAdjustmentService } from '../../../../core/services/stock-adjustment.service';
import { StockAdjustmentHeader } from '../../../../core/models/stock-adjustment.model';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-stock-adjustment-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './stock-adjustment-list.component.html',
  styleUrls: ['./stock-adjustment-list.component.scss']
})
export class StockAdjustmentListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  
  displayedColumns: string[] = [
    'referenceNumber', 
    'costCenterName', 
    'adjustmentDate', 
    'status', 
    'actions'
  ];
  
  stockAdjustments: StockAdjustmentHeader[] = [];
  filteredAdjustments: StockAdjustmentHeader[] = [];
  isLoading = false;
  searchTerm = '';
  selectedStatus = '';
  
  statusOptions = ['All', 'Draft', 'Completed', 'Cancelled'];

  constructor(
    private stockAdjustmentService: StockAdjustmentService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.loadStockAdjustments();
  }

  loadStockAdjustments(): void {
    this.isLoading = true;
    this.stockAdjustmentService.getAllStockAdjustments()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.stockAdjustments = data;
          this.filteredAdjustments = [...this.stockAdjustments];
        },
        error: (error) => {
          console.error('Error loading stock adjustments', error);
          this.snackBar.open('Error loading stock adjustments', 'Close', {
            duration: 3000
          });
        }
      });
  }

  applyFilter(): void {
    this.filteredAdjustments = this.stockAdjustments.filter(adjustment => {
      const matchesSearch = this.searchTerm === '' || 
        adjustment.referenceNumber.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        adjustment.costCenterName.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesStatus = this.selectedStatus === 'All' || this.selectedStatus === '' || 
        adjustment.status === this.selectedStatus;
      
      return matchesSearch && matchesStatus;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.filteredAdjustments = [...this.stockAdjustments];
  }

  addStockAdjustment(): void {
    this.router.navigate(['/inventory/stock-adjustments/new']);
  }

  editStockAdjustment(adjustment: StockAdjustmentHeader): void {
    this.router.navigate(['/inventory/stock-adjustments', adjustment.id]);
  }

  viewStockAdjustment(adjustment: StockAdjustmentHeader): void {
    this.router.navigate(['/inventory/stock-adjustments', adjustment.id]);
  }

  deleteStockAdjustment(adjustment: StockAdjustmentHeader): void {
    if (confirm(`Are you sure you want to delete stock adjustment ${adjustment.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockAdjustmentService.deleteStockAdjustment(adjustment.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock adjustment deleted successfully', 'Close', {
              duration: 3000
            });
            this.loadStockAdjustments();
          },
          error: (error) => {
            console.error('Error deleting stock adjustment', error);
            this.snackBar.open('Error deleting stock adjustment: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  completeStockAdjustment(adjustment: StockAdjustmentHeader): void {
    if (confirm(`Are you sure you want to complete stock adjustment ${adjustment.referenceNumber}? This will update inventory levels.`)) {
      this.isLoading = true;
      this.stockAdjustmentService.completeStockAdjustment(adjustment.id, { id: adjustment.id })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock adjustment completed successfully', 'Close', {
              duration: 3000
            });
            this.loadStockAdjustments();
          },
          error: (error) => {
            console.error('Error completing stock adjustment', error);
            this.snackBar.open('Error completing stock adjustment: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancelStockAdjustment(adjustment: StockAdjustmentHeader): void {
    if (confirm(`Are you sure you want to cancel stock adjustment ${adjustment.referenceNumber}?`)) {
      this.isLoading = true;
      this.stockAdjustmentService.cancelStockAdjustment(adjustment.id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock adjustment cancelled successfully', 'Close', {
              duration: 3000
            });
            this.loadStockAdjustments();
          },
          error: (error) => {
            console.error('Error cancelling stock adjustment', error);
            this.snackBar.open('Error cancelling stock adjustment: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Draft': return 'status-draft';
      case 'Completed': return 'status-completed';
      case 'Cancelled': return 'status-cancelled';
      default: return '';
    }
  }
}
