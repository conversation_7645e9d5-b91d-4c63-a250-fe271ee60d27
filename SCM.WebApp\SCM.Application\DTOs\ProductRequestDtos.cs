namespace SCM.Application.DTOs;

public class ProductRequestListItem
{
    public int Id { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime RequestDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public int? CreatedById { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class ProductRequestHeader
{
    public int Id { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime RequestDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = string.Empty;

    public int? CreatedById { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }

    public int? SubmittedById { get; set; }
    public string? SubmittedByName { get; set; }
    public DateTime? SubmittedAt { get; set; }

    public int? ApprovedById { get; set; }
    public string? ApprovedByName { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovalNotes { get; set; }

    public int? RejectedById { get; set; }
    public string? RejectedByName { get; set; }
    public DateTime? RejectedAt { get; set; }
    public string? RejectionReason { get; set; }

    public int? CompletedById { get; set; }
    public string? CompletedByName { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletionNotes { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public List<ProductRequestDetail> Details { get; set; } = new List<ProductRequestDetail>();
}

public class CreateProductRequestHeader
{
    public int CostCenterId { get; set; }
    public DateTime RequestDate { get; set; }
    public string? Notes { get; set; }
    public List<CreateProductRequestDetail>? Details { get; set; }
}

public class UpdateProductRequestHeader
{
    public int Id { get; set; }
    public int CostCenterId { get; set; }
    public DateTime RequestDate { get; set; }
    public string? Notes { get; set; }
}

public class ProductRequestDetail
{
    public int Id { get; set; }
    public int ProductRequestHeaderId { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal Quantity { get; set; }
    public decimal? Price { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? Notes { get; set; }
}

public class CreateProductRequestDetail
{
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? Price { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? Notes { get; set; }
}

public class UpdateProductRequestDetail
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? Price { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? Notes { get; set; }
}

public class SubmitProductRequestDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class ApproveProductRequestDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class RejectProductRequestDto
{
    public int Id { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class CompleteProductRequestDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}
