import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Observable, forkJoin, map, of, startWith } from 'rxjs';
import { TransactionService } from '../../../../core/services/transaction.service';
import { ProductService } from '../../../../core/services/product.service';
import { SupplierService } from '../../../../core/services/supplier.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { UnitService } from '../../../../core/services/unit.service';
import { ErrorService } from '../../../../core/services/error.service';
import { Product } from '../../../../core/models/product.model';
import { SupplierListItem } from '../../../../core/services/supplier.service';
import { CostCenterList } from '../../../../core/models/cost-center.model';
import { Unit } from '../../../../core/models/unit.model';
import { CreateProductOrder, TransactionHeader } from '../../../../core/models/transaction.model';

@Component({
  selector: 'app-purchase-order',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './purchase-order.component.html',
  styleUrls: ['./purchase-order.component.scss']
})
export class PurchaseOrderComponent implements OnInit {
  purchaseOrderForm!: FormGroup;
  isLoading = false;
  isEditMode = false;
  purchaseOrderId: number | null = null;
  productRequestId: number | null = null;

  displayedColumns: string[] = [
    'productCode',
    'productName',
    'unitName',
    'quantity',
    'unitPrice',
    'taxAmount',
    'discountAmount',
    'total',
    'actions'
  ];

  products: Product[] = [];
  suppliers: SupplierListItem[] = [];
  costCenters: CostCenterList[] = [];
  units: Unit[] = [];
  filteredProducts: Observable<Product[]>[] = [];

  constructor(
    private fb: FormBuilder,
    private transactionService: TransactionService,
    private productService: ProductService,
    private supplierService: SupplierService,
    private costCenterService: CostCenterService,
    private unitService: UnitService,
    private errorService: ErrorService,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadReferenceData();

    // Check if we're creating from a product request
    this.route.queryParams.subscribe(params => {
      if (params['requestId']) {
        this.productRequestId = +params['requestId'];
        this.loadProductRequest(this.productRequestId);
      }
    });

    // Check if we're editing an existing purchase order
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.isEditMode = true;
        this.purchaseOrderId = parseInt(id, 10);
        this.loadPurchaseOrder(this.purchaseOrderId);
      }
    });
  }

  initForm(): void {
    this.purchaseOrderForm = this.fb.group({
      sourceCostCenterId: ['', Validators.required],
      supplierId: ['', Validators.required],
      transactionDate: [new Date(), Validators.required],
      referenceNumber: [''],
      notes: [''],
      details: this.fb.array([])
    });

    // Add an empty row by default
    this.addItem();
  }

  get details(): FormArray {
    return this.purchaseOrderForm.get('details') as FormArray;
  }

  addItem(): void {
    const detailForm = this.fb.group({
      productId: ['', Validators.required],
      productCode: [''],
      productName: [''],
      unitId: ['', Validators.required],
      unitName: [''],
      quantity: [1, [Validators.required, Validators.min(0.01)]],
      unitPrice: [0, [Validators.required, Validators.min(0)]],
      taxId: [''],
      taxRate: [0],
      taxAmount: [0],
      discountPercentage: [0],
      discountAmount: [0],
      total: [0]
    });

    // Set up product autocomplete filtering
    const index = this.details.length;
    this.setupProductAutocomplete(detailForm, index);

    // Auto-calculate total when quantity or unit price changes
    detailForm.get('quantity')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(detailForm);
    });

    detailForm.get('unitPrice')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(detailForm);
    });

    detailForm.get('taxRate')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(detailForm);
    });

    detailForm.get('discountPercentage')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(detailForm);
    });

    this.details.push(detailForm);
  }

  removeItem(index: number): void {
    this.details.removeAt(index);
    this.filteredProducts.splice(index, 1);
  }

  loadReferenceData(): void {
    this.isLoading = true;

    forkJoin({
      products: this.productService.getAll(),
      suppliers: this.supplierService.getAll(),
      costCenters: this.costCenterService.getAll(),
      units: this.unitService.getAll()
    }).subscribe({
      next: (data) => {
        this.products = data.products;
        this.suppliers = data.suppliers;
        this.costCenters = data.costCenters;
        this.units = data.units;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorService.handleError(error);
        this.isLoading = false;
      }
    });
  }

  loadProductRequest(requestId: number): void {
    this.isLoading = true;
    this.transactionService.getById(requestId).subscribe({
      next: (request) => {
        console.log('Loaded product request:', request);
        console.log('Source cost center ID:', request.sourceCostCenterId);
        console.log('Reference number:', request.referenceNumber || request.transactionNumber);

        if (request.status !== 'Approved') {
          this.snackBar.open('This product request is not approved. Only approved requests can be converted to purchase orders.', 'Close', { duration: 5000 });
          this.router.navigate(['/transactions/purchase-orders']);
          this.isLoading = false;
          return;
        }

        // Make sure we have a source cost center ID
        if (!request.sourceCostCenterId) {
          this.snackBar.open('This product request does not have a valid cost center. Please update the request and try again.', 'Close', { duration: 5000 });
          this.router.navigate(['/transactions/purchase-orders']);
          this.isLoading = false;
          return;
        }

        // Set the cost center from the request
        this.purchaseOrderForm.patchValue({
          sourceCostCenterId: request.sourceCostCenterId,
          transactionDate: new Date(),
          referenceNumber: `PO-${request.referenceNumber || request.transactionNumber}`,
          notes: `Created from Product Request: ${request.referenceNumber || request.transactionNumber}`
        });

        // Clear existing items
        while (this.details.length) {
          this.details.removeAt(0);
        }
        this.filteredProducts = [];

        // Add items from the request
        if (request.details && request.details.length > 0) {
          request.details.forEach(detail => {
            // Find the product to get its details
            const product = this.products.find(p => p.id === detail.productId);

            const detailForm = this.fb.group({
              productId: [detail.productId, Validators.required],
              productCode: [product?.code || detail.productCode || ''],
              productName: [product?.name || detail.productName || ''],
              unitId: [detail.unitId || product?.unitId, Validators.required],
              unitName: [this.units.find(u => u.id === (detail.unitId || product?.unitId))?.name || ''],
              quantity: [detail.quantity, [Validators.required, Validators.min(0.01)]],
              unitPrice: [detail.unitPrice || product?.costPrice || 0, [Validators.required, Validators.min(0)]],
              taxId: [detail.taxId || ''],
              taxRate: [detail.taxRate || 0],
              taxAmount: [detail.taxAmount || 0],
              discountPercentage: [detail.discountPercent || 0],
              discountAmount: [detail.discountAmount || 0],
              total: [detail.totalAmount || 0]
            });

            // Set up product autocomplete filtering
            const index = this.details.length;
            this.setupProductAutocomplete(detailForm, index);

            // Auto-calculate total when quantity or unit price changes
            detailForm.get('quantity')?.valueChanges.subscribe(() => {
              this.calculateItemTotal(detailForm);
            });

            detailForm.get('unitPrice')?.valueChanges.subscribe(() => {
              this.calculateItemTotal(detailForm);
            });

            detailForm.get('taxRate')?.valueChanges.subscribe(() => {
              this.calculateItemTotal(detailForm);
            });

            detailForm.get('discountPercentage')?.valueChanges.subscribe(() => {
              this.calculateItemTotal(detailForm);
            });

            this.details.push(detailForm);
            this.calculateItemTotal(detailForm);
          });
        } else {
          // If no details, add an empty row
          this.addItem();
        }

        this.isLoading = false;
      },
      error: (error) => {
        this.errorService.handleError(error);
        this.snackBar.open('Error loading product request. Please try again.', 'Close', { duration: 5000 });
        this.isLoading = false;
      }
    });
  }

  loadPurchaseOrder(id: number): void {
    this.isLoading = true;
    this.transactionService.getById(id).subscribe({
      next: (order) => {
        // Set the form values
        this.purchaseOrderForm.patchValue({
          sourceCostCenterId: order.sourceCostCenterId,
          supplierId: order.supplierId,
          transactionDate: new Date(order.transactionDate),
          referenceNumber: order.referenceNumber,
          notes: order.notes
        });

        // Clear existing items
        while (this.details.length) {
          this.details.removeAt(0);
        }
        this.filteredProducts = [];

        // Add items from the order
        order.details.forEach(detail => {
          const detailForm = this.fb.group({
            productId: [detail.productId, Validators.required],
            productCode: [detail.productCode],
            productName: [detail.productName],
            unitId: [detail.unitId, Validators.required],
            unitName: [detail.unitName],
            quantity: [detail.quantity, [Validators.required, Validators.min(0.01)]],
            unitPrice: [detail.unitPrice || 0, [Validators.required, Validators.min(0)]],
            taxId: [detail.taxId],
            taxRate: [detail.taxRate || 0],
            taxAmount: [detail.taxAmount || 0],
            discountPercentage: [detail.discountPercent || 0],
            discountAmount: [detail.discountAmount || 0],
            total: [detail.totalAmount || 0]
          });

          // Set up product autocomplete filtering
          const index = this.details.length;
          this.setupProductAutocomplete(detailForm, index);

          // Auto-calculate total when quantity or unit price changes
          detailForm.get('quantity')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          detailForm.get('unitPrice')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          detailForm.get('taxRate')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          detailForm.get('discountPercentage')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          this.details.push(detailForm);
          this.calculateItemTotal(detailForm);
        });

        this.isLoading = false;
      },
      error: (error) => {
        this.errorService.handleError(error);
        this.isLoading = false;
      }
    });
  }

  setupProductAutocomplete(form: FormGroup, index: number): void {
    // Initialize the filtered products array for this index if it doesn't exist
    if (!this.filteredProducts[index]) {
      this.filteredProducts[index] = of([]);
    }

    // Set up the filter
    this.filteredProducts[index] = form.get('productCode')!.valueChanges.pipe(
      startWith(''),
      map(value => {
        const filterValue = value.toLowerCase();
        return this.products.filter(product =>
          product.code.toLowerCase().includes(filterValue) ||
          product.name.toLowerCase().includes(filterValue)
        );
      })
    );
  }

  selectProduct(index: number, product: Product): void {
    const detailForm = this.details.at(index) as FormGroup;

    detailForm.patchValue({
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      unitId: product.unitId,
      unitName: this.units.find(u => u.id === product.unitId)?.name || '',
      unitPrice: product.costPrice || 0
    });

    this.calculateItemTotal(detailForm);
  }

  calculateItemTotal(form: FormGroup): void {
    const quantity = form.get('quantity')?.value || 0;
    const unitPrice = form.get('unitPrice')?.value || 0;
    const taxRate = form.get('taxRate')?.value || 0;
    const discountPercentage = form.get('discountPercentage')?.value || 0;

    let subtotal = quantity * unitPrice;

    // Apply discount
    const discountAmount = subtotal * (discountPercentage / 100);
    form.get('discountAmount')?.setValue(discountAmount, { emitEvent: false });
    subtotal -= discountAmount;

    // Apply tax
    const taxAmount = subtotal * (taxRate / 100);
    form.get('taxAmount')?.setValue(taxAmount, { emitEvent: false });

    // Calculate total
    const total = subtotal + taxAmount;
    form.get('total')?.setValue(total, { emitEvent: false });
  }

  calculateOrderTotal(): number {
    let total = 0;
    for (let i = 0; i < this.details.length; i++) {
      const detailForm = this.details.at(i) as FormGroup;
      total += detailForm.get('total')?.value || 0;
    }
    return total;
  }

  savePurchaseOrder(): void {
    if (this.purchaseOrderForm.invalid) {
      this.markFormGroupTouched(this.purchaseOrderForm);
      this.snackBar.open('Please fix the errors in the form before saving.', 'Close', { duration: 3000 });
      return;
    }

    this.isLoading = true;

    const formValue = this.purchaseOrderForm.getRawValue();
    const purchaseOrder: CreateProductOrder = {
      sourceCostCenterId: formValue.sourceCostCenterId,
      supplierId: formValue.supplierId,
      transactionDate: formValue.transactionDate,
      referenceNumber: formValue.referenceNumber,
      notes: formValue.notes,
      details: formValue.details.map((detail: any) => ({
        productId: detail.productId,
        unitId: detail.unitId,
        quantity: detail.quantity,
        unitPrice: detail.unitPrice,
        taxId: detail.taxId,
        discountPercentage: detail.discountPercentage,
        notes: detail.notes
      }))
    };

    if (this.isEditMode && this.purchaseOrderId) {
      // Update existing purchase order
      this.transactionService.updateProductOrder(this.purchaseOrderId, purchaseOrder).subscribe({
        next: () => {
          this.snackBar.open('Purchase order updated successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/transactions/purchase-orders']);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    } else if (this.productRequestId) {
      // Create from product request
      this.transactionService.createProductOrderFromRequest(this.productRequestId, purchaseOrder).subscribe({
        next: (response) => {
          this.snackBar.open('Purchase order created successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/transactions/purchase-orders', response.id]);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    } else {
      // Create new purchase order
      this.transactionService.createProductOrder(purchaseOrder).subscribe({
        next: (response) => {
          this.snackBar.open('Purchase order created successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/transactions/purchase-orders', response.id]);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    }
  }

  submitPurchaseOrder(): void {
    if (!this.purchaseOrderId) {
      this.snackBar.open('Please save the purchase order before submitting.', 'Close', { duration: 3000 });
      return;
    }

    this.isLoading = true;
    this.transactionService.submitProductOrder(this.purchaseOrderId).subscribe({
      next: () => {
        this.snackBar.open('Purchase order submitted successfully', 'Close', { duration: 3000 });
        this.isLoading = false;
        this.router.navigate(['/transactions/purchase-orders']);
      },
      error: (error) => {
        this.errorService.handleError(error);
        this.isLoading = false;
      }
    });
  }

  cancelPurchaseOrder(): void {
    if (!this.purchaseOrderId) {
      this.router.navigate(['/transactions/purchase-orders']);
      return;
    }

    if (confirm('Are you sure you want to cancel this purchase order?')) {
      this.isLoading = true;
      this.transactionService.cancelProductOrder(this.purchaseOrderId).subscribe({
        next: () => {
          this.snackBar.open('Purchase order cancelled successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/transactions/purchase-orders']);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        for (let i = 0; i < control.length; i++) {
          const arrayControl = control.at(i);
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        }
      }
    });
  }

  canSubmit(): boolean {
    return this.isEditMode &&
           this.purchaseOrderId !== null &&
           this.purchaseOrderForm.valid &&
           this.details.length > 0;
  }

  goBack(): void {
    this.router.navigate(['/transactions/purchase-orders']);
  }
}