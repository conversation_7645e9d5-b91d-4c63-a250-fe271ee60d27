using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using SCM.API.DTOs;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Infrastructure.Data;

namespace SCM.API.Controllers;

public class LocationsController : ApiControllerBase
{
    private readonly ILocationService _locationService;
    private readonly ApplicationDbContext _dbContext;

    public LocationsController(ILocationService locationService, ApplicationDbContext dbContext)
    {
        _locationService = locationService;
        _dbContext = dbContext;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<LocationSimpleDto>>> GetAll()
    {
        try
        {
            // Query locations directly from the database
            var locations = await _dbContext.Locations
                .Select(l => new LocationSimpleDto
                {
                    Id = l.Id,
                    Name = l.Name,
                    Code = l.Code,
                    Description = l.Description,
                    CompanyId = l.CompanyId,
                    CompanyName = l.Company != null ? l.Company.Name : null,
                    Address = l.Address,
                    City = l.City,
                    State = l.State,
                    Country = l.Country,
                    PostalCode = l.PostalCode,
                    Phone = l.Phone,
                    Email = l.Email,
                    IsActive = l.IsActive,
                    CreatedAt = l.CreatedAt,
                    UpdatedAt = l.UpdatedAt,
                    StoresCount = l.Stores.Count,
                    CostCentersCount = l.CostCenters.Count
                })
                .ToListAsync();

            return Ok(locations);
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.GetAll: {ex.Message}");
            return StatusCode(500, "An error occurred while retrieving locations. Please try again later.");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<LocationSimpleDto>> GetById(int id)
    {
        try
        {
            // Query location directly from the database
            var location = await _dbContext.Locations
                .Where(l => l.Id == id)
                .Select(l => new LocationSimpleDto
                {
                    Id = l.Id,
                    Name = l.Name,
                    Code = l.Code,
                    Description = l.Description,
                    CompanyId = l.CompanyId,
                    CompanyName = l.Company != null ? l.Company.Name : null,
                    Address = l.Address,
                    City = l.City,
                    State = l.State,
                    Country = l.Country,
                    PostalCode = l.PostalCode,
                    Phone = l.Phone,
                    Email = l.Email,
                    IsActive = l.IsActive,
                    CreatedAt = l.CreatedAt,
                    UpdatedAt = l.UpdatedAt,
                    StoresCount = l.Stores.Count,
                    CostCentersCount = l.CostCenters.Count
                })
                .FirstOrDefaultAsync();

            if (location == null)
                return NotFound();

            return Ok(location);
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.GetById: {ex.Message}");
            return StatusCode(500, "An error occurred while retrieving the location. Please try again later.");
        }
    }

    [HttpGet("company/{companyId}")]
    public async Task<ActionResult<IEnumerable<LocationSimpleDto>>> GetByCompanyId(int companyId)
    {
        try
        {
            // Query locations directly from the database
            var locations = await _dbContext.Locations
                .Where(l => l.CompanyId == companyId)
                .Select(l => new LocationSimpleDto
                {
                    Id = l.Id,
                    Name = l.Name,
                    Code = l.Code,
                    Description = l.Description,
                    CompanyId = l.CompanyId,
                    CompanyName = l.Company != null ? l.Company.Name : null,
                    Address = l.Address,
                    City = l.City,
                    State = l.State,
                    Country = l.Country,
                    PostalCode = l.PostalCode,
                    Phone = l.Phone,
                    Email = l.Email,
                    IsActive = l.IsActive,
                    CreatedAt = l.CreatedAt,
                    UpdatedAt = l.UpdatedAt,
                    StoresCount = l.Stores.Count,
                    CostCentersCount = l.CostCenters.Count
                })
                .ToListAsync();

            return Ok(locations);
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.GetByCompanyId: {ex.Message}");
            return StatusCode(500, "An error occurred while retrieving locations. Please try again later.");
        }
    }

    [HttpGet("{id}/stores")]
    public async Task<ActionResult<LocationSimpleDto>> GetWithStores(int id)
    {
        try
        {
            // Query location with stores directly from the database
            var location = await _dbContext.Locations
                .Where(l => l.Id == id)
                .Select(l => new
                {
                    Location = new LocationSimpleDto
                    {
                        Id = l.Id,
                        Name = l.Name,
                        Code = l.Code,
                        Description = l.Description,
                        CompanyId = l.CompanyId,
                        CompanyName = l.Company != null ? l.Company.Name : null,
                        Address = l.Address,
                        City = l.City,
                        State = l.State,
                        Country = l.Country,
                        PostalCode = l.PostalCode,
                        Phone = l.Phone,
                        Email = l.Email,
                        IsActive = l.IsActive,
                        CreatedAt = l.CreatedAt,
                        UpdatedAt = l.UpdatedAt,
                        StoresCount = l.Stores.Count,
                        CostCentersCount = l.CostCenters.Count
                    },
                    Stores = l.Stores.Select(s => new
                    {
                        s.Id,
                        s.Name,
                        s.LocationId,
                        s.IsSalesPoint,
                        s.IsActive
                    }).ToList()
                })
                .FirstOrDefaultAsync();

            if (location == null)
                return NotFound();

            return Ok(new
            {
                location.Location,
                location.Stores
            });
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.GetWithStores: {ex.Message}");
            return StatusCode(500, "An error occurred while retrieving the location with stores. Please try again later.");
        }
    }

    [HttpGet("{id}/cost-centers")]
    public async Task<ActionResult<LocationSimpleDto>> GetWithCostCenters(int id)
    {
        try
        {
            // Query location with cost centers directly from the database
            var location = await _dbContext.Locations
                .Where(l => l.Id == id)
                .Select(l => new
                {
                    Location = new LocationSimpleDto
                    {
                        Id = l.Id,
                        Name = l.Name,
                        Code = l.Code,
                        Description = l.Description,
                        CompanyId = l.CompanyId,
                        CompanyName = l.Company != null ? l.Company.Name : null,
                        Address = l.Address,
                        City = l.City,
                        State = l.State,
                        Country = l.Country,
                        PostalCode = l.PostalCode,
                        Phone = l.Phone,
                        Email = l.Email,
                        IsActive = l.IsActive,
                        CreatedAt = l.CreatedAt,
                        UpdatedAt = l.UpdatedAt,
                        StoresCount = l.Stores.Count,
                        CostCentersCount = l.CostCenters.Count
                    },
                    CostCenters = l.CostCenters.Select(cc => new
                    {
                        cc.Id,
                        cc.Name,
                        cc.StoreId,
                        cc.TypeId,
                        cc.TypeName,
                        cc.IsActive
                    }).ToList()
                })
                .FirstOrDefaultAsync();

            if (location == null)
                return NotFound();

            return Ok(new
            {
                location.Location,
                location.CostCenters
            });
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.GetWithCostCenters: {ex.Message}");
            return StatusCode(500, "An error occurred while retrieving the location with cost centers. Please try again later.");
        }
    }

    [HttpPost]
    public async Task<ActionResult<LocationSimpleDto>> Create(CreateLocationDto createLocationDto)
    {
        try
        {
            // Create a new location entity
            var location = new Domain.Entities.Location
            {
                Name = createLocationDto.Name,
                Code = createLocationDto.Code,
                Description = createLocationDto.Description,
                CompanyId = createLocationDto.CompanyId,
                Address = createLocationDto.Address,
                City = createLocationDto.City,
                State = createLocationDto.State,
                Country = createLocationDto.Country,
                PostalCode = createLocationDto.PostalCode,
                Phone = createLocationDto.Phone,
                Email = createLocationDto.Email,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            // Detach any existing Company entity to avoid tracking issues
            var existingCompany = _dbContext.ChangeTracker.Entries<Domain.Entities.Company>()
                .FirstOrDefault(e => e.Entity.Id == createLocationDto.CompanyId);
            if (existingCompany != null)
            {
                existingCompany.State = EntityState.Detached;
            }

            // Add the location to the database
            _dbContext.Locations.Add(location);
            await _dbContext.SaveChangesAsync();

            // Get the company name for the response
            string? companyName = null;
            if (location.CompanyId > 0)
            {
                var company = await _dbContext.Companies.FindAsync(location.CompanyId);
                companyName = company?.Name;
            }

            // Create a DTO for the response
            var locationDto = new LocationSimpleDto
            {
                Id = location.Id,
                Name = location.Name,
                Code = location.Code,
                Description = location.Description,
                CompanyId = location.CompanyId,
                CompanyName = companyName,
                Address = location.Address,
                City = location.City,
                State = location.State,
                Country = location.Country,
                PostalCode = location.PostalCode,
                Phone = location.Phone,
                Email = location.Email,
                IsActive = location.IsActive,
                CreatedAt = location.CreatedAt,
                UpdatedAt = location.UpdatedAt,
                StoresCount = 0,
                CostCentersCount = 0
            };

            return CreatedAtAction(nameof(GetById), new { id = locationDto.Id }, locationDto);
        }
        catch (Exception ex)
        {
            // Check if it's a SQL exception for unique constraint violation
            if (ex.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
            {
                // Check if it's specifically the location name constraint
                if (sqlEx.Message.Contains("UQ_Location_Name_CompanyId"))
                {
                    return BadRequest($"A location with the name '{createLocationDto.Name}' already exists for this company. Please use a different name.");
                }
            }

            // Log the exception
            Console.WriteLine($"Error in LocationsController.Create: {ex.Message}");

            // For other exceptions, return a generic error
            return BadRequest("An error occurred while creating the location. Please try again.");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateLocationDto updateLocationDto)
    {
        if (id != updateLocationDto.Id)
            return BadRequest();

        try
        {
            // Find the location in the database
            var location = await _dbContext.Locations.FindAsync(id);
            if (location == null)
                return NotFound();

            // Update the location properties
            location.Name = updateLocationDto.Name;
            location.Code = updateLocationDto.Code;
            location.Description = updateLocationDto.Description;
            location.CompanyId = updateLocationDto.CompanyId;
            location.Address = updateLocationDto.Address;
            location.City = updateLocationDto.City;
            location.State = updateLocationDto.State;
            location.Country = updateLocationDto.Country;
            location.PostalCode = updateLocationDto.PostalCode;
            location.Phone = updateLocationDto.Phone;
            location.Email = updateLocationDto.Email;
            location.IsActive = updateLocationDto.IsActive;
            location.UpdatedAt = DateTime.UtcNow;

            // Save the changes to the database
            await _dbContext.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            // Check if it's a SQL exception for unique constraint violation
            if (ex.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
            {
                // Check if it's specifically the location name constraint
                if (sqlEx.Message.Contains("UQ_Location_Name_CompanyId"))
                {
                    return BadRequest($"A location with the name '{updateLocationDto.Name}' already exists for this company. Please use a different name.");
                }
            }

            // Log the exception
            Console.WriteLine($"Error in LocationsController.Update: {ex.Message}");

            return BadRequest("An error occurred while updating the location. Please try again.");
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            // Find the location in the database
            var location = await _dbContext.Locations.FindAsync(id);
            if (location == null)
                return NotFound();

            // Remove the location from the database
            _dbContext.Locations.Remove(location);
            await _dbContext.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.Delete: {ex.Message}");

            return BadRequest("An error occurred while deleting the location. Please try again.");
        }
    }

    [HttpPut("{id}/activate")]
    public async Task<IActionResult> Activate(int id)
    {
        try
        {
            // Find the location in the database
            var location = await _dbContext.Locations.FindAsync(id);
            if (location == null)
                return NotFound();

            // Update the IsActive property
            location.IsActive = true;
            location.UpdatedAt = DateTime.UtcNow;

            // Save the changes to the database
            await _dbContext.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.Activate: {ex.Message}");

            return BadRequest("An error occurred while activating the location. Please try again.");
        }
    }

    [HttpPut("{id}/deactivate")]
    public async Task<IActionResult> Deactivate(int id)
    {
        try
        {
            // Find the location in the database
            var location = await _dbContext.Locations.FindAsync(id);
            if (location == null)
                return NotFound();

            // Update the IsActive property
            location.IsActive = false;
            location.UpdatedAt = DateTime.UtcNow;

            // Save the changes to the database
            await _dbContext.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LocationsController.Deactivate: {ex.Message}");

            return BadRequest("An error occurred while deactivating the location. Please try again.");
        }
    }
}
