using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class ProductRequestDetailEntity : BaseEntity
{
    public int ProductRequestHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? Price { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual ProductRequestHeaderEntity ProductRequestHeader { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    public virtual Unit? Unit { get; set; }
}
