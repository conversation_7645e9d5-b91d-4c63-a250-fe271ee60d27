using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class StockTransferService : IStockTransferService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IStockService _stockService;

    public StockTransferService(
        ApplicationDbContext dbContext,
        IMapper mapper,
        IStockService stockService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _stockService = stockService;
    }

    public async Task<IEnumerable<StockTransferHeaderDto>> GetAllStockTransfersAsync()
    {
        var stockTransfers = await _dbContext.StockTransferHeaders
            .Include(st => st.FromCostCenter)
            .Include(st => st.ToCostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .OrderByDescending(st => st.TransferDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTransferHeaderDto>>(stockTransfers);
    }

    public async Task<StockTransferHeaderDto?> GetStockTransferByIdAsync(int id)
    {
        var stockTransfer = await _dbContext.StockTransferHeaders
            .Include(st => st.FromCostCenter)
            .Include(st => st.ToCostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .Include(st => st.Details)
            .FirstOrDefaultAsync(st => st.Id == id);

        if (stockTransfer == null)
            return null;

        // Load related data for stock transfer details
        foreach (var detail in stockTransfer.Details)
        {
            // Load product information
            if (detail.ProductId > 0)
            {
                detail.Product = await _dbContext.Products.FindAsync(detail.ProductId);
            }

            // Load batch information
            if (detail.BatchId.HasValue)
            {
                detail.Batch = await _dbContext.Batches.FindAsync(detail.BatchId.Value);
            }

            // Load unit information
            if (detail.UnitId.HasValue)
            {
                detail.Unit = await _dbContext.Units.FindAsync(detail.UnitId.Value);
            }
        }

        return _mapper.Map<StockTransferHeaderDto>(stockTransfer);
    }

    public async Task<IEnumerable<StockTransferHeaderDto>> GetStockTransfersByFromCostCenterIdAsync(int costCenterId)
    {
        var stockTransfers = await _dbContext.StockTransferHeaders
            .Include(st => st.FromCostCenter)
            .Include(st => st.ToCostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .Where(st => st.FromCostCenterId == costCenterId)
            .OrderByDescending(st => st.TransferDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTransferHeaderDto>>(stockTransfers);
    }

    public async Task<IEnumerable<StockTransferHeaderDto>> GetStockTransfersByToCostCenterIdAsync(int costCenterId)
    {
        var stockTransfers = await _dbContext.StockTransferHeaders
            .Include(st => st.FromCostCenter)
            .Include(st => st.ToCostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .Where(st => st.ToCostCenterId == costCenterId)
            .OrderByDescending(st => st.TransferDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTransferHeaderDto>>(stockTransfers);
    }

    public async Task<IEnumerable<StockTransferHeaderDto>> GetStockTransfersByStatusAsync(string status)
    {
        var stockTransfers = await _dbContext.StockTransferHeaders
            .Include(st => st.FromCostCenter)
            .Include(st => st.ToCostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.CompletedBy)
            .Where(st => st.Status == status)
            .OrderByDescending(st => st.TransferDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTransferHeaderDto>>(stockTransfers);
    }

    public async Task<StockTransferHeaderDto> CreateStockTransferAsync(CreateStockTransferHeaderDto createStockTransferHeaderDto)
    {
        // Generate reference number
        var referenceNumber = await GenerateReferenceNumberAsync();

        // Get the Transfer stage type
        var transferStage = await _dbContext.TransactionStageTypes
            .FirstOrDefaultAsync(tst => tst.Name == "Transfer");
        if (transferStage == null)
            throw new InvalidOperationException("Transfer stage type not found.");

        var stockTransfer = _mapper.Map<StockTransferHeader>(createStockTransferHeaderDto);
        stockTransfer.ReferenceNumber = referenceNumber;
        stockTransfer.Status = "Draft";
        stockTransfer.CreatedById = 1; // TODO: Get from current user

        _dbContext.StockTransferHeaders.Add(stockTransfer);
        await _dbContext.SaveChangesAsync();

        // Create corresponding transaction header
        var transaction = new TransactionHeader
        {
            TransactionNumber = referenceNumber,
            ReferenceNumber = referenceNumber,
            StageTypeId = transferStage.Id,
            TransactionTypeId = 3, // Stock Transfer transaction type
            SourceCostCenterId = stockTransfer.FromCostCenterId,
            DestinationCostCenterId = stockTransfer.ToCostCenterId,
            TransactionDate = stockTransfer.TransferDate,
            Status = "Draft",
            Notes = stockTransfer.Notes,
            CreatedById = stockTransfer.CreatedById ?? 1,
            CreatedAt = DateTime.UtcNow,
            IsActive = true,
            SubTotal = 0m,
            TaxAmount = 0m,
            TotalAmount = 0m,
            DiscountAmount = 0m,
            DiscountPercentage = 0m
        };

        _dbContext.TransactionHeaders.Add(transaction);
        await _dbContext.SaveChangesAsync();

        // Add details if provided
        if (createStockTransferHeaderDto.Details != null && createStockTransferHeaderDto.Details.Any())
        {
            foreach (var detailDto in createStockTransferHeaderDto.Details)
            {
                await CreateStockTransferDetailAsync(stockTransfer.Id, detailDto, transaction.Id);
            }
        }

        var createdStockTransfer = await _dbContext.StockTransferHeaders
            .Include(st => st.FromCostCenter)
            .Include(st => st.ToCostCenter)
            .Include(st => st.CreatedBy)
            .Include(st => st.Details)
                .ThenInclude(d => d.Product)
            .Include(st => st.Details)
                .ThenInclude(d => d.Batch)
            .Include(st => st.Details)
                .ThenInclude(d => d.Unit)
            .FirstOrDefaultAsync(st => st.Id == stockTransfer.Id);

        return _mapper.Map<StockTransferHeaderDto>(createdStockTransfer);
    }

    public async Task UpdateStockTransferAsync(UpdateStockTransferHeaderDto updateStockTransferHeaderDto)
    {
        var stockTransfer = await _dbContext.StockTransferHeaders
            .FirstOrDefaultAsync(st => st.Id == updateStockTransferHeaderDto.Id);
            
        if (stockTransfer == null)
            throw new KeyNotFoundException($"StockTransfer with ID {updateStockTransferHeaderDto.Id} not found.");
            
        if (stockTransfer.Status != "Draft")
            throw new InvalidOperationException("Only stock transfers in Draft status can be updated.");
            
        _mapper.Map(updateStockTransferHeaderDto, stockTransfer);
        
        _dbContext.StockTransferHeaders.Update(stockTransfer);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockTransferAsync(int id)
    {
        var stockTransfer = await _dbContext.StockTransferHeaders
            .Include(st => st.Details)
            .FirstOrDefaultAsync(st => st.Id == id);
            
        if (stockTransfer == null)
            throw new KeyNotFoundException($"StockTransfer with ID {id} not found.");
            
        if (stockTransfer.Status != "Draft")
            throw new InvalidOperationException("Only stock transfers in Draft status can be deleted.");
            
        // Remove details first
        _dbContext.StockTransferDetails.RemoveRange(stockTransfer.Details);
        
        // Then remove header
        _dbContext.StockTransferHeaders.Remove(stockTransfer);
        
        await _dbContext.SaveChangesAsync();
    }

    public async Task CompleteStockTransferAsync(CompleteStockTransferDto completeStockTransferDto)
    {
        var stockTransfer = await _dbContext.StockTransferHeaders
            .Include(st => st.Details)
                .ThenInclude(d => d.Product)
            .Include(st => st.Details)
                .ThenInclude(d => d.Batch)
            .FirstOrDefaultAsync(st => st.Id == completeStockTransferDto.Id);
            
        if (stockTransfer == null)
            throw new KeyNotFoundException($"StockTransfer with ID {completeStockTransferDto.Id} not found.");
            
        if (stockTransfer.Status != "Draft" && stockTransfer.Status != "Pending")
            throw new InvalidOperationException("Only stock transfers in Draft or Pending status can be completed.");
            
        if (!stockTransfer.Details.Any())
            throw new InvalidOperationException("Cannot complete a stock transfer with no details.");
            
        // Process the stock transfer by adjusting stock in both cost centers
        foreach (var detail in stockTransfer.Details)
        {
            // Reduce stock in the source cost center
            await _stockService.AdjustStockAsync(new StockAdjustmentDto
            {
                ProductId = detail.ProductId,
                CostCenterId = stockTransfer.FromCostCenterId,
                UnitId = detail.UnitId,
                Quantity = -detail.Quantity, // Negative to reduce stock
                CostPrice = detail.CostPrice,
                Reason = "Stock Transfer Out",
                Notes = $"Transfer #{stockTransfer.ReferenceNumber} to {stockTransfer.ToCostCenter.Name}"
            });
            
            // Increase stock in the destination cost center
            await _stockService.AdjustStockAsync(new StockAdjustmentDto
            {
                ProductId = detail.ProductId,
                CostCenterId = stockTransfer.ToCostCenterId,
                UnitId = detail.UnitId,
                Quantity = detail.Quantity, // Positive to increase stock
                CostPrice = detail.CostPrice,
                Reason = "Stock Transfer In",
                Notes = $"Transfer #{stockTransfer.ReferenceNumber} from {stockTransfer.FromCostCenter.Name}"
            });
        }
        
        // Update stock transfer header
        stockTransfer.Status = "Completed";
        stockTransfer.CompletedById = 1; // TODO: Get from current user
        stockTransfer.CompletedAt = DateTime.UtcNow;
        stockTransfer.Notes = completeStockTransferDto.Notes ?? stockTransfer.Notes;

        // Update corresponding transaction
        var transaction = await _dbContext.TransactionHeaders
            .FirstOrDefaultAsync(t => t.ReferenceNumber == stockTransfer.ReferenceNumber);
        if (transaction != null)
        {
            transaction.Status = "Completed";
            transaction.Notes = stockTransfer.Notes;
            _dbContext.TransactionHeaders.Update(transaction);
        }

        _dbContext.StockTransferHeaders.Update(stockTransfer);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelStockTransferAsync(int id)
    {
        var stockTransfer = await _dbContext.StockTransferHeaders
            .FirstOrDefaultAsync(st => st.Id == id);
            
        if (stockTransfer == null)
            throw new KeyNotFoundException($"StockTransfer with ID {id} not found.");
            
        if (stockTransfer.Status != "Draft" && stockTransfer.Status != "Pending")
            throw new InvalidOperationException("Only stock transfers in Draft or Pending status can be cancelled.");
            
        stockTransfer.Status = "Cancelled";
        
        _dbContext.StockTransferHeaders.Update(stockTransfer);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<StockTransferDetailDto>> GetStockTransferDetailsAsync(int stockTransferHeaderId)
    {
        var details = await _dbContext.StockTransferDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .Where(d => d.StockTransferHeaderId == stockTransferHeaderId)
            .OrderBy(d => d.Product.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockTransferDetailDto>>(details);
    }

    public async Task<StockTransferDetailDto?> GetStockTransferDetailByIdAsync(int id)
    {
        var detail = await _dbContext.StockTransferDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        return detail != null ? _mapper.Map<StockTransferDetailDto>(detail) : null;
    }

    public async Task<StockTransferDetailDto> CreateStockTransferDetailAsync(int stockTransferHeaderId, CreateStockTransferDetailDto createStockTransferDetailDto)
    {
        return await CreateStockTransferDetailAsync(stockTransferHeaderId, createStockTransferDetailDto, null);
    }

    public async Task<StockTransferDetailDto> CreateStockTransferDetailAsync(int stockTransferHeaderId, CreateStockTransferDetailDto createStockTransferDetailDto, int? transactionId)
    {
        var stockTransfer = await _dbContext.StockTransferHeaders
            .FirstOrDefaultAsync(st => st.Id == stockTransferHeaderId);

        if (stockTransfer == null)
            throw new KeyNotFoundException($"StockTransfer with ID {stockTransferHeaderId} not found.");

        if (stockTransfer.Status != "Draft")
            throw new InvalidOperationException("Can only add details to stock transfers in Draft status.");

        // Verify product exists
        var product = await _dbContext.Products.FindAsync(createStockTransferDetailDto.ProductId);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {createStockTransferDetailDto.ProductId} not found.");

        // Verify there is enough stock in the source cost center
        var stockOnHand = await _dbContext.StockOnHand
            .FirstOrDefaultAsync(s =>
                s.ProductId == createStockTransferDetailDto.ProductId &&
                s.CostCenterId == stockTransfer.FromCostCenterId);

        if (stockOnHand == null || stockOnHand.Quantity < createStockTransferDetailDto.Quantity)
            throw new InvalidOperationException($"Not enough stock of product {product.Name} in the source cost center.");

        var detail = _mapper.Map<StockTransferDetail>(createStockTransferDetailDto);
        detail.StockTransferHeaderId = stockTransferHeaderId;
        detail.CostPrice = stockOnHand.CostPrice;

        _dbContext.StockTransferDetails.Add(detail);
        await _dbContext.SaveChangesAsync();

        // Create corresponding transaction detail if transaction ID is provided
        if (transactionId.HasValue)
        {
            var transactionDetail = new TransactionDetail
            {
                TransactionHeaderId = transactionId.Value,
                ProductId = detail.ProductId,
                BatchId = detail.BatchId,
                UnitId = detail.UnitId,
                Quantity = detail.Quantity,
                Price = detail.CostPrice ?? 0m,
                Total = (detail.CostPrice ?? 0m) * detail.Quantity,
                Notes = detail.Notes,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _dbContext.TransactionDetails.Add(transactionDetail);
            await _dbContext.SaveChangesAsync();
        }

        var createdDetail = await _dbContext.StockTransferDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == detail.Id);

        return _mapper.Map<StockTransferDetailDto>(createdDetail);
    }

    public async Task UpdateStockTransferDetailAsync(UpdateStockTransferDetailDto updateStockTransferDetailDto)
    {
        var detail = await _dbContext.StockTransferDetails
            .Include(d => d.StockTransferHeader)
            .FirstOrDefaultAsync(d => d.Id == updateStockTransferDetailDto.Id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockTransferDetail with ID {updateStockTransferDetailDto.Id} not found.");
            
        if (detail.StockTransferHeader.Status != "Draft")
            throw new InvalidOperationException("Can only update details of stock transfers in Draft status.");
            
        // Verify there is enough stock in the source cost center if quantity is increased
        if (updateStockTransferDetailDto.Quantity > detail.Quantity)
        {
            var stockOnHand = await _dbContext.StockOnHand
                .FirstOrDefaultAsync(s =>
                    s.ProductId == updateStockTransferDetailDto.ProductId &&
                    s.CostCenterId == detail.StockTransferHeader.FromCostCenterId);
                    
            if (stockOnHand == null || stockOnHand.Quantity < (updateStockTransferDetailDto.Quantity - detail.Quantity))
                throw new InvalidOperationException($"Not enough stock of product {detail.Product.Name} in the source cost center.");
        }
        
        _mapper.Map(updateStockTransferDetailDto, detail);
        
        _dbContext.StockTransferDetails.Update(detail);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockTransferDetailAsync(int id)
    {
        var detail = await _dbContext.StockTransferDetails
            .Include(d => d.StockTransferHeader)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockTransferDetail with ID {id} not found.");
            
        if (detail.StockTransferHeader.Status != "Draft")
            throw new InvalidOperationException("Can only delete details of stock transfers in Draft status.");
            
        _dbContext.StockTransferDetails.Remove(detail);
        await _dbContext.SaveChangesAsync();
    }

    private async Task<string> GenerateReferenceNumberAsync()
    {
        // Get the current date
        var today = DateTime.UtcNow;
        var year = today.Year.ToString().Substring(2); // Last 2 digits of year
        var month = today.Month.ToString().PadLeft(2, '0');
        var day = today.Day.ToString().PadLeft(2, '0');
        
        // Get the count of transfers for today
        var todayTransfers = await _dbContext.StockTransferHeaders
            .CountAsync(st => st.CreatedAt.Date == today.Date);
        
        // Generate the reference number
        var sequence = (todayTransfers + 1).ToString().PadLeft(3, '0');
        return $"TRF-{year}{month}{day}-{sequence}";
    }
}
