namespace SCM.Application.DTOs;

public class UnitDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Abbreviation { get; set; }
    public int? UnitGroupId { get; set; }
    public string? UnitGroupName { get; set; }
    public decimal? ConversionFactor { get; set; }
    public decimal BaseConversionFactor { get; set; }
    public bool IsBaseUnit { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateUnitDto
{
    public string Name { get; set; } = string.Empty;
    public string? Abbreviation { get; set; }
    public int? UnitGroupId { get; set; }
    public decimal? ConversionFactor { get; set; } = 1;
    public decimal BaseConversionFactor { get; set; } = 1;
    public bool IsBaseUnit { get; set; } = false;
}

public class UpdateUnitDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Abbreviation { get; set; }
    public int? UnitGroupId { get; set; }
    public decimal? ConversionFactor { get; set; }
    public decimal BaseConversionFactor { get; set; }
    public bool IsBaseUnit { get; set; }
    public bool IsActive { get; set; }
}
