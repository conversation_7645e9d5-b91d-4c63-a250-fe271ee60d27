import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTreeModule, MatTreeNestedDataSource } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { NestedTreeControl } from '@angular/cdk/tree';
import { CompanyService } from '../../core/services/company.service';
import { LocationService } from '../../core/services/location.service';
import { StoreService } from '../../core/services/store.service';
import { CostCenterService } from '../../core/services/cost-center.service';
import { ErrorService } from '../../core/services/error.service';
import { Company } from '../../core/models/company.model';
import { Location } from '../../core/models/location.model';
import { Store } from '../../core/models/store.model';
import { CostCenter } from '../../core/models/cost-center.model';
import { forkJoin } from 'rxjs';

interface TreeNode {
  id: number;
  name: string;
  type: 'company' | 'location' | 'store' | 'costCenter';
  isActive: boolean;
  children?: TreeNode[];
}

@Component({
  selector: 'app-business-structure',
  standalone: true,
  imports: [
    CommonModule,
    MatTreeModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule,
    RouterModule
  ],
  templateUrl: './business-structure.component.html',
  styleUrls: ['./business-structure.component.scss']
})
export class BusinessStructureComponent implements OnInit {
  treeControl = new NestedTreeControl<TreeNode>(node => node.children);
  dataSource = new MatTreeNestedDataSource<TreeNode>();
  isLoading = false;
  selectedNode: TreeNode | null = null;

  constructor(
    private companyService: CompanyService,
    private locationService: LocationService,
    private storeService: StoreService,
    private costCenterService: CostCenterService,
    private errorService: ErrorService
  ) {}

  ngOnInit(): void {
    this.loadBusinessStructure();
  }

  loadBusinessStructure(): void {
    this.isLoading = true;
    
    this.companyService.getAll().subscribe({
      next: (companies) => {
        const treeData: TreeNode[] = companies.map(company => ({
          id: company.id,
          name: company.name,
          type: 'company' as const,
          isActive: company.isActive,
          children: []
        }));
        
        this.dataSource.data = treeData;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.errorService.handleError(error);
      }
    });
  }

  loadChildren(node: TreeNode): void {
    if (node.children && node.children.length > 0) {
      return; // Children already loaded
    }

    this.isLoading = true;
    
    switch (node.type) {
      case 'company':
        this.locationService.getByCompany(node.id).subscribe({
          next: (locations) => {
            node.children = locations.map(location => ({
              id: location.id,
              name: location.name,
              type: 'location' as const,
              isActive: location.isActive,
              children: []
            }));
            
            // Force update
            this.dataSource.data = [...this.dataSource.data];
            this.isLoading = false;
          },
          error: (error) => {
            this.isLoading = false;
            this.errorService.handleError(error);
          }
        });
        break;
        
      case 'location':
        this.storeService.getByLocation(node.id).subscribe({
          next: (stores) => {
            node.children = stores.map(store => ({
              id: store.id,
              name: store.name,
              type: 'store' as const,
              isActive: store.isActive,
              children: []
            }));
            
            // Force update
            this.dataSource.data = [...this.dataSource.data];
            this.isLoading = false;
          },
          error: (error) => {
            this.isLoading = false;
            this.errorService.handleError(error);
          }
        });
        break;
        
      case 'store':
        this.costCenterService.getByStore(node.id).subscribe({
          next: (costCenters) => {
            node.children = costCenters.map(costCenter => ({
              id: costCenter.id,
              name: costCenter.name,
              type: 'costCenter' as const,
              isActive: costCenter.isActive,
              children: []
            }));
            
            // Force update
            this.dataSource.data = [...this.dataSource.data];
            this.isLoading = false;
          },
          error: (error) => {
            this.isLoading = false;
            this.errorService.handleError(error);
          }
        });
        break;
    }
  }

  hasChild = (_: number, node: TreeNode) => 
    node.type !== 'costCenter';

  selectNode(node: TreeNode): void {
    this.selectedNode = node;
  }

  getNodeIcon(node: TreeNode): string {
    switch (node.type) {
      case 'company': return 'business';
      case 'location': return 'location_on';
      case 'store': return 'store';
      case 'costCenter': return 'account_balance';
      default: return 'folder';
    }
  }

  getNodeRoute(node: TreeNode): string[] {
    switch (node.type) {
      case 'company': return ['/business-structure/companies', node.id.toString()];
      case 'location': return ['/business-structure/locations', node.id.toString()];
      case 'store': return ['/business-structure/stores', node.id.toString()];
      case 'costCenter': return ['/business-structure/cost-centers', node.id.toString()];
      default: return ['/business-structure'];
    }
  }

  addCompany(): void {
    // Navigate to add company page
  }

  addLocation(companyId: number): void {
    // Navigate to add location page with companyId
  }

  addStore(locationId: number): void {
    // Navigate to add store page with locationId
  }

  addCostCenter(storeId: number): void {
    // Navigate to add cost center page with storeId
  }
}
