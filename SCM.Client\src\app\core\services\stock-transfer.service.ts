import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { 
  StockTransferHeader, 
  StockTransferDetail, 
  CreateStockTransferHeader, 
  UpdateStockTransferHeader, 
  CreateStockTransferDetail, 
  UpdateStockTransferDetail,
  CompleteStockTransfer
} from '../models/stock-transfer.model';

@Injectable({
  providedIn: 'root'
})
export class StockTransferService {
  private apiUrl = `${environment.apiUrl}/stock-transfers`;

  constructor(private http: HttpClient) { }

  // Stock Transfer Header methods
  getAllStockTransfers(): Observable<StockTransferHeader[]> {
    return this.http.get<StockTransferHeader[]>(this.apiUrl);
  }

  getStockTransferById(id: number): Observable<StockTransferHeader> {
    return this.http.get<StockTransferHeader>(`${this.apiUrl}/${id}`);
  }

  getStockTransfersByFromCostCenterId(costCenterId: number): Observable<StockTransferHeader[]> {
    return this.http.get<StockTransferHeader[]>(`${this.apiUrl}/from-cost-center/${costCenterId}`);
  }

  getStockTransfersByToCostCenterId(costCenterId: number): Observable<StockTransferHeader[]> {
    return this.http.get<StockTransferHeader[]>(`${this.apiUrl}/to-cost-center/${costCenterId}`);
  }

  getStockTransfersByStatus(status: string): Observable<StockTransferHeader[]> {
    return this.http.get<StockTransferHeader[]>(`${this.apiUrl}/status/${status}`);
  }

  createStockTransfer(stockTransfer: CreateStockTransferHeader): Observable<StockTransferHeader> {
    return this.http.post<StockTransferHeader>(this.apiUrl, stockTransfer);
  }

  updateStockTransfer(id: number, stockTransfer: UpdateStockTransferHeader): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, stockTransfer);
  }

  deleteStockTransfer(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  completeStockTransfer(id: number, completeData: CompleteStockTransfer): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/complete`, completeData);
  }

  cancelStockTransfer(id: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/cancel`, {});
  }

  // Stock Transfer Detail methods
  getStockTransferDetails(stockTransferHeaderId: number): Observable<StockTransferDetail[]> {
    return this.http.get<StockTransferDetail[]>(`${this.apiUrl}/${stockTransferHeaderId}/details`);
  }

  getStockTransferDetailById(id: number): Observable<StockTransferDetail> {
    return this.http.get<StockTransferDetail>(`${this.apiUrl}/details/${id}`);
  }

  createStockTransferDetail(stockTransferHeaderId: number, detail: CreateStockTransferDetail): Observable<StockTransferDetail> {
    return this.http.post<StockTransferDetail>(`${this.apiUrl}/${stockTransferHeaderId}/details`, detail);
  }

  updateStockTransferDetail(id: number, detail: UpdateStockTransferDetail): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/details/${id}`, detail);
  }

  deleteStockTransferDetail(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/details/${id}`);
  }
}
