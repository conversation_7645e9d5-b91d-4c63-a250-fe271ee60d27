import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Observable, map, startWith, finalize, forkJoin, of, catchError } from 'rxjs';
import { RecipeService } from '../../../core/services/recipe.service';
import { ProductService } from '../../../core/services/product.service';
import { UnitService } from '../../../core/services/unit.service';
import { Unit } from '../../../core/models/unit.model';
import { CreateRecipe, CreateRecipeIngredient } from '../../../core/models/recipe.model';
import { Product } from '../../../core/models/product.model';

@Component({
  selector: 'app-recipe-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    RouterModule
  ],
  templateUrl: './recipe-detail.component.html',
  styleUrls: ['./recipe-detail.component.scss']
})
export class RecipeDetailComponent implements OnInit {
  recipeForm!: FormGroup;
  isEditMode: boolean = false;
  recipeId: number | null = null;
  isLoading: boolean = false;

  displayedColumns: string[] = [
    'ingredient',
    'quantity',
    'unit',
    'wastage',
    'unitCost',
    'totalCost',
    'actions'
  ];

  products: Product[] = [];
  units: Unit[] = [];

  filteredProducts: Observable<Product[]>[] = [];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private recipeService: RecipeService,
    private productService: ProductService,
    private unitService: UnitService
  ) {}

  ngOnInit(): void {
    this.isLoading = true;

    // Load reference data
    forkJoin({
      products: this.productService.getAll().pipe(catchError(error => {
        console.error('Error loading products', error);
        return of([]);
      })),
      units: this.unitService.getAll().pipe(catchError(error => {
        console.error('Error loading units', error);
        return of([]);
      }))
    })
    .pipe(finalize(() => {
      this.initForm();

      // Check if we're in edit mode
      const idParam = this.route.snapshot.paramMap.get('id');
      if (idParam) {
        const id = parseInt(idParam, 10);
        if (!isNaN(id)) {
          this.recipeId = id;
          this.isEditMode = true;
          this.loadRecipeData(id);
        }
      }

      this.isLoading = false;
    }))
    .subscribe({
      next: (data) => {
        this.products = data.products;
        this.units = data.units;
      },
      error: (error) => {
        console.error('Error loading reference data', error);
        this.snackBar.open('Error loading reference data', 'Close', {
          duration: 3000
        });
      }
    });
  }

  initForm(): void {
    this.recipeForm = this.fb.group({
      productId: ['', Validators.required],
      name: ['', Validators.required],
      yield: [1, [Validators.required, Validators.min(0.01)]],
      unitId: ['', Validators.required],
      instructions: [''],
      notes: [''],
      isSubRecipe: [false],
      isActive: [true],
      ingredients: this.fb.array([])
    });

    // Add an empty ingredient row by default
    this.addIngredient();
  }

  get ingredients_(): FormArray {
    return this.recipeForm.get('ingredients') as FormArray;
  }

  addIngredient(): void {
    const ingredientForm = this.fb.group({
      productId: ['', Validators.required],
      quantity: [1, [Validators.required, Validators.min(0.01)]],
      unitId: ['', Validators.required],
      notes: [''],
      sequence: [this.ingredients_.length + 1],
      wastagePercentage: [0],
      unitCost: [{ value: 0, disabled: true }],
      totalCost: [{ value: 0, disabled: true }]
    });

    // Set up product autocomplete filtering
    const index = this.ingredients_.length;
    this.setupProductAutocomplete(ingredientForm, index);

    // Auto-calculate total cost when quantity changes
    ingredientForm.get('quantity')?.valueChanges.subscribe(() => {
      this.calculateIngredientCost(ingredientForm);
    });

    // Auto-calculate total cost when wastage percentage changes
    ingredientForm.get('wastagePercentage')?.valueChanges.subscribe(() => {
      this.calculateIngredientCost(ingredientForm);
    });

    this.ingredients_.push(ingredientForm);
  }

  setupProductAutocomplete(ingredientForm: FormGroup, index: number): void {
    const productControl = ingredientForm.get('productId');

    this.filteredProducts[index] = productControl!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterProducts(value || ''))
    );

    productControl?.valueChanges.subscribe(productId => {
      if (productId) {
        const product = this.products.find(p => p.id === productId);
        if (product && product.costPrice) {
          ingredientForm.patchValue({
            unitCost: product.costPrice
          });
          this.calculateIngredientCost(ingredientForm);
        } else {
          ingredientForm.patchValue({
            unitCost: 0
          });
        }
      }
    });
  }

  private _filterProducts(value: string | number): Product[] {
    if (typeof value === 'number') {
      return this.products.filter(product => product.id === value);
    }

    const filterValue = value.toLowerCase();
    return this.products.filter(product =>
      product.code.toLowerCase().includes(filterValue) ||
      product.name.toLowerCase().includes(filterValue)
    );
  }

  calculateIngredientCost(ingredientForm: FormGroup): void {
    const quantity = ingredientForm.get('quantity')?.value || 0;
    const unitCost = ingredientForm.get('unitCost')?.value || 0;
    const wastagePercentage = ingredientForm.get('wastagePercentage')?.value || 0;

    // Apply wastage if specified
    const wastageMultiplier = 1 + (wastagePercentage / 100);
    const totalCost = quantity * unitCost * wastageMultiplier;

    ingredientForm.get('totalCost')?.setValue(totalCost);
    this.calculateTotalCost();
  }

  calculateTotalCost(): number {
    let total = 0;
    for (const ingredient of this.ingredients_.controls) {
      total += ingredient.get('totalCost')?.value || 0;
    }
    return total;
  }

  calculateCostPerUnit(): number {
    const totalCost = this.calculateTotalCost();
    const yield_ = this.recipeForm.get('yield')?.value || 1;
    return totalCost / yield_;
  }

  removeIngredient(index: number): void {
    this.ingredients_.removeAt(index);
    this.filteredProducts.splice(index, 1);

    // Update sequence numbers
    for (let i = 0; i < this.ingredients_.length; i++) {
      this.ingredients_.at(i).get('sequence')?.setValue(i + 1);
    }

    this.calculateTotalCost();
  }

  displayProductFn(productId: number): string {
    if (!productId) return '';
    const product = this.products.find(p => p.id === productId);
    return product ? `${product.code} - ${product.name}` : '';
  }

  loadRecipeData(id: number): void {
    this.isLoading = true;

    this.recipeService.getById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (recipe) => {
          // Patch the form with recipe data
          this.recipeForm.patchValue({
            productId: recipe.productId,
            name: recipe.name,
            yield: recipe.yield,
            unitId: recipe.unitId,
            instructions: recipe.instructions,
            notes: recipe.notes,
            isSubRecipe: recipe.isSubRecipe,
            isActive: recipe.isActive
          });

          // Clear existing ingredients
          while (this.ingredients_.length !== 0) {
            this.ingredients_.removeAt(0);
          }

          // Add ingredients
          if (recipe.ingredients && recipe.ingredients.length > 0) {
            recipe.ingredients.forEach(ing => {
              const ingredientForm = this.fb.group({
                productId: [ing.productId, Validators.required],
                quantity: [ing.quantity, [Validators.required, Validators.min(0.01)]],
                unitId: [ing.unitId, Validators.required],
                notes: [ing.notes],
                sequence: [ing.sequence],
                wastagePercentage: [ing.wastagePercentage || 0],
                unitCost: [{ value: ing.cost || 0, disabled: true }],
                totalCost: [{ value: ing.totalCost || 0, disabled: true }]
              });

              const index = this.ingredients_.length;
              this.setupProductAutocomplete(ingredientForm, index);

              ingredientForm.get('quantity')?.valueChanges.subscribe(() => {
                this.calculateIngredientCost(ingredientForm);
              });

              ingredientForm.get('wastagePercentage')?.valueChanges.subscribe(() => {
                this.calculateIngredientCost(ingredientForm);
              });

              this.ingredients_.push(ingredientForm);
            });
          }
        },
        error: (error) => {
          console.error('Error loading recipe', error);
          this.snackBar.open('Error loading recipe', 'Close', {
            duration: 3000
          });
        }
      });
  }

  saveRecipe(): void {
    if (this.recipeForm.valid) {
      this.isLoading = true;

      const formValue = this.recipeForm.getRawValue();

      // Prepare ingredients data
      const ingredients: CreateRecipeIngredient[] = formValue.ingredients.map((ing: any) => ({
        productId: ing.productId,
        quantity: ing.quantity,
        unitId: ing.unitId,
        notes: ing.notes,
        sequence: ing.sequence,
        wastagePercentage: ing.wastagePercentage
      }));

      if (this.isEditMode && this.recipeId) {
        // Update existing recipe
        const updateRecipe = {
          id: this.recipeId,
          name: formValue.name,
          productId: formValue.productId,
          yield: formValue.yield,
          unitId: formValue.unitId,
          instructions: formValue.instructions,
          notes: formValue.notes,
          isSubRecipe: formValue.isSubRecipe,
          isActive: formValue.isActive
        };

        this.recipeService.update(this.recipeId, updateRecipe)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe({
            next: () => {
              this.snackBar.open('Recipe updated successfully', 'Close', {
                duration: 3000
              });
              this.router.navigate(['/recipes']);
            },
            error: (error) => {
              console.error('Error updating recipe', error);
              this.snackBar.open('Error updating recipe', 'Close', {
                duration: 3000
              });
            }
          });
      } else {
        // Create new recipe
        const createRecipe: CreateRecipe = {
          name: formValue.name,
          productId: formValue.productId,
          yield: formValue.yield,
          unitId: formValue.unitId,
          instructions: formValue.instructions,
          notes: formValue.notes,
          isSubRecipe: formValue.isSubRecipe,
          isActive: formValue.isActive,
          ingredients: ingredients
        };

        this.recipeService.create(createRecipe)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe({
            next: () => {
              this.snackBar.open('Recipe created successfully', 'Close', {
                duration: 3000
              });
              this.router.navigate(['/recipes']);
            },
            error: (error) => {
              console.error('Error creating recipe', error);
              this.snackBar.open('Error creating recipe', 'Close', {
                duration: 3000
              });
            }
          });
      }
    } else {
      this.markFormGroupTouched(this.recipeForm);
      this.snackBar.open('Please fix the errors in the form', 'Close', {
        duration: 3000
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/recipes']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
