using System;
using System.Collections.Generic;

namespace SCM.Application.DTOs;

// Transaction Header DTOs
public class TransactionHeaderDto
{
    public int Id { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public int TransactionTypeId { get; set; }
    public string TransactionTypeName { get; set; } = string.Empty;
    public int TransactionProcessId { get; set; }
    public string TransactionProcessName { get; set; } = string.Empty;
    public int? StageTypeId { get; set; }
    public string? StageTypeName { get; set; }
    public int SourceCostCenterId { get; set; }
    public string SourceCostCenterName { get; set; } = string.Empty;
    public int? DestinationCostCenterId { get; set; }
    public string? DestinationCostCenterName { get; set; }
    public int? SupplierId { get; set; }
    public string? SupplierName { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public decimal? TotalAmount { get; set; }
    public decimal? TaxAmount { get; set; }
    public decimal? DiscountAmount { get; set; }
    public decimal? NetAmount { get; set; }
    public int? CreatedById { get; set; }
    public string? CreatedByName { get; set; }
    public DateTime CreatedAt { get; set; }
    public int? ApprovedById { get; set; }
    public string? ApprovedByName { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public int? CompletedById { get; set; }
    public string? CompletedByName { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int? RelatedTransactionId { get; set; }
    public string? RelatedTransactionNumber { get; set; }
    public bool IsSkippedStep { get; set; }
    public List<TransactionDetailDto> Details { get; set; } = new();
}

// Transaction Detail DTOs
public class TransactionDetailDto
{
    public int Id { get; set; }
    public int TransactionHeaderId { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal Quantity { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? TaxRate { get; set; }
    public int? TaxId { get; set; }
    public string? TaxName { get; set; }
    public decimal? TaxAmount { get; set; }
    public decimal? DiscountPercent { get; set; }
    public decimal? DiscountAmount { get; set; }
    public decimal? TotalAmount { get; set; }
    public string? Notes { get; set; }
    public int LineNumber { get; set; }
}

// Create Transaction DTOs
public class CreateProductRequestDto
{
    public int SourceCostCenterId { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? Notes { get; set; }
    public string? ReferenceNumber { get; set; }
    public List<CreateTransactionDetailDto> Details { get; set; } = new();
}

public class UpdateProductRequestDto
{
    public int Id { get; set; }
    public int SourceCostCenterId { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? Notes { get; set; }
}

public class CreateProductOrderDto
{
    public int SupplierId { get; set; }
    public int SourceCostCenterId { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? Notes { get; set; }
    public string? ReferenceNumber { get; set; }
    public List<CreateTransactionDetailDto>? Details { get; set; }
    public bool UpdatePrices { get; set; } = false;
}

public class UpdateProductOrderDto
{
    public int Id { get; set; }
    public int SupplierId { get; set; }
    public int SourceCostCenterId { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? Notes { get; set; }
}

public class CreateReceivingDto
{
    public int SupplierId { get; set; }
    public int SourceCostCenterId { get; set; }
    public DateTime TransactionDate { get; set; }
    public string? InvoiceNumber { get; set; }
    public string? DeliveryNoteNumber { get; set; }
    public string? Notes { get; set; }
    public string? ReferenceNumber { get; set; }
    public List<CreateTransactionDetailDto>? Details { get; set; }
}

public class UpdateReceivingDto
{
    public int Id { get; set; }
    public int SupplierId { get; set; }
    public int SourceCostCenterId { get; set; }
    public DateTime TransactionDate { get; set; }
    public string? InvoiceNumber { get; set; }
    public string? DeliveryNoteNumber { get; set; }
    public string? Notes { get; set; }
}

// Transaction Detail DTOs
public class CreateTransactionDetailDto
{
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? UnitPrice { get; set; }
    public int? TaxId { get; set; }
    public decimal? DiscountPercentage { get; set; } // Changed from DiscountPercent to match entity
    public string? Notes { get; set; }
}

public class UpdateTransactionDetailDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? UnitPrice { get; set; }
    public int? TaxId { get; set; }
    public decimal? DiscountPercentage { get; set; } // Changed from DiscountPercent to match entity
    public string? Notes { get; set; }
}

// Direct Receiving DTOs (for privileged users to skip Request and Order steps)
public class CreateDirectReceivingDto
{
    public int SupplierId { get; set; }
    public int SourceCostCenterId { get; set; }
    public DateTime TransactionDate { get; set; }
    public string? InvoiceNumber { get; set; }
    public string? DeliveryNoteNumber { get; set; }
    public string? Notes { get; set; }
    public List<CreateTransactionDetailDto> Details { get; set; } = new();
}

// Credit Note DTOs
public class CreateCreditNoteDto
{
    public int SourceCostCenterId { get; set; }
    public int? SupplierId { get; set; }
    public int? RelatedTransactionId { get; set; }
    public DateTime TransactionDate { get; set; }
    public string? ReferenceNumber { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public List<CreateCreditNoteDetailDto> Details { get; set; } = new();
}

public class CreateCreditNoteDetailDto
{
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? UnitPrice { get; set; }
    public int? TaxId { get; set; }
    public string? Notes { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class UpdateCreditNoteDto
{
    public int Id { get; set; }
    public int SourceCostCenterId { get; set; }
    public int? SupplierId { get; set; }
    public DateTime TransactionDate { get; set; }
    public string? ReferenceNumber { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class UpdateReceivingDetailDto
{
    public int Quantity { get; set; }
    public int? BatchId { get; set; }
    public decimal? UnitPrice { get; set; }
    public string? Notes { get; set; }
}

public class TransactionProcessDto
{
    public int Id { get; set; }
    public string ProcessNumber { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}
