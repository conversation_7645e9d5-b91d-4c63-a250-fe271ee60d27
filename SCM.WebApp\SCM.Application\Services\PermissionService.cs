using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class PermissionService : IPermissionService
{
    private readonly ApplicationDbContext _dbContext;

    public PermissionService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<PermissionDto>> GetAllPermissionsAsync()
    {
        var permissions = await _dbContext.Permissions
            .OrderBy(p => p.Category)
            .ThenBy(p => p.Name)
            .ToListAsync();

        return permissions.Select(p => new PermissionDto
        {
            Id = p.Id,
            Name = p.Name,
            Description = p.Description,
            Category = p.Category
        });
    }

    public async Task<IEnumerable<PermissionCategoryDto>> GetPermissionsByCategoryAsync()
    {
        var permissions = await _dbContext.Permissions
            .OrderBy(p => p.Category)
            .ThenBy(p => p.Name)
            .ToListAsync();

        return permissions
            .GroupBy(p => p.Category)
            .Select(g => new PermissionCategoryDto
            {
                Category = g.Key,
                Permissions = g.Select(p => new PermissionDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    Category = p.Category
                }).ToList()
            });
    }

    public async Task<PermissionDto?> GetPermissionByIdAsync(int id)
    {
        var permission = await _dbContext.Permissions.FindAsync(id);
        if (permission == null)
            return null;

        return new PermissionDto
        {
            Id = permission.Id,
            Name = permission.Name,
            Description = permission.Description,
            Category = permission.Category
        };
    }

    public async Task<PermissionDto?> GetPermissionByNameAsync(string name)
    {
        var permission = await _dbContext.Permissions
            .FirstOrDefaultAsync(p => p.Name == name);

        if (permission == null)
            return null;

        return new PermissionDto
        {
            Id = permission.Id,
            Name = permission.Name,
            Description = permission.Description,
            Category = permission.Category
        };
    }

    public async Task InitializeDefaultPermissionsAsync()
    {
        var defaultPermissions = new[]
        {
            // Products
            new Permission { Name = "View Products", Description = "View product information", Category = "Products" },
            new Permission { Name = "Create Products", Description = "Create new products", Category = "Products" },
            new Permission { Name = "Edit Products", Description = "Edit existing products", Category = "Products" },
            new Permission { Name = "Delete Products", Description = "Delete products", Category = "Products" },

            // Inventory
            new Permission { Name = "View Inventory", Description = "View inventory levels", Category = "Inventory" },
            new Permission { Name = "Adjust Inventory", Description = "Make inventory adjustments", Category = "Inventory" },
            new Permission { Name = "Transfer Inventory", Description = "Transfer inventory between locations", Category = "Inventory" },

            // Purchases
            new Permission { Name = "View Purchases", Description = "View purchase orders", Category = "Purchases" },
            new Permission { Name = "Create Purchase Orders", Description = "Create new purchase orders", Category = "Purchases" },
            new Permission { Name = "Approve Purchase Orders", Description = "Approve purchase orders", Category = "Purchases" },
            new Permission { Name = "Receive Goods", Description = "Receive goods from suppliers", Category = "Purchases" },

            // Sales
            new Permission { Name = "View Sales", Description = "View sales transactions", Category = "Sales" },
            new Permission { Name = "Create Sales", Description = "Create new sales transactions", Category = "Sales" },

            // Reports
            new Permission { Name = "View Reports", Description = "View system reports", Category = "Reports" },
            new Permission { Name = "Export Reports", Description = "Export reports to files", Category = "Reports" },

            // Users
            new Permission { Name = "View Users", Description = "View user accounts", Category = "Users" },
            new Permission { Name = "Create Users", Description = "Create new user accounts", Category = "Users" },
            new Permission { Name = "Edit Users", Description = "Edit user accounts", Category = "Users" },
            new Permission { Name = "Delete Users", Description = "Delete user accounts", Category = "Users" }
        };

        foreach (var permission in defaultPermissions)
        {
            var existingPermission = await _dbContext.Permissions
                .FirstOrDefaultAsync(p => p.Name == permission.Name);

            if (existingPermission == null)
            {
                permission.CreatedAt = DateTime.UtcNow;
                permission.IsActive = true;
                _dbContext.Permissions.Add(permission);
            }
        }

        await _dbContext.SaveChangesAsync();
    }
}
