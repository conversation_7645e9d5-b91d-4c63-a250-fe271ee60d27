using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IStockRequestService
{
    Task<IEnumerable<StockRequestHeaderDto>> GetAllStockRequestsAsync();
    Task<StockRequestHeaderDto?> GetStockRequestByIdAsync(int id);
    Task<IEnumerable<StockRequestHeaderDto>> GetStockRequestsByFromCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<StockRequestHeaderDto>> GetStockRequestsByToCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<StockRequestHeaderDto>> GetStockRequestsByStatusAsync(string status);
    Task<StockRequestHeaderDto> CreateStockRequestAsync(CreateStockRequestHeaderDto createStockRequestHeaderDto);
    Task UpdateStockRequestAsync(UpdateStockRequestHeaderDto updateStockRequestHeaderDto);
    Task DeleteStockRequestAsync(int id);
    Task SubmitStockRequestAsync(SubmitStockRequestDto submitStockRequestDto);
    Task ApproveStockRequestAsync(ApproveStockRequestDto approveStockRequestDto);
    Task RejectStockRequestAsync(RejectStockRequestDto rejectStockRequestDto);
    Task CompleteStockRequestAsync(CompleteStockRequestDto completeStockRequestDto);
    Task CancelStockRequestAsync(int id);
    Task<IEnumerable<StockRequestDetailDto>> GetStockRequestDetailsAsync(int stockRequestHeaderId);
    Task<StockRequestDetailDto?> GetStockRequestDetailByIdAsync(int id);
    Task<StockRequestDetailDto> CreateStockRequestDetailAsync(int stockRequestHeaderId, CreateStockRequestDetailDto createStockRequestDetailDto);
    Task UpdateStockRequestDetailAsync(UpdateStockRequestDetailDto updateStockRequestDetailDto);
    Task DeleteStockRequestDetailAsync(int id);
}
