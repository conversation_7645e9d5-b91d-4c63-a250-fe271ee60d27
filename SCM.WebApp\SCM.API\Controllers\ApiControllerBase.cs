using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace SCM.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public abstract class ApiControllerBase : ControllerBase
{
    /// <summary>
    /// Gets the current authenticated user ID from JWT claims
    /// </summary>
    /// <returns>User ID if authenticated, null otherwise</returns>
    protected int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (int.TryParse(userIdClaim, out int userId))
        {
            return userId;
        }
        return null;
    }

    /// <summary>
    /// Gets the current authenticated user ID from JWT claims, throws exception if not found
    /// </summary>
    /// <returns>User ID</returns>
    /// <exception cref="UnauthorizedAccessException">Thrown when user is not authenticated</exception>
    protected int GetCurrentUserIdRequired()
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            throw new UnauthorizedAccessException("User is not authenticated or user ID not found in token");
        }
        return userId.Value;
    }

    /// <summary>
    /// Gets the current user's role from JWT claims
    /// </summary>
    /// <returns>Role name if found, null otherwise</returns>
    protected string? GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value;
    }

    /// <summary>
    /// Gets the current user's username from JWT claims
    /// </summary>
    /// <returns>Username if found, null otherwise</returns>
    protected string? GetCurrentUsername()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value;
    }

    /// <summary>
    /// Test endpoint to check authentication and claims
    /// </summary>
    [HttpGet("test-auth")]
    public ActionResult TestAuth()
    {
        var claims = User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList();
        var userId = GetCurrentUserId();
        var username = GetCurrentUsername();
        var role = GetCurrentUserRole();

        return Ok(new
        {
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false,
            UserId = userId,
            Username = username,
            Role = role,
            Claims = claims
        });
    }
}
