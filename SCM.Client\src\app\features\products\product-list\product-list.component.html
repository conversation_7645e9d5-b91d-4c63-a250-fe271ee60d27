<div class="page-container">
  <div class="page-header">
    <h1>Products</h1>
    <div class="header-actions">
      <button mat-stroked-button color="accent" (click)="openImportDialog()">
        <mat-icon>upload</mat-icon> Import Products
      </button>
      <button mat-raised-button color="primary" (click)="addProduct()">
        <mat-icon>add</mat-icon> Add Product
      </button>
    </div>
  </div>

  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name or ID">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Category</mat-label>
          <mat-select [(ngModel)]="selectedCategory" (selectionChange)="applyFilter()">
            <mat-option value="">All Categories</mat-option>
            <mat-option *ngFor="let category of categories" [value]="category">
              {{category}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2">
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading products...</p>
    </div>

    <div *ngIf="!isLoading && filteredProducts.length === 0" class="empty-state">
      <mat-icon>inventory_2</mat-icon>
      <p>No products found</p>
      <button mat-raised-button color="primary" (click)="addProduct()">Add Product</button>
    </div>

    <div *ngIf="!isLoading && filteredProducts.length > 0">
      <table mat-table [dataSource]="filteredProducts" class="product-table">
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>ID</th>
          <td mat-cell *matCellDef="let product">{{product.id}}</td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Name</th>
          <td mat-cell *matCellDef="let product">{{product.name}}</td>
        </ng-container>

        <!-- Category Column -->
        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef>Category</th>
          <td mat-cell *matCellDef="let product">{{product.category}}</td>
        </ng-container>

        <!-- Unit Size Column -->
        <ng-container matColumnDef="unitSize">
          <th mat-header-cell *matHeaderCellDef>Unit Size</th>
          <td mat-cell *matCellDef="let product">{{product.unitSize}}</td>
        </ng-container>

        <!-- Unit Price Column -->
        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef>Unit Price</th>
          <td mat-cell *matCellDef="let product">{{product.unitPrice | currency}}</td>
        </ng-container>

        <!-- Average Cost Column -->
        <ng-container matColumnDef="averageCost">
          <th mat-header-cell *matHeaderCellDef>Average Cost</th>
          <td mat-cell *matCellDef="let product">{{product.averageCost | currency}}</td>
        </ng-container>

        <!-- Stock On Hand Column -->
        <ng-container matColumnDef="stockOnHand">
          <th mat-header-cell *matHeaderCellDef>Stock On Hand</th>
          <td mat-cell *matCellDef="let product" [ngClass]="{'low-stock': product.stockOnHand < product.reorderLevel}">
            {{product.stockOnHand}}
          </td>
        </ng-container>

        <!-- Base Quantity Column -->
        <ng-container matColumnDef="baseQuantity">
          <th mat-header-cell *matHeaderCellDef>Base Quantity</th>
          <td mat-cell *matCellDef="let product">{{product.baseQuantity}}</td>
        </ng-container>

        <!-- Reorder Level Column -->
        <ng-container matColumnDef="reorderLevel">
          <th mat-header-cell *matHeaderCellDef>Reorder Level</th>
          <td mat-cell *matCellDef="let product">{{product.reorderLevel}}</td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let product" [ngClass]="{'status-active': product.status === 'Active', 'status-low': product.status === 'Low Stock'}">
            {{product.status}}
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let product">
            <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">
              <button mat-menu-item (click)="viewProductDetails(product)">
                <mat-icon>visibility</mat-icon>
                <span>View Details</span>
              </button>
              <button mat-menu-item (click)="editProduct(product)">
                <mat-icon>edit</mat-icon>
                <span>Edit</span>
              </button>
              <button mat-menu-item (click)="deleteProduct(product)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
    </div>
  </div>
</div>
