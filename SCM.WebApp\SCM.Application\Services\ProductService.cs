using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class ProductService : IProductService
{
    private readonly IRepository<Product> _productRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public ProductService(
        IRepository<Product> productRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _productRepository = productRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<ProductDto>> GetAllProductsAsync()
    {
        var products = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .ToListAsync();

        var productDtos = _mapper.Map<IEnumerable<ProductDto>>(products).ToList();

        // Add stock information for each product
        foreach (var productDto in productDtos)
        {
            try
            {
                // Get total stock quantity for this product across all cost centers
                var totalStock = await _dbContext.StockOnHand
                    .Where(s => s.ProductId == productDto.Id)
                    .SumAsync(s => s.Quantity);

                productDto.StockOnHand = totalStock;

                // Calculate base quantity (stock quantity * unit conversion factor)
                // For now, assuming 1:1 conversion, but this can be enhanced later
                productDto.BaseQuantity = totalStock;
            }
            catch (Exception ex)
            {
                // If there's an error getting stock, default to 0
                productDto.StockOnHand = 0;
                productDto.BaseQuantity = 0;
            }
        }

        return productDtos;
    }

    public async Task<ProductDto?> GetProductByIdAsync(int id)
    {
        var product = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .Include(p => p.Barcodes)
            .FirstOrDefaultAsync(p => p.Id == id);
            
        return product != null ? _mapper.Map<ProductDto>(product) : null;
    }

    public async Task<ProductDto?> GetProductByCodeAsync(string code)
    {
        var product = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .FirstOrDefaultAsync(p => p.Code == code);
            
        return product != null ? _mapper.Map<ProductDto>(product) : null;
    }

    public async Task<IEnumerable<ProductDto>> GetProductsByDepartmentIdAsync(int departmentId)
    {
        var products = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .Where(p => p.DepartmentId == departmentId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductDto>>(products);
    }

    public async Task<IEnumerable<ProductDto>> GetProductsByGroupIdAsync(int groupId)
    {
        var products = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .Where(p => p.GroupId == groupId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductDto>>(products);
    }

    public async Task<IEnumerable<ProductDto>> GetProductsBySubGroupIdAsync(int subGroupId)
    {
        var products = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .Where(p => p.SubGroupId == subGroupId)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductDto>>(products);
    }

    public async Task<IEnumerable<ProductDto>> SearchProductsAsync(string searchTerm)
    {
        var products = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .Where(p => p.Name.Contains(searchTerm) || p.Code.Contains(searchTerm))
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<ProductDto>>(products);
    }

    public async Task<ProductDto> CreateProductAsync(CreateProductDto createProductDto)
    {
        var product = _mapper.Map<Product>(createProductDto);
        await _productRepository.AddAsync(product);
        
        // Reload the product with all related entities
        var createdProduct = await _dbContext.Products
            .Include(p => p.Brand)
            .Include(p => p.Unit)
            .Include(p => p.Department)
            .Include(p => p.Group)
            .Include(p => p.SubGroup)
            .Include(p => p.Tax)
            .Include(p => p.SalesUnit)
            .FirstOrDefaultAsync(p => p.Id == product.Id);
            
        return _mapper.Map<ProductDto>(createdProduct);
    }

    public async Task UpdateProductAsync(UpdateProductDto updateProductDto)
    {
        var product = await _productRepository.GetByIdAsync(updateProductDto.Id);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {updateProductDto.Id} not found.");

        _mapper.Map(updateProductDto, product);
        await _productRepository.UpdateAsync(product);
    }

    public async Task DeleteProductAsync(int id)
    {
        var product = await _productRepository.GetByIdAsync(id);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {id} not found.");

        // Check if there are any stock on hand for this product
        var hasStock = await _dbContext.StockOnHand.AnyAsync(s => s.ProductId == id);
        if (hasStock)
            throw new InvalidOperationException($"Cannot delete Product with ID {id} because it has stock on hand.");

        // Check if there are any transaction details for this product
        var hasTransactions = await _dbContext.TransactionDetails.AnyAsync(td => td.ProductId == id);
        if (hasTransactions)
            throw new InvalidOperationException($"Cannot delete Product with ID {id} because it is used in transactions.");

        // Check if there are any recipe ingredients for this product
        var hasRecipeIngredients = await _dbContext.RecipeIngredients.AnyAsync(ri => ri.ProductId == id);
        if (hasRecipeIngredients)
            throw new InvalidOperationException($"Cannot delete Product with ID {id} because it is used in recipes.");

        await _productRepository.DeleteAsync(product);
    }
}
