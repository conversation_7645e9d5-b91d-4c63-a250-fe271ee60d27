import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { ApiService } from './api.service';
import { AuthService } from './auth.service';

export interface Permission {
  id: number;
  name: string;
  description?: string;
}

export interface RolePermission {
  roleId: number;
  roleName: string;
  permissions: Permission[];
}

export interface UserPermission {
  userId: number;
  username: string;
  permissions: Permission[];
}

export interface FormPermission {
  formName: string;
  actions: {
    name: string;
    allowed: boolean;
  }[];
}

export interface UserPermissions {
  userId: number;
  canViewTransactions: boolean;
  canCreateTransactions: boolean;
  canEditTransactions: boolean;
  canDeleteTransactions: boolean;
  canApproveTransactions: boolean;
  hasFullReceivePermission: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class PermissionsService {
  private readonly path = 'permissions';
  private readonly userPermissionsPath = 'user-permissions';
  private cachedUserPermissions: { [userId: number]: Permission[] } = {};
  private cachedRolePermissions: { [roleId: number]: Permission[] } = {};
  private cachedFormPermissions: { [formName: string]: FormPermission } = {};
  private currentUserPermissions: UserPermissions | null = null;

  constructor(
    private apiService: ApiService,
    private authService: AuthService
  ) {
    // Subscribe to logout events to clear cache
    this.authService.logout$.subscribe(() => {
      this.clearCache();
    });
  }

  // Get all permissions
  getAllPermissions(): Observable<Permission[]> {
    return this.apiService.get<Permission[]>(this.path);
  }

  // Get permissions for a specific role
  getRolePermissions(roleId: number): Observable<Permission[]> {
    if (this.cachedRolePermissions[roleId]) {
      return of(this.cachedRolePermissions[roleId]);
    }
    return this.apiService.get<Permission[]>(`${this.path}/roles/${roleId}`);
  }

  // Get permissions for a specific user
  getUserPermissions(userId: number): Observable<Permission[]> {
    if (this.cachedUserPermissions[userId]) {
      return of(this.cachedUserPermissions[userId]);
    }
    return this.apiService.get<Permission[]>(`${this.path}/users/${userId}`);
  }

  // Get form permissions for the current user
  getFormPermissions(formName: string): Observable<FormPermission> {
    if (this.cachedFormPermissions[formName]) {
      return of(this.cachedFormPermissions[formName]);
    }

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      return of({
        formName,
        actions: []
      });
    }

    return this.apiService.get<FormPermission>(`${this.path}/forms/${formName}/users/${currentUser.id}`);
  }

  // Get current user permissions from backend
  getCurrentUserPermissions(): Observable<UserPermissions> {
    if (this.currentUserPermissions) {
      return of(this.currentUserPermissions);
    }

    return this.apiService.get<UserPermissions>(`${this.userPermissionsPath}/current`).pipe(
      tap(permissions => this.currentUserPermissions = permissions)
    );
  }

  // Check a specific permission via backend
  checkPermission(permission: string): Observable<boolean> {
    return this.apiService.get<boolean>(`${this.userPermissionsPath}/check/${permission}`);
  }

  // Clear cached permissions (call when user logs out or permissions change)
  clearCache(): void {
    this.currentUserPermissions = null;
    this.cachedUserPermissions = {};
    this.cachedRolePermissions = {};
    this.cachedFormPermissions = {};
  }

  // Check if the current user has a specific permission (synchronous)
  hasPermission(permissionName: string): boolean {
    // Admin users have all permissions
    if (this.authService.hasRole('Admin')) {
      return true;
    }

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      return false;
    }

    // Check cached permissions first
    if (this.currentUserPermissions) {
      switch (permissionName) {
        case 'transactions.view':
          return this.currentUserPermissions.canViewTransactions;
        case 'transactions.create':
          return this.currentUserPermissions.canCreateTransactions;
        case 'transactions.edit':
          return this.currentUserPermissions.canEditTransactions;
        case 'transactions.delete':
          return this.currentUserPermissions.canDeleteTransactions;
        case 'transactions.approve':
          return this.currentUserPermissions.canApproveTransactions;
        case 'receiving.full':
          return this.currentUserPermissions.hasFullReceivePermission;
      }
    }

    // Fallback to role-based checks for basic permissions
    if (permissionName.startsWith('product.')) {
      return true; // All users can access product features
    }

    if (permissionName.startsWith('inventory.')) {
      return true; // All users can access inventory features
    }

    if (permissionName.startsWith('transaction.')) {
      return true; // All users can access transaction features (basic access)
    }

    if (permissionName.startsWith('user.') && !this.authService.hasRole('Admin')) {
      return false; // Only admins can access user management
    }

    if (permissionName.startsWith('config.')) {
      return true; // For now, allow all users to access configuration features
    }

    return false;
  }

  // Async version that fetches from backend if needed
  hasPermissionAsync(permissionName: string): Observable<boolean> {
    // Admin users have all permissions
    if (this.authService.hasRole('Admin')) {
      return of(true);
    }

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      return of(false);
    }

    // If we have cached permissions, use them
    if (this.currentUserPermissions) {
      return of(this.hasPermission(permissionName));
    }

    // Otherwise, fetch from backend
    return this.getCurrentUserPermissions().pipe(
      tap(() => {}), // Trigger caching
      map(() => this.hasPermission(permissionName))
    );
  }

  // Check if the current user can perform an action on a form
  canPerformAction(formName: string, actionName: string): boolean {
    // Admin users can perform all actions
    if (this.authService.hasRole('Admin')) {
      return true;
    }
    
    const formPermission = this.cachedFormPermissions[formName];
    if (!formPermission) {
      return false;
    }
    
    const action = formPermission.actions.find(a => a.name === actionName);
    return action ? action.allowed : false;
  }
}
