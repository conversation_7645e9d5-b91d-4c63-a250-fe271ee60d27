import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { ProductRequestListItem } from '../../../../core/models/product-request.model';

interface DialogData {
  productRequests: ProductRequestListItem[];
}

@Component({
  selector: 'app-select-product-request-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatTableModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    FormsModule
  ],
  templateUrl: './select-product-request-dialog.component.html',
  styleUrls: ['./select-product-request-dialog.component.scss']
})
export class SelectProductRequestDialogComponent implements OnInit {
  displayedColumns: string[] = ['referenceNumber', 'costCenterName', 'requestDate', 'actions'];
  filteredRequests: ProductRequestListItem[] = [];
  searchTerm = '';

  constructor(
    public dialogRef: MatDialogRef<SelectProductRequestDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {}

  ngOnInit(): void {
    // Make sure all requests have a reference number and cost center name
    this.filteredRequests = this.data.productRequests.map(request => {
      // Ensure reference number is set
      if (!request.referenceNumber && request.id) {
        request.referenceNumber = `PR-${request.id.toString().padStart(5, '0')}`;
      }

      // Log the data for debugging
      console.log('Product request:', request);

      return request;
    });
  }

  onSearch(event: Event): void {
    this.searchTerm = (event.target as HTMLInputElement).value;
    this.applyFilter();
  }

  applyFilter(): void {
    if (!this.searchTerm) {
      // Make sure all requests have a reference number and cost center name
      this.filteredRequests = this.data.productRequests.map(request => {
        // Ensure reference number is set
        if (!request.referenceNumber && request.id) {
          request.referenceNumber = `PR-${request.id.toString().padStart(5, '0')}`;
        }
        return request;
      });
      return;
    }

    const searchLower = this.searchTerm.toLowerCase();
    this.filteredRequests = this.data.productRequests.filter(request => {
      const refNumber = request.referenceNumber || `PR-${request.id.toString().padStart(5, '0')}`;
      const costCenter = request.costCenterName || '';

      return refNumber.toLowerCase().includes(searchLower) ||
             costCenter.toLowerCase().includes(searchLower);
    });
  }

  selectRequest(request: ProductRequestListItem): void {
    this.dialogRef.close(request);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
