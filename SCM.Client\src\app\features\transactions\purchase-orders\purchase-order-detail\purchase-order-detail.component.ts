import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { PurchaseOrderService } from '../../../../core/services/purchase-order.service';
import { SupplierService, Supplier } from '../../../../core/services/supplier.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductService } from '../../../../core/services/product.service';
import { ProductRequestService } from '../../../../core/services/product-request.service';
import { ErrorService } from '../../../../core/services/error.service';
import {
  PurchaseOrder,
  PurchaseOrderDetail,
  CreatePurchaseOrder,
  CreatePurchaseOrderDetail
} from '../../../../core/models/purchase-order.model';
import { TransactionHeader, TransactionDetail } from '../../../../core/models/transaction.model';
import { TransactionAdapter } from '../../../../core/adapters/transaction-adapter';
import { Product } from '../../../../core/models/product.model';
import { ProductRequestHeader } from '../../../../core/models/product-request.model';
import { forkJoin, finalize, catchError, of } from 'rxjs';

@Component({
  selector: 'app-purchase-order-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    MatDialogModule
  ],
  templateUrl: './purchase-order-detail.component.html',
  styleUrls: ['./purchase-order-detail.component.scss']
})
export class PurchaseOrderDetailComponent implements OnInit {
  purchaseOrderForm!: FormGroup;
  purchaseOrder: PurchaseOrder | null = null;
  suppliers: Supplier[] = [];
  costCenters: any[] = [];
  products: Product[] = [];
  productRequests: any[] = [];

  isLoading = false;
  isEditMode = false;
  isViewMode = false;
  purchaseOrderId: number | null = null;
  productRequestId: number | null = null;

  displayedColumns: string[] = [
    'productName',
    'quantity',
    'unitPrice',
    'totalPrice',
    'actions'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private purchaseOrderService: PurchaseOrderService,
    private supplierService: SupplierService,
    private costCenterService: CostCenterService,
    private productService: ProductService,
    private productRequestService: ProductRequestService,
    private errorService: ErrorService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadReferenceData();

    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      const mode = params.get('mode');

      if (id) {
        this.purchaseOrderId = +id;
        this.isViewMode = !mode || mode !== 'edit';
        this.isEditMode = mode === 'edit';
        this.loadPurchaseOrder(this.purchaseOrderId);
      }
    });

    // Check for query parameters (product request ID)
    this.route.queryParamMap.subscribe(params => {
      const requestId = params.get('requestId');
      if (requestId) {
        this.productRequestId = +requestId;
        this.loadFromProductRequest(this.productRequestId);
      }
    });

    // Log the view mode for debugging
    console.log('View mode:', this.isViewMode);
    console.log('Edit mode:', this.isEditMode);
  }

  initForm(): void {
    this.purchaseOrderForm = this.fb.group({
      supplierId: ['', Validators.required],
      costCenterId: ['', Validators.required],
      orderDate: [new Date(), Validators.required],
      expectedDeliveryDate: [null],
      notes: [''],
      details: this.fb.array([])
    });

    console.log('Form initialized, view mode:', this.isViewMode);

    // Disable form if in view mode
    if (this.isViewMode) {
      this.purchaseOrderForm.disable();
    }
  }

  loadReferenceData(): void {
    this.isLoading = true;

    forkJoin({
      suppliers: this.supplierService.getAll().pipe(
        catchError(error => {
          console.error('Error loading suppliers', error);
          return of([]);
        })
      ),
      costCenters: this.costCenterService.getAllCostCenters().pipe(
        catchError(error => {
          console.error('Error loading cost centers', error);
          return of([]);
        })
      ),
      products: this.productService.getAll().pipe(
        catchError(error => {
          console.error('Error loading products', error);
          return of([]);
        })
      ),
      productRequests: this.productRequestService.getByStatus('Approved').pipe(
        catchError(error => {
          console.error('Error loading product requests', error);
          return of([]);
        })
      )
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe(result => {
      this.suppliers = result.suppliers;
      this.costCenters = result.costCenters;
      this.products = result.products;
      this.productRequests = result.productRequests;
    });
  }

  loadFromProductRequest(requestId: number): void {
    this.isLoading = true;

    this.productRequestService.getById(requestId)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (request) => {
          // Set cost center from the product request
          this.purchaseOrderForm.patchValue({
            costCenterId: request.costCenterId,
            orderDate: new Date(),
            expectedDeliveryDate: new Date(new Date().setDate(new Date().getDate() + 7)) // Default to 7 days from now
          });

          // Clear existing details
          while (this.details.length) {
            this.details.removeAt(0);
          }

          // Add details from product request
          if (request.details && request.details.length > 0) {
            // Create a map to consolidate duplicate products
            const productMap = new Map<number, { quantity: number, price: number, notes: string }>();

            // Group products by productId
            request.details.forEach(detail => {
              if (productMap.has(detail.productId)) {
                // Product already exists, update quantity
                const existing = productMap.get(detail.productId)!;
                existing.quantity += detail.quantity;
                // Keep the latest price and notes
                existing.price = detail.price || existing.price;
                existing.notes = detail.notes || existing.notes;
              } else {
                // New product
                productMap.set(detail.productId, {
                  quantity: detail.quantity,
                  price: detail.price || 0,
                  notes: detail.notes || ''
                });
              }
            });

            // Add consolidated products to the form
            productMap.forEach((value, productId) => {
              const detailForm = this.fb.group({
                productId: [productId, Validators.required],
                quantity: [value.quantity, [Validators.required, Validators.min(1)]],
                unitPrice: [value.price, [Validators.required, Validators.min(0)]],
                notes: [value.notes]
              });

              this.details.push(detailForm);
            });
          }

          this.snackBar.open('Product request loaded successfully', 'Close', { duration: 3000 });
        },
        error: (error) => {
          this.errorService.handleError(error);
        }
      });
  }

  loadPurchaseOrder(id: number): void {
    this.isLoading = true;
    console.log('Loading purchase order with ID:', id);

    this.purchaseOrderService.getById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (transaction: TransactionHeader) => {
          console.log('Received transaction data:', transaction);

          // Convert TransactionHeader to PurchaseOrder
          const purchaseOrder = TransactionAdapter.toPurchaseOrder(transaction);
          console.log('Converted to purchase order:', purchaseOrder);

          this.purchaseOrder = purchaseOrder;

          // Only patch the form if we're in edit mode
          if (this.isEditMode) {
            this.patchFormWithPurchaseOrder(purchaseOrder);
          }
        },
        error: (error) => {
          console.error('Error loading purchase order:', error);
          this.errorService.handleError(error);
          this.router.navigate(['/transactions/purchase-orders']);
        }
      });
  }

  patchFormWithPurchaseOrder(order: PurchaseOrder): void {
    this.purchaseOrderForm.patchValue({
      supplierId: order.supplierId,
      costCenterId: order.costCenterId,
      orderDate: new Date(order.orderDate),
      expectedDeliveryDate: order.expectedDeliveryDate ? new Date(order.expectedDeliveryDate) : null,
      notes: order.notes || ''
    });

    // Clear existing details
    while (this.details.length) {
      this.details.removeAt(0);
    }

    // Add details
    if (order.details && order.details.length > 0) {
      // Create a map to consolidate duplicate products
      const productMap = new Map<number, PurchaseOrderDetail>();

      // Group products by productId
      order.details.forEach(detail => {
        if (productMap.has(detail.productId)) {
          // Product already exists, update quantity
          const existing = productMap.get(detail.productId)!;
          existing.quantity += detail.quantity;
          // Keep the latest price and notes
          existing.unitPrice = detail.unitPrice || existing.unitPrice;
          existing.notes = detail.notes || existing.notes;
        } else {
          // New product
          productMap.set(detail.productId, {...detail});
        }
      });

      // Add consolidated products to the form
      productMap.forEach(detail => {
        this.addDetail(detail);
      });
    }

    // Disable form if in view mode
    if (this.isViewMode) {
      this.purchaseOrderForm.disable();
    }
  }

  get details(): FormArray {
    return this.purchaseOrderForm.get('details') as FormArray;
  }

  addDetail(detail?: PurchaseOrderDetail): void {
    const productId = detail ? detail.productId : '';

    // Check if the product already exists in the details
    if (productId) {
      const existingDetailIndex = this.findDetailIndexByProductId(productId);

      if (existingDetailIndex !== -1) {
        // Product already exists, update the quantity instead of adding a duplicate
        const existingDetail = this.details.at(existingDetailIndex);
        const currentQuantity = existingDetail.get('quantity')?.value || 0;
        const additionalQuantity = detail ? detail.quantity : 1;

        existingDetail.get('quantity')?.setValue(currentQuantity + additionalQuantity);

        // Show notification
        this.snackBar.open('Product already exists in the order. Quantity has been updated.', 'Close', { duration: 3000 });
        return;
      }
    }

    // If product doesn't exist or no product selected yet, add a new detail
    const detailForm = this.fb.group({
      productId: [productId, Validators.required],
      quantity: [detail ? detail.quantity : 1, [Validators.required, Validators.min(1)]],
      unitPrice: [detail ? detail.unitPrice : 0, [Validators.required, Validators.min(0)]],
      notes: [detail ? detail.notes : '']
    });

    // Add value change listeners to recalculate totals
    detailForm.get('quantity')?.valueChanges.subscribe(() => {
      // Recalculate the total amount when quantity changes
      console.log('Quantity changed, new total:', this.calculateOrderTotal());
    });

    detailForm.get('unitPrice')?.valueChanges.subscribe(() => {
      // Recalculate the total amount when unit price changes
      console.log('Unit price changed, new total:', this.calculateOrderTotal());
    });

    this.details.push(detailForm);

    // Disable the detail form if in view mode
    if (this.isViewMode) {
      detailForm.disable();
    }
  }

  // Helper method to find a detail by product ID
  findDetailIndexByProductId(productId: number): number {
    for (let i = 0; i < this.details.length; i++) {
      const detail = this.details.at(i);
      if (detail.get('productId')?.value === productId) {
        return i;
      }
    }
    return -1;
  }

  // Handle product selection change
  onProductSelectionChange(event: any, index: number): void {
    const selectedProductId = event.value;

    // Skip if no product is selected
    if (!selectedProductId) return;

    // Check if this product already exists in another row
    for (let i = 0; i < this.details.length; i++) {
      // Skip the current row
      if (i === index) continue;

      const detail = this.details.at(i);
      const productId = detail.get('productId')?.value;

      if (productId === selectedProductId) {
        // Found duplicate product
        // Reset the selection
        this.details.at(index).get('productId')?.setValue('');

        // Show error message
        this.snackBar.open(
          'This product is already in the order. Please update the quantity in the existing row instead of adding a duplicate.',
          'Close',
          { duration: 5000 }
        );
        break;
      }
    }
  }

  removeDetail(index: number): void {
    this.details.removeAt(index);
  }

  calculateTotalPrice(index: number): number {
    const detail = this.details.at(index).value;
    const quantity = detail.quantity || 0;
    const unitPrice = detail.unitPrice || 0;
    return quantity * unitPrice;
  }

  calculateOrderTotal(): number {
    return this.details.controls.reduce((total, control) => {
      const detail = control.value;
      const quantity = detail.quantity || 0;
      const unitPrice = detail.unitPrice || 0;
      return total + (quantity * unitPrice);
    }, 0);
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.name : '';
  }

  getSupplierName(supplierId: number): string {
    const supplier = this.suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : '';
  }

  getCostCenterName(costCenterId: number): string {
    const costCenter = this.costCenters.find(c => c.id === costCenterId);
    return costCenter ? costCenter.name : '';
  }

  savePurchaseOrder(): void {
    if (this.purchaseOrderForm.invalid) {
      this.markFormGroupTouched(this.purchaseOrderForm);
      this.errorService.showError('Please fix the errors in the form before saving.');
      return;
    }

    if (this.details.length === 0) {
      this.errorService.showError('Please add at least one product to the purchase order.');
      return;
    }

    // Check for duplicate products
    const productIds = new Set<number>();
    let hasDuplicates = false;

    for (let i = 0; i < this.details.length; i++) {
      const productId = this.details.at(i).get('productId')?.value;
      if (productIds.has(productId)) {
        hasDuplicates = true;
        break;
      }
      productIds.add(productId);
    }

    if (hasDuplicates) {
      this.errorService.showError('There are duplicate products in the order. Please consolidate them before saving.');
      return;
    }

    // Log the total amount for debugging
    const totalAmount = this.calculateOrderTotal();
    console.log('Total amount before saving:', totalAmount);

    this.isLoading = true;

    const formValue = this.purchaseOrderForm.value;

    if (this.isEditMode && this.purchaseOrderId) {
      // Update existing purchase order
      const updatePurchaseOrder = {
        id: this.purchaseOrderId,
        ...formValue
      };

      this.purchaseOrderService.update(this.purchaseOrderId, updatePurchaseOrder)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.errorService.showSuccess('Purchase order updated successfully');
            // Force a refresh of the purchase order list
            this.router.navigateByUrl('/transactions', { skipLocationChange: true }).then(() => {
              this.router.navigate(['/transactions/purchase-orders']);
            });
          },
          error: (error) => this.errorService.handleError(error)
        });
    } else {
      // Create new purchase order
      const createPurchaseOrder: CreatePurchaseOrder = {
        supplierId: formValue.supplierId,
        costCenterId: formValue.costCenterId,
        orderDate: formValue.orderDate,
        expectedDeliveryDate: formValue.expectedDeliveryDate,
        notes: formValue.notes,
        details: formValue.details.map((detail: any) => ({
          productId: detail.productId,
          quantity: detail.quantity,
          unitPrice: detail.unitPrice,
          notes: detail.notes
        }))
      };

      // Log the total amount for debugging
      console.log('Total amount calculated before sending to server:', this.calculateOrderTotal());

      this.purchaseOrderService.create(createPurchaseOrder)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.errorService.showSuccess('Purchase order created successfully');
            // Force a refresh of the purchase order list
            this.router.navigateByUrl('/transactions', { skipLocationChange: true }).then(() => {
              this.router.navigate(['/transactions/purchase-orders']);
            });
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  submitPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const notes = prompt('Add any notes for submission (optional):');
    // If user clicks cancel, return without submitting
    if (notes === null) return;

    this.isLoading = true;

    this.purchaseOrderService.submit(this.purchaseOrderId, notes)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order submitted successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  approvePurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const notes = prompt('Add any approval notes (optional):');
    // If user clicks cancel, return without approving
    if (notes === null) return;

    this.isLoading = true;

    this.purchaseOrderService.approve(this.purchaseOrderId, notes)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order approved successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  rejectPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const reason = prompt('Please enter a reason for rejection:');
    if (reason === null) return; // User cancelled

    this.isLoading = true;

    this.purchaseOrderService.reject(this.purchaseOrderId, reason)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order rejected successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  cancelPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const reason = prompt('Please enter a reason for cancellation:');
    if (reason === null) return; // User cancelled

    this.isLoading = true;

    this.purchaseOrderService.cancel(this.purchaseOrderId, reason)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order cancelled successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  cancel(): void {
    this.router.navigate(['/transactions/purchase-orders']);
  }

  // Helper method to mark all controls in a form group as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
