using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class RecipeService : IRecipeService
{
    private readonly IRepository<Recipe> _recipeRepository;
    private readonly IRepository<RecipeIngredient> _recipeIngredientRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public RecipeService(
        IRepository<Recipe> recipeRepository,
        IRepository<RecipeIngredient> recipeIngredientRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _recipeRepository = recipeRepository;
        _recipeIngredientRepository = recipeIngredientRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<RecipeListDto>> GetAllRecipesAsync()
    {
        var recipes = await _dbContext.Recipes
            .Include(r => r.Product)
            .Include(r => r.Unit)
            .Include(r => r.Ingredients)
            .OrderBy(r => r.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<RecipeListDto>>(recipes);
    }

    public async Task<RecipeDto?> GetRecipeByIdAsync(int id)
    {
        var recipe = await _dbContext.Recipes
            .Include(r => r.Product)
            .Include(r => r.Unit)
            .Include(r => r.Ingredients)
            .FirstOrDefaultAsync(r => r.Id == id);

        if (recipe == null)
            return null;

        // Load related data for recipe ingredients
        foreach (var ingredient in recipe.Ingredients)
        {
            // Load product information
            if (ingredient.ProductId > 0)
            {
                ingredient.Product = await _dbContext.Products.FindAsync(ingredient.ProductId);
            }

            // Load unit information
            if (ingredient.UnitId.HasValue)
            {
                ingredient.Unit = await _dbContext.Units.FindAsync(ingredient.UnitId.Value);
            }
        }

        return _mapper.Map<RecipeDto>(recipe);
    }

    public async Task<IEnumerable<RecipeListDto>> GetRecipesByProductIdAsync(int productId)
    {
        var recipes = await _dbContext.Recipes
            .Include(r => r.Product)
            .Include(r => r.Unit)
            .Include(r => r.Ingredients)
            .Where(r => r.ProductId == productId)
            .OrderBy(r => r.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<RecipeListDto>>(recipes);
    }

    public async Task<IEnumerable<RecipeListDto>> GetActiveRecipesAsync()
    {
        var recipes = await _dbContext.Recipes
            .Include(r => r.Product)
            .Include(r => r.Unit)
            .Include(r => r.Ingredients)
            .Where(r => r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<RecipeListDto>>(recipes);
    }

    public async Task<IEnumerable<RecipeListDto>> GetSubRecipesAsync()
    {
        var recipes = await _dbContext.Recipes
            .Include(r => r.Product)
            .Include(r => r.Unit)
            .Include(r => r.Ingredients)
            .Where(r => r.IsSubRecipe)
            .OrderBy(r => r.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<RecipeListDto>>(recipes);
    }

    public async Task<RecipeDto> CreateRecipeAsync(CreateRecipeDto createRecipeDto)
    {
        var recipe = _mapper.Map<Recipe>(createRecipeDto);
        
        // Calculate the cost based on ingredients
        if (createRecipeDto.Ingredients.Any())
        {
            decimal totalCost = 0;
            
            foreach (var ingredientDto in createRecipeDto.Ingredients)
            {
                var product = await _dbContext.Products.FindAsync(ingredientDto.ProductId);
                if (product != null && product.CostPrice.HasValue)
                {
                    var ingredient = _mapper.Map<RecipeIngredient>(ingredientDto);
                    ingredient.Cost = product.CostPrice;
                    
                    // Apply wastage if specified
                    if (ingredientDto.WastagePercentage.HasValue && ingredientDto.WastagePercentage > 0)
                    {
                        var wastageMultiplier = 1 + (ingredientDto.WastagePercentage.Value / 100);
                        totalCost += product.CostPrice.Value * ingredient.Quantity * wastageMultiplier;
                    }
                    else
                    {
                        totalCost += product.CostPrice.Value * ingredient.Quantity;
                    }
                    
                    recipe.Ingredients.Add(ingredient);
                }
            }
            
            recipe.Cost = totalCost / recipe.Yield;
        }
        
        await _recipeRepository.AddAsync(recipe);
        
        // Reload the recipe with all related entities
        var createdRecipe = await _dbContext.Recipes
            .Include(r => r.Product)
            .Include(r => r.Unit)
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.Product)
            .Include(r => r.Ingredients)
                .ThenInclude(i => i.Unit)
            .FirstOrDefaultAsync(r => r.Id == recipe.Id);
            
        return _mapper.Map<RecipeDto>(createdRecipe);
    }

    public async Task UpdateRecipeAsync(UpdateRecipeDto updateRecipeDto)
    {
        var recipe = await _recipeRepository.GetByIdAsync(updateRecipeDto.Id);
        if (recipe == null)
            throw new KeyNotFoundException($"Recipe with ID {updateRecipeDto.Id} not found.");

        _mapper.Map(updateRecipeDto, recipe);
        
        // Recalculate the cost
        await CalculateRecipeCostAsync(recipe.Id);
        
        await _recipeRepository.UpdateAsync(recipe);
    }

    public async Task DeleteRecipeAsync(int id)
    {
        var recipe = await _recipeRepository.GetByIdAsync(id);
        if (recipe == null)
            throw new KeyNotFoundException($"Recipe with ID {id} not found.");

        // Check if this recipe is used as an ingredient in other recipes
        var usedAsIngredient = await _dbContext.RecipeIngredients
            .AnyAsync(ri => ri.ProductId == recipe.ProductId);
            
        if (usedAsIngredient)
            throw new InvalidOperationException("Cannot delete recipe as it is used as an ingredient in other recipes.");

        await _recipeRepository.DeleteAsync(recipe);
    }

    public async Task<RecipeIngredientDto> AddIngredientAsync(int recipeId, CreateRecipeIngredientDto createRecipeIngredientDto)
    {
        var recipe = await _recipeRepository.GetByIdAsync(recipeId);
        if (recipe == null)
            throw new KeyNotFoundException($"Recipe with ID {recipeId} not found.");

        var ingredient = _mapper.Map<RecipeIngredient>(createRecipeIngredientDto);
        ingredient.RecipeId = recipeId;
        
        // Get the product cost
        var product = await _dbContext.Products.FindAsync(createRecipeIngredientDto.ProductId);
        if (product != null && product.CostPrice.HasValue)
        {
            ingredient.Cost = product.CostPrice;
        }
        
        await _recipeIngredientRepository.AddAsync(ingredient);
        
        // Recalculate the recipe cost
        await CalculateRecipeCostAsync(recipeId);
        
        // Reload the ingredient with related entities
        var createdIngredient = await _dbContext.RecipeIngredients
            .Include(ri => ri.Product)
            .Include(ri => ri.Unit)
            .FirstOrDefaultAsync(ri => ri.Id == ingredient.Id);
            
        return _mapper.Map<RecipeIngredientDto>(createdIngredient);
    }

    public async Task UpdateIngredientAsync(UpdateRecipeIngredientDto updateRecipeIngredientDto)
    {
        var ingredient = await _recipeIngredientRepository.GetByIdAsync(updateRecipeIngredientDto.Id);
        if (ingredient == null)
            throw new KeyNotFoundException($"Recipe ingredient with ID {updateRecipeIngredientDto.Id} not found.");

        _mapper.Map(updateRecipeIngredientDto, ingredient);
        
        // Update the cost if the product has changed
        if (ingredient.ProductId != updateRecipeIngredientDto.ProductId)
        {
            var product = await _dbContext.Products.FindAsync(updateRecipeIngredientDto.ProductId);
            if (product != null && product.CostPrice.HasValue)
            {
                ingredient.Cost = product.CostPrice;
            }
        }
        
        await _recipeIngredientRepository.UpdateAsync(ingredient);
        
        // Recalculate the recipe cost
        await CalculateRecipeCostAsync(ingredient.RecipeId);
    }

    public async Task RemoveIngredientAsync(int recipeId, int ingredientId)
    {
        var ingredient = await _recipeIngredientRepository.GetByIdAsync(ingredientId);
        if (ingredient == null || ingredient.RecipeId != recipeId)
            throw new KeyNotFoundException($"Recipe ingredient with ID {ingredientId} not found for recipe {recipeId}.");

        await _recipeIngredientRepository.DeleteAsync(ingredient);
        
        // Recalculate the recipe cost
        await CalculateRecipeCostAsync(recipeId);
    }

    public async Task<decimal> CalculateRecipeCostAsync(int recipeId)
    {
        var recipe = await _dbContext.Recipes
            .Include(r => r.Ingredients)
            .FirstOrDefaultAsync(r => r.Id == recipeId);
            
        if (recipe == null)
            throw new KeyNotFoundException($"Recipe with ID {recipeId} not found.");

        decimal totalCost = 0;
        
        foreach (var ingredient in recipe.Ingredients)
        {
            // Get the latest product cost
            var product = await _dbContext.Products.FindAsync(ingredient.ProductId);
            if (product != null && product.CostPrice.HasValue)
            {
                ingredient.Cost = product.CostPrice;
                
                // Apply wastage if specified
                if (ingredient.WastagePercentage.HasValue && ingredient.WastagePercentage > 0)
                {
                    var wastageMultiplier = 1 + (ingredient.WastagePercentage.Value / 100);
                    totalCost += product.CostPrice.Value * ingredient.Quantity * wastageMultiplier;
                }
                else
                {
                    totalCost += product.CostPrice.Value * ingredient.Quantity;
                }
            }
        }
        
        // Update the recipe cost
        recipe.Cost = recipe.Yield > 0 ? totalCost / recipe.Yield : totalCost;
        await _recipeRepository.UpdateAsync(recipe);
        
        return recipe.Cost ?? 0;
    }
}
