namespace SCM.Application.DTOs;

public class StoreDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int LocationId { get; set; }
    public string? LocationName { get; set; }
    public string? CompanyName { get; set; }
    public bool IsSalesPoint { get; set; }
    public string? LogoPath { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int CostCentersCount { get; set; }
    public List<CostCenterDto>? CostCenters { get; set; }
}

public class StoreListDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int LocationId { get; set; }
    public string? LocationName { get; set; }
    public string? CompanyName { get; set; }
    public bool IsSalesPoint { get; set; }
    public bool IsActive { get; set; }
    public int CostCentersCount { get; set; }
}

public class CreateStoreDto
{
    public string Name { get; set; } = string.Empty;
    public int LocationId { get; set; }
    public bool IsSalesPoint { get; set; } = false;
    public string? LogoPath { get; set; }
}

public class UpdateStoreDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int LocationId { get; set; }
    public bool IsSalesPoint { get; set; }
    public string? LogoPath { get; set; }
    public bool IsActive { get; set; }
}
