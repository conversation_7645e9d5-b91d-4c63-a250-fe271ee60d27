import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';

import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CompanyService } from '../../../../core/services/company.service';
import { LocationService } from '../../../../core/services/location.service';
import { Company } from '../../../../core/models/company.model';
import { LocationList } from '../../../../core/models/location.model';
import { finalize, forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'app-company-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule,
    MatListModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './company-detail.component.html',
  styleUrls: ['./company-detail.component.scss']
})
export class CompanyDetailComponent implements OnInit {
  company: Company | null = null;
  locations: LocationList[] = [];
  isLoading = false;
  locationColumns: string[] = ['name', 'code', 'city', 'country', 'isActive', 'storesCount', 'actions'];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private companyService: CompanyService,
    private locationService: LocationService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadCompanyData();
  }

  loadCompanyData(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.snackBar.open('Company ID is missing', 'Close', { duration: 3000 });
      this.router.navigate(['/companies']);
      return;
    }

    this.isLoading = true;

    forkJoin({
      company: this.companyService.getById(+id).pipe(
        catchError(error => {
          console.error('Error loading company:', error);
          this.snackBar.open('Failed to load company details', 'Close', { duration: 3000 });
          return of(null);
        })
      ),
      locations: this.locationService.getByCompany(+id).pipe(
        catchError(error => {
          console.error('Error loading locations:', error);
          return of([]);
        })
      )
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe(result => {
      this.company = result.company;
      this.locations = result.locations;

      if (!this.company) {
        this.router.navigate(['/companies']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/companies']);
  }

  editCompany(): void {
    if (this.company) {
      this.router.navigate(['/companies/edit', this.company.id]);
    }
  }

  viewLocation(id: number): void {
    this.router.navigate(['/locations', id]);
  }

  addLocation(): void {
    if (this.company) {
      this.router.navigate(['/locations/new'], {
        queryParams: { companyId: this.company.id }
      });
    }
  }
}
