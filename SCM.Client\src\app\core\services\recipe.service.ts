import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Recipe, RecipeList, CreateRecipe, UpdateRecipe, RecipeIngredient, CreateRecipeIngredient, UpdateRecipeIngredient } from '../models/recipe.model';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class RecipeService {
  private readonly path = 'recipes';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<RecipeList[]> {
    return this.apiService.get<RecipeList[]>(this.path);
  }

  getById(id: number): Observable<Recipe> {
    return this.apiService.get<Recipe>(`${this.path}/${id}`);
  }

  getByProductId(productId: number): Observable<RecipeList[]> {
    return this.apiService.get<RecipeList[]>(`${this.path}/product/${productId}`);
  }

  getActive(): Observable<RecipeList[]> {
    return this.apiService.get<RecipeList[]>(`${this.path}/active`);
  }

  getSubRecipes(): Observable<RecipeList[]> {
    return this.apiService.get<RecipeList[]>(`${this.path}/subrecipes`);
  }

  create(recipe: CreateRecipe): Observable<Recipe> {
    return this.apiService.post<Recipe, CreateRecipe>(this.path, recipe);
  }

  update(id: number, recipe: UpdateRecipe): Observable<void> {
    return this.apiService.put<void, UpdateRecipe>(`${this.path}/${id}`, recipe);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  addIngredient(recipeId: number, ingredient: CreateRecipeIngredient): Observable<RecipeIngredient> {
    return this.apiService.post<RecipeIngredient, CreateRecipeIngredient>(`${this.path}/${recipeId}/ingredients`, ingredient);
  }

  updateIngredient(id: number, ingredient: UpdateRecipeIngredient): Observable<void> {
    return this.apiService.put<void, UpdateRecipeIngredient>(`${this.path}/ingredients/${id}`, ingredient);
  }

  removeIngredient(recipeId: number, ingredientId: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${recipeId}/ingredients/${ingredientId}`);
  }

  calculateCost(id: number): Observable<number> {
    return this.apiService.get<number>(`${this.path}/${id}/calculate-cost`);
  }
}
