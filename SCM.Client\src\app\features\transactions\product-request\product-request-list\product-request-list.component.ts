import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { ProductRequestService } from '../../../../core/services/product-request.service';
import { ErrorService } from '../../../../core/services/error.service';
import { ProductRequestListItem } from '../../../../core/models/product-request.model';
import { finalize, catchError, of } from 'rxjs';

@Component({
  selector: 'app-product-request-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatChipsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './product-request-list.component.html',
  styleUrls: ['./product-request-list.component.scss']
})
export class ProductRequestListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'referenceNumber',
    'costCenterName',
    'requestDate',
    'status',
    'createdByName',
    'actions'
  ];

  productRequests: ProductRequestListItem[] = [];
  filteredRequests: ProductRequestListItem[] = [];
  isLoading = false;
  searchTerm = '';
  selectedStatus = '';

  statusOptions = ['All', 'Draft', 'Submitted', 'Approved', 'Rejected', 'Completed', 'Cancelled'];

  constructor(
    private productRequestService: ProductRequestService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private errorService: ErrorService
  ) { }

  ngOnInit(): void {
    this.loadProductRequests();
  }

  loadProductRequests(): void {
    this.isLoading = true;

    // Get product requests directly
    this.productRequestService.getAll()
      .pipe(
        finalize(() => this.isLoading = false),
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      )
      .subscribe(requests => {
        console.log('Loaded product requests:', requests);
        this.productRequests = requests;
        this.applyFilters();
      });
  }

  applyFilters(): void {
    let filtered = [...this.productRequests];

    // Make sure all requests have a reference number
    filtered = filtered.map(request => {
      if (!request.referenceNumber && request.id) {
        request.referenceNumber = `PR-${request.id.toString().padStart(5, '0')}`;
      }
      return request;
    });

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(request =>
        request.referenceNumber.toLowerCase().includes(searchLower) ||
        request.costCenterName.toLowerCase().includes(searchLower) ||
        (request.createdByName && request.createdByName.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (this.selectedStatus && this.selectedStatus !== 'All') {
      filtered = filtered.filter(request => request.status === this.selectedStatus);
    }

    this.filteredRequests = filtered;
  }

  onSearch(event: Event): void {
    this.searchTerm = (event.target as HTMLInputElement).value;
    this.applyFilters();
  }

  onStatusChange(): void {
    this.applyFilters();
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.applyFilters();
  }

  createProductRequest(): void {
    this.router.navigate(['/transactions/product-requests/new']);
  }

  editProductRequest(id: number): void {
    this.router.navigate([`/transactions/product-requests/${id}/edit`]);
  }

  viewProductRequest(id: number): void {
    this.router.navigate([`/transactions/product-requests/${id}/view`]);
  }

  deleteProductRequest(id: number): void {
    if (confirm('Are you sure you want to delete this product request?')) {
      this.isLoading = true;

      this.productRequestService.delete(id)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Product request deleted successfully', 'Close', { duration: 3000 });
            this.loadProductRequests();
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  approveProductRequest(id: number): void {
    this.isLoading = true;

    // First, get the transaction details to ensure we have the correct reference number
    this.productRequestService.getById(id).subscribe({
      next: (request) => {
        console.log('Approving product request:', request);

        this.productRequestService.approve({ id, notes: '' })
          .pipe(finalize(() => this.isLoading = false))
          .subscribe({
            next: () => {
              this.snackBar.open(`Product request ${request.referenceNumber} approved successfully. You can now create a purchase order from it.`, 'Close', { duration: 5000 });
              this.loadProductRequests();
            },
            error: (error) => this.errorService.handleError(error)
          });
      },
      error: (error) => {
        this.isLoading = false;
        this.errorService.handleError(error);
      }
    });
  }

  rejectProductRequest(id: number): void {
    const reason = prompt('Please enter a reason for rejection:');
    if (reason) {
      this.isLoading = true;

      this.productRequestService.reject({ id, reason })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Product request rejected successfully', 'Close', { duration: 3000 });
            this.loadProductRequests();
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  createPurchaseOrder(id: number): void {
    this.router.navigate(['/transactions/purchase-orders/new'], {
      queryParams: { requestId: id }
    });

    this.snackBar.open('Creating purchase order from approved product request. You will be able to add a supplier and modify details.', 'Close', { duration: 5000 });
  }

  showWorkflowInfo(): void {
    const workflowInfo = `
      Product Request Workflow:
      1. Create and Submit a Product Request
      2. Request is reviewed and Approved by an authorized user
      3. Create a Purchase Order from the approved request
      4. Submit the Purchase Order to the supplier
      5. Receive goods when they arrive
    `;

    this.snackBar.open(workflowInfo, 'Close', { duration: 10000 });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Draft': return 'status-draft';
      case 'Submitted': return 'status-submitted';
      case 'Approved': return 'status-approved';
      case 'Rejected': return 'status-rejected';
      case 'Completed': return 'status-completed';
      case 'Cancelled': return 'status-cancelled';
      default: return '';
    }
  }
}
