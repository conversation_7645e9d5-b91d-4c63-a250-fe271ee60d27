using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class UnitService : IUnitService
{
    private readonly IRepository<Unit> _unitRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public UnitService(
        IRepository<Unit> unitRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _unitRepository = unitRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<UnitDto>> GetAllUnitsAsync()
    {
        var units = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .ToListAsync();

        return _mapper.Map<IEnumerable<UnitDto>>(units);
    }

    public async Task<UnitDto?> GetUnitByIdAsync(int id)
    {
        var unit = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .FirstOrDefaultAsync(u => u.Id == id);

        return unit != null ? _mapper.Map<UnitDto>(unit) : null;
    }

    public async Task<IEnumerable<UnitDto>> GetUnitsByUnitGroupIdAsync(int unitGroupId)
    {
        var units = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .Where(u => u.UnitGroupId == unitGroupId)
            .ToListAsync();

        return _mapper.Map<IEnumerable<UnitDto>>(units);
    }

    public async Task<UnitDto> CreateUnitAsync(CreateUnitDto createUnitDto)
    {
        // Check if a unit with the same name already exists
        var existingUnit = await _dbContext.Units
            .FirstOrDefaultAsync(u => u.Name.ToLower() == createUnitDto.Name.ToLower());

        if (existingUnit != null)
            throw new InvalidOperationException($"A unit with the name '{createUnitDto.Name}' already exists.");

        // Ensure BaseConversionFactor is set
        if (createUnitDto.BaseConversionFactor <= 0)
            createUnitDto.BaseConversionFactor = 1;

        var unit = _mapper.Map<Unit>(createUnitDto);

        // If this is a base unit, set ConversionFactor to 1
        if (unit.IsBaseUnit == true)
        {
            unit.ConversionFactor = 1;
            unit.BaseConversionFactor = 1;
        }
        else
        {
            // If ConversionFactor is not set, use the default value
            if (unit.ConversionFactor == null)
            {
                unit.ConversionFactor = 1;
            }

            // Ensure BaseConversionFactor is set
            if (unit.BaseConversionFactor <= 0)
            {
                unit.BaseConversionFactor = 1;
            }
        }

        await _unitRepository.AddAsync(unit);

        // Reload the unit with the unit group
        var createdUnit = await _dbContext.Units
            .Include(u => u.UnitGroup)
            .FirstOrDefaultAsync(u => u.Id == unit.Id);

        return _mapper.Map<UnitDto>(createdUnit);
    }

    public async Task UpdateUnitAsync(UpdateUnitDto updateUnitDto)
    {
        var unit = await _unitRepository.GetByIdAsync(updateUnitDto.Id);
        if (unit == null)
            throw new KeyNotFoundException($"Unit with ID {updateUnitDto.Id} not found.");

        // Check if the name is being changed and if it would conflict with an existing unit
        if (unit.Name != updateUnitDto.Name)
        {
            var existingUnit = await _dbContext.Units
                .FirstOrDefaultAsync(u => u.Name.ToLower() == updateUnitDto.Name.ToLower() && u.Id != updateUnitDto.Id);

            if (existingUnit != null)
                throw new InvalidOperationException($"A unit with the name '{updateUnitDto.Name}' already exists.");
        }

        // If this is a base unit, set ConversionFactor to 1
        if (updateUnitDto.IsBaseUnit)
        {
            updateUnitDto.ConversionFactor = 1;
            updateUnitDto.BaseConversionFactor = 1;
        }
        else
        {
            // If ConversionFactor is not set, use the default value
            if (updateUnitDto.ConversionFactor == null)
            {
                updateUnitDto.ConversionFactor = 1;
            }

            // Ensure BaseConversionFactor is set
            if (updateUnitDto.BaseConversionFactor <= 0)
            {
                updateUnitDto.BaseConversionFactor = 1;
            }
        }

        _mapper.Map(updateUnitDto, unit);
        await _unitRepository.UpdateAsync(unit);
    }

    public async Task DeleteUnitAsync(int id)
    {
        var unit = await _unitRepository.GetByIdAsync(id);
        if (unit == null)
            throw new KeyNotFoundException($"Unit with ID {id} not found.");

        // Check if there are any products using this unit
        var hasProducts = await _dbContext.Products.AnyAsync(p => p.UnitId == id || p.SalesUnitId == id);
        if (hasProducts)
            throw new InvalidOperationException($"Cannot delete Unit with ID {id} because it is being used by one or more products.");

        // Check if there are any recipes using this unit
        var hasRecipes = await _dbContext.Recipes.AnyAsync(r => r.UnitId == id);
        if (hasRecipes)
            throw new InvalidOperationException($"Cannot delete Unit with ID {id} because it is being used by one or more recipes.");

        await _unitRepository.DeleteAsync(unit);
    }
}
