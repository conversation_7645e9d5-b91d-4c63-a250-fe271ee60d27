import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ProductCostCenterLinkService } from '../../../../core/services/product-cost-center-link.service';
import { ProductService } from '../../../../core/services/product.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { Product } from '../../../../core/models/product.model';
import { CostCenter } from '../../../../core/models/cost-center.model';
import { ProductCostCenterLink, CreateProductCostCenterLink, UpdateProductCostCenterLink } from '../../../../core/models/product-cost-center-link.model';
import { forkJoin, catchError, of, Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';

@Component({
  selector: 'app-product-cost-center-link-detail',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    RouterModule
  ],
  templateUrl: './product-cost-center-link-detail.component.html',
  styleUrls: ['./product-cost-center-link-detail.component.scss']
})
export class ProductCostCenterLinkDetailComponent implements OnInit {
  linkForm!: FormGroup;
  isEditMode: boolean = false;
  linkId: string | null = null;
  isLoading: boolean = false;
  isSaving: boolean = false;

  // Data from API
  products: Product[] = [];
  filteredProducts!: Observable<Product[]>;
  costCenters: CostCenter[] = [];
  filteredCostCenters!: Observable<CostCenter[]>;
  currentLink: ProductCostCenterLink | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private productCostCenterLinkService: ProductCostCenterLinkService,
    private productService: ProductService,
    private costCenterService: CostCenterService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadInitialData();

    // Check if we're in edit mode
    this.linkId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.linkId;

    if (this.isEditMode && this.linkId) {
      this.loadLinkData(+this.linkId);
    }
  }

  private initializeForm(): void {
    this.linkForm = this.fb.group({
      productId: [null, Validators.required],
      productSearch: [''],
      costCenterId: [null, Validators.required],
      costCenterSearch: [''],
      minStock: [null],
      maxStock: [null],
      reorderPoint: [null]
    });

    // Initialize filtered observables
    this.filteredProducts = this.linkForm.get('productSearch')!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterProducts(value || ''))
    );

    this.filteredCostCenters = this.linkForm.get('costCenterSearch')!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterCostCenters(value || ''))
    );
  }

  private loadInitialData(): void {
    this.isLoading = true;

    forkJoin({
      products: this.productService.getAll(),
      costCenters: this.costCenterService.getAllCostCenters()
    }).pipe(
      catchError(error => {
        console.error('Error loading initial data', error);
        this.snackBar.open('Error loading data. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        return of({ products: [], costCenters: [] });
      })
    ).subscribe(({ products, costCenters }) => {
      this.products = products;
      this.costCenters = costCenters;
      this.isLoading = false;
    });
  }

  loadLinkData(id: number): void {
    this.productCostCenterLinkService.getById(id).pipe(
      catchError(error => {
        console.error('Error loading link data', error);
        this.snackBar.open('Error loading link data. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/products/cost-center-links']);
        return of(null);
      })
    ).subscribe(link => {
      if (link) {
        this.currentLink = link;
        this.linkForm.patchValue({
          productId: link.productId,
          costCenterId: link.costCenterId,
          minStock: link.minStock,
          maxStock: link.maxStock,
          reorderPoint: link.reorderPoint
        });
      }
    });
  }

  onSubmit(): void {
    if (this.linkForm.valid) {
      this.isSaving = true;
      const formData = this.linkForm.value;

      if (this.isEditMode && this.currentLink) {
        const updateData: UpdateProductCostCenterLink = {
          id: this.currentLink.id,
          productId: formData.productId,
          costCenterId: formData.costCenterId,
          minStock: formData.minStock,
          maxStock: formData.maxStock,
          reorderPoint: formData.reorderPoint,
          isActive: true
        };

        this.productCostCenterLinkService.update(this.currentLink.id, updateData).pipe(
          catchError(error => {
            console.error('Error updating link', error);
            this.snackBar.open('Error updating link. Please try again.', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            return of(null);
          })
        ).subscribe(() => {
          this.isSaving = false;
          this.snackBar.open('Link updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/products/cost-center-links']);
        });
      } else {
        const createData: CreateProductCostCenterLink = {
          productId: formData.productId,
          costCenterId: formData.costCenterId,
          minStock: formData.minStock,
          maxStock: formData.maxStock,
          reorderPoint: formData.reorderPoint
        };

        this.productCostCenterLinkService.create(createData).pipe(
          catchError(error => {
            console.error('Error creating link', error);
            this.snackBar.open('Error creating link. Please try again.', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            return of(null);
          })
        ).subscribe(() => {
          this.isSaving = false;
          this.snackBar.open('Link created successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/products/cost-center-links']);
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.router.navigate(['/products/cost-center-links']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.linkForm.controls).forEach(key => {
      const control = this.linkForm.get(key);
      control?.markAsTouched();
    });
  }

  getProductDisplayName(product: Product): string {
    return `${product.code} - ${product.name}`;
  }

  getCostCenterDisplayName(costCenter: any): string {
    return costCenter.name;
  }

  get submitButtonText(): string {
    return this.isEditMode ? 'Update Link' : 'Create Link';
  }

  get pageTitle(): string {
    return this.isEditMode ? 'Edit Product Cost Center Link' : 'Create Product Cost Center Link';
  }

  // Autocomplete functionality methods
  private _filterProducts(value: string): Product[] {
    const filterValue = value.toLowerCase();
    return this.products.filter(product =>
      product.name.toLowerCase().includes(filterValue) ||
      product.code.toLowerCase().includes(filterValue)
    );
  }

  private _filterCostCenters(value: string): CostCenter[] {
    const filterValue = value.toLowerCase();
    return this.costCenters.filter(costCenter =>
      costCenter.name.toLowerCase().includes(filterValue)
    );
  }

  onProductSelected(product: Product): void {
    this.linkForm.patchValue({ productId: product.id });
  }

  onCostCenterSelected(costCenter: CostCenter): void {
    this.linkForm.patchValue({ costCenterId: costCenter.id });
  }

  displayProduct(product: Product): string {
    return product ? `${product.code} - ${product.name}` : '';
  }

  displayCostCenter(costCenter: CostCenter): string {
    return costCenter ? costCenter.name : '';
  }
}
