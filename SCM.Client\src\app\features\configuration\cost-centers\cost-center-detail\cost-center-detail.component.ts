import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { CostCenter } from '../../../../core/models/cost-center.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-cost-center-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule,
    MatListModule
  ],
  templateUrl: './cost-center-detail.component.html',
  styleUrls: ['./cost-center-detail.component.scss']
})
export class CostCenterDetailComponent implements OnInit {
  costCenter: CostCenter | null = null;
  isLoading = false;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private costCenterService: CostCenterService,
    private snackBar: MatSnackBar
  ) {}
  
  ngOnInit(): void {
    this.loadCostCenter();
  }
  
  loadCostCenter(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.snackBar.open('Cost center ID is missing', 'Close', { duration: 3000 });
      this.router.navigate(['/configuration/cost-centers']);
      return;
    }
    
    this.isLoading = true;
    this.costCenterService.getById(+id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.costCenter = data;
        },
        error: (error) => {
          console.error('Error loading cost center:', error);
          this.snackBar.open('Failed to load cost center details', 'Close', { duration: 3000 });
          this.router.navigate(['/configuration/cost-centers']);
        }
      });
  }
  
  goBack(): void {
    this.router.navigate(['/configuration/cost-centers']);
  }
  
  editCostCenter(): void {
    if (this.costCenter) {
      this.router.navigate(['/configuration/cost-centers/edit', this.costCenter.id]);
    }
  }
}
