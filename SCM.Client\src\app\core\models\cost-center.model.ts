export interface CostCenter {
  id: number;
  name: string;
  code?: string;
  storeId: number;
  storeName?: string;
  locationName?: string;
  companyName?: string;
  typeId?: number;
  typeName?: string;
  description?: string;
  autoTransfer: boolean;
  isSalesPoint: boolean;
  abbreviation?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CostCenterList {
  id: number;
  name: string;
  code?: string;
  storeId: number;
  storeName?: string;
  locationName?: string;
  companyName?: string;
  typeName?: string;
  isSalesPoint: boolean;
  isActive: boolean;
}

export interface CreateCostCenter {
  name: string;
  code?: string;
  storeId: number;
  typeId?: number;
  description?: string;
  autoTransfer: boolean;
  isSalesPoint: boolean;
  abbreviation?: string;
}

export interface UpdateCostCenter {
  id: number;
  name: string;
  code?: string;
  storeId: number;
  typeId?: number;
  description?: string;
  autoTransfer: boolean;
  isSalesPoint: boolean;
  abbreviation?: string;
  isActive: boolean;
}

export interface CostCenterType {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateCostCenterType {
  name: string;
  description?: string;
}

export interface UpdateCostCenterType {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
}

// Store interfaces moved to store.model.ts
