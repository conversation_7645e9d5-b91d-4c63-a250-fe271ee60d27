<div class="units-container">
  <div class="page-header">
    <h1>Units Management</h1>
    <button mat-stroked-button color="accent" (click)="openImportDialog()">
      <mat-icon>upload</mat-icon>
      Import Units
    </button>
  </div>

  <div *ngIf="filteredUnitGroupId" class="filter-notification">
    <div class="filter-message">
      <mat-icon>filter_list</mat-icon>
      <span>Showing units for selected unit group</span>
    </div>
    <button mat-button color="primary" (click)="clearFilter()">
      <mat-icon>clear</mat-icon>
      Clear Filter
    </button>
  </div>

  <div class="units-content">
    <!-- Form Card -->
    <mat-card class="form-card">
      <mat-card-header>
        <mat-card-title>{{ isEditMode ? 'Edit Unit' : 'Add Unit' }}</mat-card-title>
      </mat-card-header>

      <mat-card-content>
        <form [formGroup]="unitForm" (ngSubmit)="saveUnit()">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter unit name" required>
              <mat-error *ngIf="unitForm.get('name')?.hasError('required')">
                Name is required
              </mat-error>
              <mat-error *ngIf="unitForm.get('name')?.hasError('maxlength')">
                Name cannot exceed 50 characters
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Abbreviation</mat-label>
              <input matInput formControlName="abbreviation" placeholder="Enter abbreviation" required>
              <mat-error *ngIf="unitForm.get('abbreviation')?.hasError('required')">
                Abbreviation is required
              </mat-error>
              <mat-error *ngIf="unitForm.get('abbreviation')?.hasError('maxlength')">
                Abbreviation cannot exceed 10 characters
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Unit Group</mat-label>
              <mat-select formControlName="unitGroupId" required>
                <mat-option *ngFor="let group of unitGroups" [value]="group.id">
                  {{ group.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="unitForm.get('unitGroupId')?.hasError('required')">
                Unit Group is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Conversion Factor</mat-label>
              <input matInput type="number" formControlName="conversionFactor" placeholder="Enter conversion factor" required>
              <mat-hint>Relative to the base unit in the group</mat-hint>
              <mat-error *ngIf="unitForm.get('conversionFactor')?.hasError('required')">
                Conversion Factor is required
              </mat-error>
              <mat-error *ngIf="unitForm.get('conversionFactor')?.hasError('min')">
                Conversion Factor must be greater than 0
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Base Conversion Factor</mat-label>
              <input matInput type="number" formControlName="baseConversionFactor" placeholder="Enter base conversion factor">
              <mat-hint>Base conversion factor for this unit</mat-hint>
            </mat-form-field>
          </div>

          <div class="form-field-checkbox">
            <mat-checkbox formControlName="isBaseUnit">Base Unit</mat-checkbox>
            <mat-hint class="checkbox-hint">Mark as the base unit for this group</mat-hint>
          </div>

          <div class="form-field-checkbox" *ngIf="isEditMode">
            <mat-checkbox formControlName="isActive">Active</mat-checkbox>
          </div>

          <div class="form-actions">
            <button type="submit" mat-raised-button color="primary" [disabled]="unitForm.invalid || isSubmitting">
              <mat-icon>save</mat-icon>
              {{ isEditMode ? 'Update' : 'Save' }}
            </button>

            <button type="button" mat-button (click)="resetForm()" [disabled]="isSubmitting">
              <mat-icon>refresh</mat-icon>
              Reset
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Table Card -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>Units</mat-card-title>
      </mat-card-header>

      <mat-card-content>
        <div class="filter-container">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Filter</mat-label>
            <input matInput (keyup)="applyFilter($event)" placeholder="Search units">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>

        <div class="spinner-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
        </div>

        <table mat-table [dataSource]="dataSource" class="full-width" matSort>
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
            <td mat-cell *matCellDef="let unit">{{ unit.id }}</td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let unit">{{ unit.name }}</td>
          </ng-container>

          <!-- Abbreviation Column -->
          <ng-container matColumnDef="abbreviation">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Abbreviation</th>
            <td mat-cell *matCellDef="let unit">{{ unit.abbreviation }}</td>
          </ng-container>

          <!-- Unit Group Column -->
          <ng-container matColumnDef="unitGroupName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Unit Group</th>
            <td mat-cell *matCellDef="let unit">{{ unit.unitGroupName }}</td>
          </ng-container>

          <!-- Conversion Factor Column -->
          <ng-container matColumnDef="conversionFactor">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Conversion Factor</th>
            <td mat-cell *matCellDef="let unit">{{ unit.conversionFactor }}</td>
          </ng-container>

          <!-- Base Conversion Factor Column -->
          <ng-container matColumnDef="baseConversionFactor">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Base Conversion Factor</th>
            <td mat-cell *matCellDef="let unit">{{ unit.baseConversionFactor }}</td>
          </ng-container>

          <!-- Base Unit Column -->
          <ng-container matColumnDef="isBaseUnit">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Base Unit</th>
            <td mat-cell *matCellDef="let unit">
              <mat-icon *ngIf="unit.isBaseUnit" color="primary">check_circle</mat-icon>
              <mat-icon *ngIf="!unit.isBaseUnit" color="warn">cancel</mat-icon>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let unit">
              <span [ngClass]="unit.isActive ? 'status-active' : 'status-inactive'">
                {{ unit.isActive ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let unit">
              <button mat-icon-button color="primary" (click)="editUnit(unit)"
                      [disabled]="!canEdit()" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteUnit(unit.id)"
                      [disabled]="!canDelete()" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          <!-- Row shown when there is no matching data -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="8">No data matching the filter</td>
          </tr>
        </table>

        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" showFirstLastButtons></mat-paginator>
      </mat-card-content>
    </mat-card>
  </div>
</div>
