import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { TransactionHeader } from '../../../../core/models/transaction.model';

interface DialogData {
  purchaseOrders: TransactionHeader[];
}

@Component({
  selector: 'app-select-purchase-order-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatTableModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    FormsModule
  ],
  templateUrl: './select-purchase-order-dialog.component.html',
  styleUrls: ['./select-purchase-order-dialog.component.scss']
})
export class SelectPurchaseOrderDialogComponent implements OnInit {
  displayedColumns: string[] = ['referenceNumber', 'supplierName', 'costCenterName', 'transactionDate', 'totalAmount', 'actions'];
  filteredOrders: TransactionHeader[] = [];
  searchTerm = '';

  constructor(
    public dialogRef: MatDialogRef<SelectPurchaseOrderDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {}

  ngOnInit(): void {
    // Make sure all orders have a reference number
    this.filteredOrders = this.data.purchaseOrders.map(order => {
      // Ensure reference number is set
      if (!order.referenceNumber && order.id) {
        order.referenceNumber = `PO-${order.id.toString().padStart(5, '0')}`;
      }

      // Log the data for debugging
      console.log('Purchase order:', order);

      return order;
    });
  }

  onSearch(event: Event): void {
    this.searchTerm = (event.target as HTMLInputElement).value;
    this.applyFilter();
  }

  applyFilter(): void {
    if (!this.searchTerm) {
      // Make sure all orders have a reference number
      this.filteredOrders = this.data.purchaseOrders.map(order => {
        // Ensure reference number is set
        if (!order.referenceNumber && order.id) {
          order.referenceNumber = `PO-${order.id.toString().padStart(5, '0')}`;
        }
        return order;
      });
      return;
    }

    const searchLower = this.searchTerm.toLowerCase();
    this.filteredOrders = this.data.purchaseOrders.filter(order => {
      const refNumber = order.referenceNumber || `PO-${order.id.toString().padStart(5, '0')}`;
      const supplier = order.supplierName || '';
      const costCenter = order.sourceCostCenterName || '';

      return refNumber.toLowerCase().includes(searchLower) ||
             supplier.toLowerCase().includes(searchLower) ||
             costCenter.toLowerCase().includes(searchLower);
    });
  }

  selectOrder(order: TransactionHeader): void {
    this.dialogRef.close(order);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
