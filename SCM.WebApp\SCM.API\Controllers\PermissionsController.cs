using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class PermissionsController : ApiControllerBase
{
    private readonly IPermissionService _permissionService;
    private readonly ILogger<PermissionsController> _logger;

    public PermissionsController(IPermissionService permissionService, ILogger<PermissionsController> logger)
    {
        _permissionService = permissionService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<PermissionDto>>> GetAll()
    {
        try
        {
            var permissions = await _permissionService.GetAllPermissionsAsync();
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving permissions");
            return StatusCode(500, "An error occurred while retrieving permissions");
        }
    }

    [HttpGet("by-category")]
    public async Task<ActionResult<IEnumerable<PermissionCategoryDto>>> GetByCategory()
    {
        try
        {
            var permissions = await _permissionService.GetPermissionsByCategoryAsync();
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving permissions by category");
            return StatusCode(500, "An error occurred while retrieving permissions");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<PermissionDto>> GetById(int id)
    {
        try
        {
            var permissions = await _permissionService.GetPermissionByIdAsync(id);
            if (permissions == null)
                return NotFound();

            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving permission {PermissionId}", id);
            return StatusCode(500, "An error occurred while retrieving the permission");
        }
    }

    [HttpGet("by-name/{name}")]
    public async Task<ActionResult<PermissionDto>> GetByName(string name)
    {
        try
        {
            var permissions = await _permissionService.GetPermissionByNameAsync(name);
            if (permissions == null)
                return NotFound();

            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving permission {PermissionName}", name);
            return StatusCode(500, "An error occurred while retrieving the permission");
        }
    }

    [HttpPost("initialize")]
    public async Task<IActionResult> InitializeDefaultPermissions()
    {
        try
        {
            await _permissionService.InitializeDefaultPermissionsAsync();
            return Ok("Default permissions initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing default permissions");
            return StatusCode(500, "An error occurred while initializing permissions");
        }
    }
}
