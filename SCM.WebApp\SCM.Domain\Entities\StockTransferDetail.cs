using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class StockTransferDetail : BaseEntity
{
    public int StockTransferHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? CostPrice { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual StockTransferHeader StockTransferHeader { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    public virtual Batch? Batch { get; set; }
    public virtual Unit? Unit { get; set; }
}
