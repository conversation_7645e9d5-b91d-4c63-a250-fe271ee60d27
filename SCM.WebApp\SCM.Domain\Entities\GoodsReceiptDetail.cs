using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class GoodsReceiptDetail : BaseEntity
{
    public int GoodsReceiptHeaderId { get; set; }
    public int ProductId { get; set; }
    public int? PurchaseOrderDetailId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal OrderedQuantity { get; set; }
    public decimal ReceivedQuantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual GoodsReceiptHeader GoodsReceiptHeader { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    public virtual PurchaseOrderDetail? PurchaseOrderDetail { get; set; }
    public virtual Batch? Batch { get; set; }
    public virtual Unit? Unit { get; set; }
}
