.business-tree-container {
  height: 100%;
  overflow: auto;
  position: relative;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.business-tree {
  margin-left: -20px;
}

.mat-tree-node {
  min-height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  
  &.active {
    background-color: rgba(63, 81, 181, 0.1);
    font-weight: 500;
  }
  
  &.inactive {
    color: rgba(0, 0, 0, 0.38);
    
    .node-icon {
      color: rgba(0, 0, 0, 0.38);
    }
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 4px 0;
}

.node-icon {
  margin-right: 8px;
  color: #3f51b5;
}

.node-label {
  flex: 1;
}

.node-spinner {
  margin-left: 8px;
}

.tree-invisible {
  display: none;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: rgba(0, 0, 0, 0.54);
}
