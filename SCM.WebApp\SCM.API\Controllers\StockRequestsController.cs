using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class StockRequestsController : ApiControllerBase
{
    private readonly IStockRequestService _stockRequestService;

    public StockRequestsController(IStockRequestService stockRequestService)
    {
        _stockRequestService = stockRequestService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<StockRequestHeaderDto>>> GetAll()
    {
        var stockRequests = await _stockRequestService.GetAllStockRequestsAsync();
        return Ok(stockRequests);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<StockRequestHeaderDto>> GetById(int id)
    {
        var stockRequest = await _stockRequestService.GetStockRequestByIdAsync(id);
        if (stockRequest == null)
            return NotFound();

        return Ok(stockRequest);
    }

    [HttpGet("from-cost-center/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockRequestHeaderDto>>> GetByFromCostCenterId(int costCenterId)
    {
        var stockRequests = await _stockRequestService.GetStockRequestsByFromCostCenterIdAsync(costCenterId);
        return Ok(stockRequests);
    }

    [HttpGet("to-cost-center/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockRequestHeaderDto>>> GetByToCostCenterId(int costCenterId)
    {
        var stockRequests = await _stockRequestService.GetStockRequestsByToCostCenterIdAsync(costCenterId);
        return Ok(stockRequests);
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<StockRequestHeaderDto>>> GetByStatus(string status)
    {
        var stockRequests = await _stockRequestService.GetStockRequestsByStatusAsync(status);
        return Ok(stockRequests);
    }

    [HttpPost]
    public async Task<ActionResult<StockRequestHeaderDto>> Create(CreateStockRequestHeaderDto createStockRequestHeaderDto)
    {
        var stockRequest = await _stockRequestService.CreateStockRequestAsync(createStockRequestHeaderDto);
        return CreatedAtAction(nameof(GetById), new { id = stockRequest.Id }, stockRequest);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateStockRequestHeaderDto updateStockRequestHeaderDto)
    {
        if (id != updateStockRequestHeaderDto.Id)
            return BadRequest();

        try
        {
            await _stockRequestService.UpdateStockRequestAsync(updateStockRequestHeaderDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _stockRequestService.DeleteStockRequestAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/submit")]
    public async Task<IActionResult> Submit(int id, SubmitStockRequestDto submitStockRequestDto)
    {
        if (id != submitStockRequestDto.Id)
            return BadRequest();

        try
        {
            await _stockRequestService.SubmitStockRequestAsync(submitStockRequestDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/approve")]
    public async Task<IActionResult> Approve(int id, ApproveStockRequestDto approveStockRequestDto)
    {
        if (id != approveStockRequestDto.Id)
            return BadRequest();

        try
        {
            await _stockRequestService.ApproveStockRequestAsync(approveStockRequestDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/reject")]
    public async Task<IActionResult> Reject(int id, RejectStockRequestDto rejectStockRequestDto)
    {
        if (id != rejectStockRequestDto.Id)
            return BadRequest();

        try
        {
            await _stockRequestService.RejectStockRequestAsync(rejectStockRequestDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/complete")]
    public async Task<IActionResult> Complete(int id, CompleteStockRequestDto completeStockRequestDto)
    {
        if (id != completeStockRequestDto.Id)
            return BadRequest();

        try
        {
            await _stockRequestService.CompleteStockRequestAsync(completeStockRequestDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/cancel")]
    public async Task<IActionResult> Cancel(int id)
    {
        try
        {
            await _stockRequestService.CancelStockRequestAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpGet("{stockRequestHeaderId}/details")]
    public async Task<ActionResult<IEnumerable<StockRequestDetailDto>>> GetDetails(int stockRequestHeaderId)
    {
        var details = await _stockRequestService.GetStockRequestDetailsAsync(stockRequestHeaderId);
        return Ok(details);
    }

    [HttpGet("details/{id}")]
    public async Task<ActionResult<StockRequestDetailDto>> GetDetailById(int id)
    {
        var detail = await _stockRequestService.GetStockRequestDetailByIdAsync(id);
        if (detail == null)
            return NotFound();

        return Ok(detail);
    }

    [HttpPost("{stockRequestHeaderId}/details")]
    public async Task<ActionResult<StockRequestDetailDto>> CreateDetail(int stockRequestHeaderId, CreateStockRequestDetailDto createStockRequestDetailDto)
    {
        try
        {
            var detail = await _stockRequestService.CreateStockRequestDetailAsync(stockRequestHeaderId, createStockRequestDetailDto);
            return CreatedAtAction(nameof(GetDetailById), new { id = detail.Id }, detail);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("details/{id}")]
    public async Task<IActionResult> UpdateDetail(int id, UpdateStockRequestDetailDto updateStockRequestDetailDto)
    {
        if (id != updateStockRequestDetailDto.Id)
            return BadRequest();

        try
        {
            await _stockRequestService.UpdateStockRequestDetailAsync(updateStockRequestDetailDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("details/{id}")]
    public async Task<IActionResult> DeleteDetail(int id)
    {
        try
        {
            await _stockRequestService.DeleteStockRequestDetailAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
