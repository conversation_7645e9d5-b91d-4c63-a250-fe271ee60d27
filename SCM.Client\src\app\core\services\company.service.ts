import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Company, CompanyList, CreateCompany, UpdateCompany } from '../models/company.model';

@Injectable({
  providedIn: 'root'
})
export class CompanyService {
  private readonly path = 'companies';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<CompanyList[]> {
    return this.apiService.get<CompanyList[]>(this.path);
  }

  getById(id: number): Observable<Company> {
    return this.apiService.get<Company>(`${this.path}/${id}`);
  }

  create(company: CreateCompany): Observable<Company> {
    return this.apiService.post<Company, CreateCompany>(this.path, company);
  }

  update(id: number, company: UpdateCompany): Observable<void> {
    return this.apiService.put<void, UpdateCompany>(`${this.path}/${id}`, company);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  activate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/activate`, {});
  }

  deactivate(id: number): Observable<void> {
    return this.apiService.put<void, any>(`${this.path}/${id}/deactivate`, {});
  }
}
