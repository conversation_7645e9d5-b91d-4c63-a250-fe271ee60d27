import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, RouterOutlet, Router } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatTreeModule } from '@angular/material/tree';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { AuthService, User } from '../../core/services/auth.service';
import { BusinessStructureService } from '../../core/services/business-structure.service';
import { Subscription } from 'rxjs';

interface NavigationNode {
  name: string;
  icon?: string;
  children?: NavigationNode[];
  path?: string;
}

interface FlatNode {
  expandable: boolean;
  name: string;
  level: number;
  icon?: string;
  path?: string;
}

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    RouterOutlet,
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatTreeModule,
    MatTabsModule,
    MatDividerModule
  ],
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss']
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  private _transformer = (node: NavigationNode, level: number): FlatNode => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      level: level,
      icon: node.icon,
      path: node.path
    };
  };

  treeControl = new FlatTreeControl<FlatNode>(
    node => node.level,
    node => node.expandable
  );

  treeFlattener = new MatTreeFlattener(
    this._transformer,
    node => node.level,
    node => node.expandable,
    node => node.children
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  navLinks = [
    { path: '/', label: 'Dashboard' },
    { path: '/products', label: 'Products' },
    { path: '/inventory', label: 'Inventory' },
    { path: '/transactions', label: 'Transactions' },
    { path: '/recipes', label: 'Recipes' },
    { path: '/users', label: 'Users', roles: ['Admin'] },
    { path: '/configuration', label: 'Configuration' }
  ];

  currentUser: User | null = null;
  private userSubscription: Subscription | null = null;

  businessStructure: NavigationNode[] = [];
  applicationMenu: NavigationNode[] = [];

  constructor(
    private authService: AuthService,
    private router: Router,
    private businessStructureService: BusinessStructureService
  ) {
    // Initialize application menu
    this.applicationMenu = [
      {
        name: 'Dashboard',
        icon: 'dashboard',
        path: '/'
      },
      {
        name: 'Products',
        icon: 'inventory_2',
        children: [
          { name: 'Product List', icon: 'list', path: '/products' },
          { name: 'Add Product', icon: 'add', path: '/products/new' },
          { name: 'Categories', icon: 'category', path: '/products/categories' },
          { name: 'Cost Center Links', icon: 'link', path: '/products/cost-center-links' }
        ]
      },
      {
        name: 'Transactions',
        icon: 'receipt_long',
        children: [
          { name: 'Product Requests', icon: 'assignment', path: '/transactions/product-requests' },
          { name: 'Purchase Orders', icon: 'shopping_cart', path: '/transactions/purchase-orders' },
          { name: 'Goods Receiving', icon: 'local_shipping', path: '/transactions/receiving' },
          { name: 'Credit Notes', icon: 'assignment_return', path: '/transactions/credit-notes' },
          { name: 'Sales', icon: 'point_of_sale', path: '/transactions/sales' },
          { name: 'Suppliers', icon: 'business', path: '/procurement/suppliers' }
        ]
      },
      {
        name: 'Inventory',
        icon: 'inventory',
        children: [
          { name: 'Stock Requests', icon: 'assignment', path: '/inventory/stock-requests' },
          { name: 'Stock Transfers', icon: 'compare_arrows', path: '/inventory/stock-transfers' },
          { name: 'Stock Adjustments', icon: 'playlist_add_check', path: '/inventory/stock-adjustments' },
          { name: 'Stock Taking', icon: 'fact_check', path: '/inventory/stock-taking' }
        ]
      },
      {
        name: 'Recipes',
        icon: 'menu_book',
        children: [
          { name: 'Recipe List', icon: 'list', path: '/recipes' },
          { name: 'Add Recipe', icon: 'add', path: '/recipes/new' }
        ]
      },
      {
        name: 'Users',
        icon: 'people',
        children: [
          { name: 'User List', icon: 'list', path: '/users' },
          { name: 'Add User', icon: 'person_add', path: '/users/new' },
          { name: 'Role Management', icon: 'admin_panel_settings', path: '/users/roles' }
        ]
      },
      {
        name: 'Configuration',
        icon: 'settings',
        children: [
          { name: 'Companies', icon: 'business', path: '/companies' },
          { name: 'Locations', icon: 'location_on', path: '/locations' },
          { name: 'Stores', icon: 'storefront', path: '/configuration/stores' },
          { name: 'Store Configuration', icon: 'store', path: '/configuration/store-config' },
          { name: 'Departments', icon: 'category', path: '/configuration/departments' },
          { name: 'Cost Centers', icon: 'attach_money', path: '/configuration/cost-centers' },
          { name: 'Unit Groups', icon: 'category', path: '/configuration/unit-groups' },
          { name: 'Units', icon: 'straighten', path: '/configuration/units' },
          { name: 'User Permissions', icon: 'security', path: '/configuration/permissions' }
        ]
      }
    ];

    // Load business structure
    this.loadBusinessStructure();
  }

  private loadBusinessStructure(): void {
    this.businessStructureService.getBusinessStructure().subscribe({
      next: (structure) => {
        // Transform the business structure into navigation nodes
        this.businessStructure = this.transformBusinessStructure(structure);

        // Combine business structure with application menu
        this.updateNavigationTree();
      },
      error: (error) => {
        console.error('Error loading business structure:', error);
        // If there's an error, just use the application menu
        this.updateNavigationTree();
      }
    });
  }

  private transformBusinessStructure(structure: any[]): NavigationNode[] {
    return structure.map(unit => ({
      name: unit.name,
      icon: this.getNodeIcon(unit.type || 'root'),
      children: unit.children?.map((location: any) => ({
        name: location.name,
        icon: this.getNodeIcon(location.type || 'company'),
        path: location.type === 'company' ? `/companies/${location.id}` : undefined,
        children: location.children?.map((store: any) => ({
          name: store.name,
          icon: this.getNodeIcon(store.type || 'location'),
          path: store.type === 'location' ? `/locations/${store.id}` : undefined,
          children: store.children?.map((costCenter: any) => ({
            name: costCenter.name,
            icon: this.getNodeIcon(costCenter.type || 'store'),
            path: costCenter.type === 'store' ? `/stores/${costCenter.id}` :
                  costCenter.type === 'costCenter' ? `/cost-centers/${costCenter.id}` : undefined,
            children: costCenter.children?.map((item: any) => ({
              name: item.name,
              icon: this.getNodeIcon(item.type || 'costCenter'),
              path: item.type === 'costCenter' ? `/cost-centers/${item.id}` : undefined
            }))
          }))
        }))
      }))
    }));
  }

  private getNodeIcon(nodeType: string): string {
    switch (nodeType) {
      case 'root': return 'account_tree';
      case 'company': return 'business';
      case 'location': return 'location_on';
      case 'store': return 'store';
      case 'costCenter': return 'account_balance';
      default: return 'folder';
    }
  }

  private updateNavigationTree(): void {
    // Combine business structure with application menu
    this.dataSource.data = [
      ...this.businessStructure,
      ...this.applicationMenu
    ];
  }

  ngOnInit(): void {
    // Initialize the navigation tree
    this.updateNavigationTree();

    // Subscribe to user changes
    this.userSubscription = this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.updateNavigationBasedOnRole();

      // Expand the first level nodes by default after data is loaded
      setTimeout(() => {
        this.treeControl.dataNodes.forEach(node => {
          if (node.level === 0) {
            this.treeControl.expand(node);
          }
        });
      }, 100);
    });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  updateNavigationBasedOnRole(): void {
    // Filter navLinks based on user role
    this.navLinks = this.navLinks.filter(link => {
      if (!link.roles) return true;
      return this.currentUser && link.roles.some(role =>
        this.authService.hasRole(role)
      );
    });
  }

  hasChild = (_: number, node: FlatNode) => node.expandable;

  logout(): void {
    this.authService.logout();
  }

  get userDisplayName(): string {
    return this.currentUser?.fullName || this.currentUser?.username || 'User';
  }

  navigateTo(path: string | undefined): void {
    if (path) {
      this.router.navigate([path]);
    }
  }
}
