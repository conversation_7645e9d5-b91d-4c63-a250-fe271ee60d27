{"Version": 1, "WorkspaceRootPath": "E:\\Blocx\\SCM.WebApp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|e:\\blocx\\scm.webapp\\scm.api\\controllers\\receivingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|solutionrelative:scm.api\\controllers\\receivingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|e:\\blocx\\scm.webapp\\scm.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|solutionrelative:scm.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|e:\\blocx\\scm.webapp\\scm.api\\controllers\\userpermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|solutionrelative:scm.api\\controllers\\userpermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4D8C9EB4-1155-4587-AB53-09AB384C64DB}|SCM.Application\\SCM.Application.csproj|e:\\blocx\\scm.webapp\\scm.application\\services\\permissionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4D8C9EB4-1155-4587-AB53-09AB384C64DB}|SCM.Application\\SCM.Application.csproj|solutionrelative:scm.application\\services\\permissionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|e:\\blocx\\scm.webapp\\scm.api\\controllers\\permissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{99D38486-17A9-4262-9D04-E70B0D5EF1FC}|SCM.API\\SCM.API.csproj|solutionrelative:scm.api\\controllers\\permissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "UserPermissionsController.cs", "DocumentMoniker": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Controllers\\UserPermissionsController.cs", "RelativeDocumentMoniker": "SCM.API\\Controllers\\UserPermissionsController.cs", "ToolTip": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Controllers\\UserPermissionsController.cs", "RelativeToolTip": "SCM.API\\Controllers\\UserPermissionsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T09:29:09.786Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "PermissionService.cs", "DocumentMoniker": "E:\\Blocx\\SCM.WebApp\\SCM.Application\\Services\\PermissionService.cs", "RelativeDocumentMoniker": "SCM.Application\\Services\\PermissionService.cs", "ToolTip": "E:\\Blocx\\SCM.WebApp\\SCM.Application\\Services\\PermissionService.cs", "RelativeToolTip": "SCM.Application\\Services\\PermissionService.cs", "ViewState": "AgIAABAAAAAAAAAAAAAQwBYAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T07:38:55.084Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "PermissionsController.cs", "DocumentMoniker": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Controllers\\PermissionsController.cs", "RelativeDocumentMoniker": "SCM.API\\Controllers\\PermissionsController.cs", "ToolTip": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Controllers\\PermissionsController.cs", "RelativeToolTip": "SCM.API\\Controllers\\PermissionsController.cs", "ViewState": "AgIAABcAAAAAAAAAAAAiwCMAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T07:05:07.307Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ReceivingController.cs", "DocumentMoniker": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Controllers\\ReceivingController.cs", "RelativeDocumentMoniker": "SCM.API\\Controllers\\ReceivingController.cs", "ToolTip": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Controllers\\ReceivingController.cs", "RelativeToolTip": "SCM.API\\Controllers\\ReceivingController.cs", "ViewState": "AgIAAC4BAAAAAAAAAAAYwDcBAABhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T06:56:25.746Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Program.cs", "RelativeDocumentMoniker": "SCM.API\\Program.cs", "ToolTip": "E:\\Blocx\\SCM.WebApp\\SCM.API\\Program.cs", "RelativeToolTip": "SCM.API\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T06:47:56.698Z", "EditorCaption": ""}]}]}]}