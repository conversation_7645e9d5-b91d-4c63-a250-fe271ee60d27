export interface Company {
  id: number;
  name: string;
  code?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  taxNumber?: string;
  logoUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  locations?: Location[];
}

export interface CompanyList {
  id: number;
  name: string;
  code?: string;
  city?: string;
  country?: string;
  phone?: string;
  isActive: boolean;
  locationsCount: number;
}

export interface CreateCompany {
  name: string;
  code?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  taxNumber?: string;
  logoUrl?: string;
}

export interface UpdateCompany {
  id: number;
  name: string;
  code?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  taxNumber?: string;
  logoUrl?: string;
  isActive: boolean;
}
