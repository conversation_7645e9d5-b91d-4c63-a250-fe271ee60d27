using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class ProductRequestsController : ApiControllerBase
{
    private readonly IProductRequestService _productRequestService;

    public ProductRequestsController(IProductRequestService productRequestService)
    {
        _productRequestService = productRequestService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProductRequestListItem>>> GetAll()
    {
        var productRequests = await _productRequestService.GetAllAsync();
        return Ok(productRequests);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductRequestHeader>> GetById(int id)
    {
        var productRequest = await _productRequestService.GetByIdAsync(id);
        if (productRequest == null)
            return NotFound();

        return Ok(productRequest);
    }

    [HttpGet("costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<ProductRequestListItem>>> GetByCostCenter(int costCenterId)
    {
        var productRequests = await _productRequestService.GetByCostCenterAsync(costCenterId);
        return Ok(productRequests);
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<ProductRequestListItem>>> GetByStatus(string status)
    {
        var productRequests = await _productRequestService.GetByStatusAsync(status);
        return Ok(productRequests);
    }

    [HttpPost]
    public async Task<ActionResult<ProductRequestHeader>> Create(CreateProductRequestHeader createProductRequest)
    {
        try
        {
            var productRequest = await _productRequestService.CreateAsync(createProductRequest);
            return CreatedAtAction(nameof(GetById), new { id = productRequest.Id }, productRequest);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateProductRequestHeader updateProductRequest)
    {
        if (id != updateProductRequest.Id)
            return BadRequest();

        try
        {
            await _productRequestService.UpdateAsync(id, updateProductRequest);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _productRequestService.DeleteAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("submit")]
    public async Task<IActionResult> Submit(SubmitProductRequestDto submitDto)
    {
        try
        {
            await _productRequestService.SubmitAsync(submitDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("approve")]
    public async Task<IActionResult> Approve(ApproveProductRequestDto approveDto)
    {
        try
        {
            await _productRequestService.ApproveAsync(approveDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("reject")]
    public async Task<IActionResult> Reject(RejectProductRequestDto rejectDto)
    {
        try
        {
            await _productRequestService.RejectAsync(rejectDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("complete")]
    public async Task<IActionResult> Complete(CompleteProductRequestDto completeDto)
    {
        try
        {
            await _productRequestService.CompleteAsync(completeDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("cancel/{id}")]
    public async Task<IActionResult> Cancel(int id)
    {
        try
        {
            await _productRequestService.CancelAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpGet("{productRequestId}/details")]
    public async Task<ActionResult<IEnumerable<ProductRequestDetail>>> GetDetails(int productRequestId)
    {
        var details = await _productRequestService.GetDetailsAsync(productRequestId);
        return Ok(details);
    }

    [HttpPost("{productRequestId}/details")]
    public async Task<ActionResult<ProductRequestDetail>> AddDetail(int productRequestId, CreateProductRequestDetail createDetail)
    {
        try
        {
            var detail = await _productRequestService.AddDetailAsync(productRequestId, createDetail);
            return Ok(detail);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("details/{id}")]
    public async Task<IActionResult> UpdateDetail(int id, UpdateProductRequestDetail updateDetail)
    {
        if (id != updateDetail.Id)
            return BadRequest();

        try
        {
            await _productRequestService.UpdateDetailAsync(id, updateDetail);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("details/{id}")]
    public async Task<IActionResult> DeleteDetail(int id)
    {
        try
        {
            await _productRequestService.DeleteDetailAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
