<div class="page-container">
  <div class="page-header">
    <h1>
      <ng-container *ngIf="!purchaseOrderId">New Purchase Order</ng-container>
      <ng-container *ngIf="purchaseOrderId && isViewMode">
        Purchase Order: {{purchaseOrder?.documentNumber}}
        <span *ngIf="purchaseOrder?.status" class="status-chip" [ngClass]="'status-' + purchaseOrder?.status?.toLowerCase()">
          {{purchaseOrder?.status}}
        </span>
      </ng-container>
      <ng-container *ngIf="purchaseOrderId && isEditMode">Edit Purchase Order: {{purchaseOrder?.documentNumber}}</ng-container>
    </h1>
    <div class="header-actions">
      <button mat-button (click)="cancel()">Cancel</button>
      <ng-container *ngIf="!isViewMode">
        <button mat-raised-button color="primary" (click)="savePurchaseOrder()" [disabled]="isLoading">
          <mat-icon *ngIf="isLoading">sync</mat-icon>
          <span *ngIf="!isLoading">Save</span>
        </button>
      </ng-container>
      <ng-container *ngIf="isViewMode && purchaseOrder?.status === 'Draft'">
        <button mat-raised-button color="primary" (click)="submitPurchaseOrder()" [disabled]="isLoading">
          <mat-icon>send</mat-icon>
          Submit
        </button>
        <button mat-raised-button color="warn" (click)="cancelPurchaseOrder()" [disabled]="isLoading">
          <mat-icon>cancel</mat-icon>
          Cancel
        </button>
      </ng-container>
      <ng-container *ngIf="isViewMode && purchaseOrder?.status === 'Pending'">
        <button mat-raised-button color="primary" (click)="approvePurchaseOrder()" [disabled]="isLoading">
          <mat-icon>check</mat-icon>
          Approve
        </button>
        <button mat-raised-button color="warn" (click)="rejectPurchaseOrder()" [disabled]="isLoading">
          <mat-icon>close</mat-icon>
          Reject
        </button>
      </ng-container>
      <ng-container *ngIf="isViewMode && purchaseOrder?.status === 'Approved'">
        <button mat-raised-button color="warn" (click)="cancelPurchaseOrder()" [disabled]="isLoading">
          <mat-icon>cancel</mat-icon>
          Cancel
        </button>
      </ng-container>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- View Mode Display -->
  <div *ngIf="!isLoading && isViewMode && purchaseOrder">
    <mat-card>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-row">
            <div class="info-label">Supplier:</div>
            <div class="info-value">{{purchaseOrder.supplierName}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Cost Center:</div>
            <div class="info-value">{{purchaseOrder.costCenterName}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Order Date:</div>
            <div class="info-value">{{purchaseOrder.orderDate | date:'mediumDate'}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Expected Delivery Date:</div>
            <div class="info-value">{{purchaseOrder.expectedDeliveryDate | date:'mediumDate'}}</div>
          </div>
          <div class="info-row" *ngIf="purchaseOrder.notes">
            <div class="info-label">Notes:</div>
            <div class="info-value notes-text">{{purchaseOrder.notes}}</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Edit Mode Form -->
  <form [formGroup]="purchaseOrderForm" *ngIf="!isLoading && !isViewMode">
    <mat-card>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Supplier</mat-label>
            <mat-select formControlName="supplierId">
              <mat-option *ngFor="let supplier of suppliers" [value]="supplier.id">
                {{supplier.name}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="purchaseOrderForm.get('supplierId')?.hasError('required')">
              Supplier is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Cost Center</mat-label>
            <mat-select formControlName="costCenterId">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{costCenter.name}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="purchaseOrderForm.get('costCenterId')?.hasError('required')">
              Cost Center is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Order Date</mat-label>
            <input matInput [matDatepicker]="orderDatePicker" formControlName="orderDate">
            <mat-datepicker-toggle matSuffix [for]="orderDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #orderDatePicker></mat-datepicker>
            <mat-error *ngIf="purchaseOrderForm.get('orderDate')?.hasError('required')">
              Order Date is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Expected Delivery Date</mat-label>
            <input matInput [matDatepicker]="deliveryDatePicker" formControlName="expectedDeliveryDate">
            <mat-datepicker-toggle matSuffix [for]="deliveryDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #deliveryDatePicker></mat-datepicker>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes</mat-label>
            <textarea matInput formControlName="notes" rows="3"></textarea>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Edit Mode Order Items -->
    <div *ngIf="!isViewMode">
      <div class="section-header">
        <h2>Order Items</h2>
        <button mat-mini-fab color="primary" (click)="addDetail()">
          <mat-icon>add</mat-icon>
        </button>
      </div>

      <div class="table-container mat-elevation-z2">
        <div *ngIf="details.length === 0" class="no-data-message">
          No items added to this purchase order.
        </div>

        <table mat-table [dataSource]="details.controls" *ngIf="details.length > 0">
          <!-- Product Column -->
          <ng-container matColumnDef="productName">
            <th mat-header-cell *matHeaderCellDef>Product</th>
            <td mat-cell *matCellDef="let detail; let i = index">
              <mat-form-field appearance="outline">
                <mat-select [formControl]="detail.get('productId')" (selectionChange)="onProductSelectionChange($event, i)">
                  <mat-option *ngFor="let product of products" [value]="product.id">
                    {{product.name}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="detail.get('productId')?.hasError('required')">
                  Product is required
                </mat-error>
              </mat-form-field>
            </td>
          </ng-container>

          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef>Quantity</th>
            <td mat-cell *matCellDef="let detail; let i = index">
              <mat-form-field appearance="outline">
                <input matInput type="number" [formControl]="detail.get('quantity')" min="1">
                <mat-error *ngIf="detail.get('quantity')?.hasError('required')">
                  Quantity is required
                </mat-error>
                <mat-error *ngIf="detail.get('quantity')?.hasError('min')">
                  Quantity must be at least 1
                </mat-error>
              </mat-form-field>
            </td>
          </ng-container>

          <!-- Unit Price Column -->
          <ng-container matColumnDef="unitPrice">
            <th mat-header-cell *matHeaderCellDef>Unit Price</th>
            <td mat-cell *matCellDef="let detail; let i = index">
              <mat-form-field appearance="outline">
                <input matInput type="number" [formControl]="detail.get('unitPrice')" min="0" step="0.01">
                <span matPrefix>$&nbsp;</span>
                <mat-error *ngIf="detail.get('unitPrice')?.hasError('required')">
                  Unit Price is required
                </mat-error>
                <mat-error *ngIf="detail.get('unitPrice')?.hasError('min')">
                  Unit Price must be at least 0
                </mat-error>
              </mat-form-field>
            </td>
          </ng-container>

          <!-- Total Price Column -->
          <ng-container matColumnDef="totalPrice">
            <th mat-header-cell *matHeaderCellDef>Total Price</th>
            <td mat-cell *matCellDef="let detail; let i = index">
              {{calculateTotalPrice(i) | currency}}
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let detail; let i = index">
              <button mat-icon-button color="warn" (click)="removeDetail(i)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>

      <div class="order-total" *ngIf="details.length > 0">
        <span>Total:</span>
        <span class="total-amount">{{calculateOrderTotal() | currency}}</span>
      </div>
    </div>

    <!-- View Mode Order Items -->
    <div *ngIf="isViewMode && purchaseOrder">
      <div class="section-header">
        <h2>Order Items</h2>
      </div>

      <div class="table-container mat-elevation-z2">
        <div *ngIf="!purchaseOrder.details || purchaseOrder.details.length === 0" class="no-data-message">
          No items added to this purchase order.
        </div>

        <table mat-table [dataSource]="purchaseOrder.details" *ngIf="purchaseOrder.details && purchaseOrder.details.length > 0">
          <!-- Product Column -->
          <ng-container matColumnDef="productName">
            <th mat-header-cell *matHeaderCellDef>Product</th>
            <td mat-cell *matCellDef="let detail">{{detail.productName}}</td>
          </ng-container>

          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef>Quantity</th>
            <td mat-cell *matCellDef="let detail">{{detail.quantity}}</td>
          </ng-container>

          <!-- Unit Price Column -->
          <ng-container matColumnDef="unitPrice">
            <th mat-header-cell *matHeaderCellDef>Unit Price</th>
            <td mat-cell *matCellDef="let detail">{{detail.unitPrice | currency}}</td>
          </ng-container>

          <!-- Total Price Column -->
          <ng-container matColumnDef="totalPrice">
            <th mat-header-cell *matHeaderCellDef>Total Price</th>
            <td mat-cell *matCellDef="let detail">{{detail.totalPrice | currency}}</td>
          </ng-container>

          <!-- Actions Column (empty in view mode) -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let detail"></td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>

      <div class="order-total" *ngIf="purchaseOrder.details && purchaseOrder.details.length > 0">
        <span>Total:</span>
        <span class="total-amount">{{purchaseOrder.totalAmount | currency}}</span>
      </div>
    </div>

    <div class="form-actions" *ngIf="!isViewMode">
      <button mat-button (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" (click)="savePurchaseOrder()" [disabled]="isLoading">
        <mat-icon *ngIf="isLoading">sync</mat-icon>
        <span *ngIf="!isLoading">Save</span>
      </button>
    </div>
  </form>
</div>
