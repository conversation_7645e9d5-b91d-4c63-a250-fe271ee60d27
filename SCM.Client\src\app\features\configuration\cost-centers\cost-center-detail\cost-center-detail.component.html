<div class="container">
  <div class="loading-shade" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>
  
  <div *ngIf="costCenter">
    <mat-card>
      <mat-card-header>
        <div mat-card-avatar>
          <mat-icon color="primary">account_balance</mat-icon>
        </div>
        <mat-card-title>{{ costCenter.name }}</mat-card-title>
        <mat-card-subtitle *ngIf="costCenter.code">Code: {{ costCenter.code }}</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <mat-list>
          <mat-list-item>
            <span matListItemTitle>Store</span>
            <span matListItemLine>{{ costCenter.storeName }}</span>
          </mat-list-item>
          
          <mat-divider></mat-divider>
          
          <mat-list-item *ngIf="costCenter.locationName">
            <span matListItemTitle>Location</span>
            <span matListItemLine>{{ costCenter.locationName }}</span>
          </mat-list-item>
          
          <mat-divider *ngIf="costCenter.locationName"></mat-divider>
          
          <mat-list-item *ngIf="costCenter.companyName">
            <span matListItemTitle>Company</span>
            <span matListItemLine>{{ costCenter.companyName }}</span>
          </mat-list-item>
          
          <mat-divider *ngIf="costCenter.companyName"></mat-divider>
          
          <mat-list-item *ngIf="costCenter.typeName">
            <span matListItemTitle>Type</span>
            <span matListItemLine>{{ costCenter.typeName }}</span>
          </mat-list-item>
          
          <mat-divider *ngIf="costCenter.typeName"></mat-divider>
          
          <mat-list-item *ngIf="costCenter.description">
            <span matListItemTitle>Description</span>
            <span matListItemLine>{{ costCenter.description }}</span>
          </mat-list-item>
          
          <mat-divider *ngIf="costCenter.description"></mat-divider>
          
          <mat-list-item>
            <span matListItemTitle>Auto Transfer</span>
            <span matListItemLine>{{ costCenter.autoTransfer ? 'Yes' : 'No' }}</span>
          </mat-list-item>
          
          <mat-divider></mat-divider>
          
          <mat-list-item>
            <span matListItemTitle>Sales Point</span>
            <span matListItemLine>{{ costCenter.isSalesPoint ? 'Yes' : 'No' }}</span>
          </mat-list-item>
          
          <mat-divider></mat-divider>
          
          <mat-list-item *ngIf="costCenter.abbreviation">
            <span matListItemTitle>Abbreviation</span>
            <span matListItemLine>{{ costCenter.abbreviation }}</span>
          </mat-list-item>
          
          <mat-divider *ngIf="costCenter.abbreviation"></mat-divider>
          
          <mat-list-item>
            <span matListItemTitle>Status</span>
            <span matListItemLine>{{ costCenter.isActive ? 'Active' : 'Inactive' }}</span>
          </mat-list-item>
          
          <mat-divider></mat-divider>
          
          <mat-list-item>
            <span matListItemTitle>Created At</span>
            <span matListItemLine>{{ costCenter.createdAt | date:'medium' }}</span>
          </mat-list-item>
          
          <mat-divider></mat-divider>
          
          <mat-list-item *ngIf="costCenter.updatedAt">
            <span matListItemTitle>Last Updated</span>
            <span matListItemLine>{{ costCenter.updatedAt | date:'medium' }}</span>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
      
      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="editCostCenter()">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
