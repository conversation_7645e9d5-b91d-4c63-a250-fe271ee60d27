<div class="container">
  <div class="loading-shade" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <div *ngIf="company">
    <mat-card>
      <mat-card-header>
        <div mat-card-avatar>
          <mat-icon color="primary">business</mat-icon>
        </div>
        <mat-card-title>{{ company.name }}</mat-card-title>
        <mat-card-subtitle *ngIf="company.code">Code: {{ company.code }}</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="tab-content">
          <h3>Company Details</h3>
          <mat-list>
            <mat-list-item *ngIf="company.description">
              <span matListItemTitle>Description</span>
              <span matListItemLine>{{ company.description }}</span>
            </mat-list-item>

            <mat-divider *ngIf="company.description"></mat-divider>

            <mat-list-item *ngIf="company.address">
              <span matListItemTitle>Address</span>
              <span matListItemLine>{{ company.address }}</span>
            </mat-list-item>

            <mat-divider *ngIf="company.address"></mat-divider>

            <mat-list-item *ngIf="company.city || company.state || company.country">
              <span matListItemTitle>Location</span>
              <span matListItemLine>
                {{ company.city }}{{ company.city && (company.state || company.country) ? ', ' : '' }}
                {{ company.state }}{{ company.state && company.country ? ', ' : '' }}
                {{ company.country }}
              </span>
            </mat-list-item>

            <mat-divider *ngIf="company.city || company.state || company.country"></mat-divider>

            <mat-list-item *ngIf="company.postalCode">
              <span matListItemTitle>Postal Code</span>
              <span matListItemLine>{{ company.postalCode }}</span>
            </mat-list-item>

            <mat-divider *ngIf="company.postalCode"></mat-divider>

            <mat-list-item *ngIf="company.phone">
              <span matListItemTitle>Phone</span>
              <span matListItemLine>{{ company.phone }}</span>
            </mat-list-item>

            <mat-divider *ngIf="company.phone"></mat-divider>

            <mat-list-item *ngIf="company.email">
              <span matListItemTitle>Email</span>
              <span matListItemLine>{{ company.email }}</span>
            </mat-list-item>

            <mat-divider *ngIf="company.email"></mat-divider>

            <mat-list-item *ngIf="company.website">
              <span matListItemTitle>Website</span>
              <span matListItemLine>{{ company.website }}</span>
            </mat-list-item>

            <mat-divider *ngIf="company.website"></mat-divider>

            <mat-list-item *ngIf="company.taxNumber">
              <span matListItemTitle>Tax Number</span>
              <span matListItemLine>{{ company.taxNumber }}</span>
            </mat-list-item>

            <mat-divider *ngIf="company.taxNumber"></mat-divider>

            <mat-list-item>
              <span matListItemTitle>Status</span>
              <span matListItemLine>{{ company.isActive ? 'Active' : 'Inactive' }}</span>
            </mat-list-item>

            <mat-divider></mat-divider>

            <mat-list-item>
              <span matListItemTitle>Created At</span>
              <span matListItemLine>{{ company.createdAt | date:'medium' }}</span>
            </mat-list-item>

            <mat-divider></mat-divider>

            <mat-list-item *ngIf="company.updatedAt">
              <span matListItemTitle>Last Updated</span>
              <span matListItemLine>{{ company.updatedAt | date:'medium' }}</span>
            </mat-list-item>
          </mat-list>
        </div>

        <div class="tab-content mt-4">
          <h3>Locations</h3>
          <div class="action-bar">
            <button mat-raised-button color="primary" (click)="addLocation()">
              <mat-icon>add</mat-icon> Add Location
            </button>
          </div>

          <div *ngIf="locations.length > 0">
            <table mat-table [dataSource]="locations" class="full-width">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let location">{{ location.name }}</td>
              </ng-container>

              <!-- Code Column -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef>Code</th>
                <td mat-cell *matCellDef="let location">{{ location.code }}</td>
              </ng-container>

              <!-- City Column -->
              <ng-container matColumnDef="city">
                <th mat-header-cell *matHeaderCellDef>City</th>
                <td mat-cell *matCellDef="let location">{{ location.city }}</td>
              </ng-container>

              <!-- Country Column -->
              <ng-container matColumnDef="country">
                <th mat-header-cell *matHeaderCellDef>Country</th>
                <td mat-cell *matCellDef="let location">{{ location.country }}</td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="isActive">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let location">
                  <mat-icon [color]="location.isActive ? 'primary' : 'warn'">
                    {{ location.isActive ? 'check_circle' : 'cancel' }}
                  </mat-icon>
                </td>
              </ng-container>

              <!-- Stores Count Column -->
              <ng-container matColumnDef="storesCount">
                <th mat-header-cell *matHeaderCellDef>Stores</th>
                <td mat-cell *matCellDef="let location">{{ location.storesCount }}</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let location">
                  <button mat-icon-button color="primary" (click)="viewLocation(location.id)" matTooltip="View">
                    <mat-icon>visibility</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="locationColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: locationColumns;"></tr>
            </table>
          </div>

          <div class="no-data-message" *ngIf="locations.length === 0">
            No locations found for this company. Click "Add Location" to create one.
          </div>
        </div>
      </mat-card-content>

      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="editCompany()">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
