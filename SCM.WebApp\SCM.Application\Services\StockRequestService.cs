using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class StockRequestService : IStockRequestService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IStockService _stockService;

    public StockRequestService(
        ApplicationDbContext dbContext,
        IMapper mapper,
        IStockService stockService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _stockService = stockService;
    }

    public async Task<IEnumerable<StockRequestHeaderDto>> GetAllStockRequestsAsync()
    {
        var stockRequests = await _dbContext.StockRequestHeaders
            .Include(sr => sr.FromCostCenter)
            .Include(sr => sr.ToCostCenter)
            .Include(sr => sr.CreatedBy)
            .Include(sr => sr.ApprovedBy)
            .Include(sr => sr.CompletedBy)
            .OrderByDescending(sr => sr.RequestDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockRequestHeaderDto>>(stockRequests);
    }

    public async Task<StockRequestHeaderDto?> GetStockRequestByIdAsync(int id)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .Include(sr => sr.FromCostCenter)
            .Include(sr => sr.ToCostCenter)
            .Include(sr => sr.CreatedBy)
            .Include(sr => sr.ApprovedBy)
            .Include(sr => sr.CompletedBy)
            .Include(sr => sr.Details)
            .FirstOrDefaultAsync(sr => sr.Id == id);

        if (stockRequest == null)
            return null;

        // Load related data for stock request details
        foreach (var detail in stockRequest.Details)
        {
            // Load product information
            if (detail.ProductId > 0)
            {
                detail.Product = await _dbContext.Products.FindAsync(detail.ProductId);
            }

            // Load batch information
            if (detail.BatchId.HasValue)
            {
                detail.Batch = await _dbContext.Batches.FindAsync(detail.BatchId.Value);
            }

            // Load unit information
            if (detail.UnitId.HasValue)
            {
                detail.Unit = await _dbContext.Units.FindAsync(detail.UnitId.Value);
            }
        }

        return _mapper.Map<StockRequestHeaderDto>(stockRequest);
    }

    public async Task<IEnumerable<StockRequestHeaderDto>> GetStockRequestsByFromCostCenterIdAsync(int costCenterId)
    {
        var stockRequests = await _dbContext.StockRequestHeaders
            .Include(sr => sr.FromCostCenter)
            .Include(sr => sr.ToCostCenter)
            .Include(sr => sr.CreatedBy)
            .Include(sr => sr.ApprovedBy)
            .Include(sr => sr.CompletedBy)
            .Where(sr => sr.FromCostCenterId == costCenterId)
            .OrderByDescending(sr => sr.RequestDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockRequestHeaderDto>>(stockRequests);
    }

    public async Task<IEnumerable<StockRequestHeaderDto>> GetStockRequestsByToCostCenterIdAsync(int costCenterId)
    {
        var stockRequests = await _dbContext.StockRequestHeaders
            .Include(sr => sr.FromCostCenter)
            .Include(sr => sr.ToCostCenter)
            .Include(sr => sr.CreatedBy)
            .Include(sr => sr.ApprovedBy)
            .Include(sr => sr.CompletedBy)
            .Where(sr => sr.ToCostCenterId == costCenterId)
            .OrderByDescending(sr => sr.RequestDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockRequestHeaderDto>>(stockRequests);
    }

    public async Task<IEnumerable<StockRequestHeaderDto>> GetStockRequestsByStatusAsync(string status)
    {
        var stockRequests = await _dbContext.StockRequestHeaders
            .Include(sr => sr.FromCostCenter)
            .Include(sr => sr.ToCostCenter)
            .Include(sr => sr.CreatedBy)
            .Include(sr => sr.ApprovedBy)
            .Include(sr => sr.CompletedBy)
            .Where(sr => sr.Status == status)
            .OrderByDescending(sr => sr.RequestDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockRequestHeaderDto>>(stockRequests);
    }

    public async Task<StockRequestHeaderDto> CreateStockRequestAsync(CreateStockRequestHeaderDto createStockRequestHeaderDto)
    {
        // Generate reference number
        var referenceNumber = await GenerateReferenceNumberAsync();
        
        var stockRequest = _mapper.Map<StockRequestHeader>(createStockRequestHeaderDto);
        stockRequest.ReferenceNumber = referenceNumber;
        stockRequest.Status = "Draft";
        stockRequest.CreatedById = 1; // TODO: Get from current user
        
        _dbContext.StockRequestHeaders.Add(stockRequest);
        await _dbContext.SaveChangesAsync();
        
        // Add details if provided
        if (createStockRequestHeaderDto.Details != null && createStockRequestHeaderDto.Details.Any())
        {
            foreach (var detailDto in createStockRequestHeaderDto.Details)
            {
                await CreateStockRequestDetailAsync(stockRequest.Id, detailDto);
            }
        }
        
        var createdStockRequest = await _dbContext.StockRequestHeaders
            .Include(sr => sr.FromCostCenter)
            .Include(sr => sr.ToCostCenter)
            .Include(sr => sr.CreatedBy)
            .Include(sr => sr.Details)
                .ThenInclude(d => d.Product)
            .Include(sr => sr.Details)
                .ThenInclude(d => d.Batch)
            .Include(sr => sr.Details)
                .ThenInclude(d => d.Unit)
            .FirstOrDefaultAsync(sr => sr.Id == stockRequest.Id);
            
        return _mapper.Map<StockRequestHeaderDto>(createdStockRequest);
    }

    public async Task UpdateStockRequestAsync(UpdateStockRequestHeaderDto updateStockRequestHeaderDto)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .FirstOrDefaultAsync(sr => sr.Id == updateStockRequestHeaderDto.Id);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {updateStockRequestHeaderDto.Id} not found.");
            
        if (stockRequest.Status != "Draft")
            throw new InvalidOperationException("Only stock requests in Draft status can be updated.");
            
        _mapper.Map(updateStockRequestHeaderDto, stockRequest);
        
        _dbContext.StockRequestHeaders.Update(stockRequest);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockRequestAsync(int id)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .Include(sr => sr.Details)
            .FirstOrDefaultAsync(sr => sr.Id == id);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {id} not found.");
            
        if (stockRequest.Status != "Draft")
            throw new InvalidOperationException("Only stock requests in Draft status can be deleted.");
            
        // Remove details first
        _dbContext.StockRequestDetails.RemoveRange(stockRequest.Details);
        
        // Then remove header
        _dbContext.StockRequestHeaders.Remove(stockRequest);
        
        await _dbContext.SaveChangesAsync();
    }

    public async Task SubmitStockRequestAsync(SubmitStockRequestDto submitStockRequestDto)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .Include(sr => sr.Details)
            .FirstOrDefaultAsync(sr => sr.Id == submitStockRequestDto.Id);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {submitStockRequestDto.Id} not found.");
            
        if (stockRequest.Status != "Draft")
            throw new InvalidOperationException("Only stock requests in Draft status can be submitted.");
            
        if (!stockRequest.Details.Any())
            throw new InvalidOperationException("Cannot submit a stock request with no details.");
            
        stockRequest.Status = "Submitted";
        stockRequest.Notes = submitStockRequestDto.Notes ?? stockRequest.Notes;
        
        _dbContext.StockRequestHeaders.Update(stockRequest);
        await _dbContext.SaveChangesAsync();
    }

    public async Task ApproveStockRequestAsync(ApproveStockRequestDto approveStockRequestDto)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .FirstOrDefaultAsync(sr => sr.Id == approveStockRequestDto.Id);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {approveStockRequestDto.Id} not found.");
            
        if (stockRequest.Status != "Submitted")
            throw new InvalidOperationException("Only submitted stock requests can be approved.");
            
        stockRequest.Status = "Approved";
        stockRequest.ApprovedById = 1; // TODO: Get from current user
        stockRequest.ApprovedAt = DateTime.UtcNow;
        stockRequest.Notes = approveStockRequestDto.Notes ?? stockRequest.Notes;
        
        _dbContext.StockRequestHeaders.Update(stockRequest);
        await _dbContext.SaveChangesAsync();
    }

    public async Task RejectStockRequestAsync(RejectStockRequestDto rejectStockRequestDto)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .FirstOrDefaultAsync(sr => sr.Id == rejectStockRequestDto.Id);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {rejectStockRequestDto.Id} not found.");
            
        if (stockRequest.Status != "Submitted")
            throw new InvalidOperationException("Only submitted stock requests can be rejected.");
            
        stockRequest.Status = "Rejected";
        stockRequest.Notes = rejectStockRequestDto.Notes ?? stockRequest.Notes;
        
        _dbContext.StockRequestHeaders.Update(stockRequest);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CompleteStockRequestAsync(CompleteStockRequestDto completeStockRequestDto)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .Include(sr => sr.Details)
                .ThenInclude(d => d.Product)
            .Include(sr => sr.Details)
                .ThenInclude(d => d.Batch)
            .FirstOrDefaultAsync(sr => sr.Id == completeStockRequestDto.Id);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {completeStockRequestDto.Id} not found.");
            
        if (stockRequest.Status != "Approved")
            throw new InvalidOperationException("Only approved stock requests can be completed.");
            
        if (!stockRequest.Details.Any())
            throw new InvalidOperationException("Cannot complete a stock request with no details.");
            
        // Process the stock request by creating a stock transfer
        // This would typically involve creating a stock transfer record and adjusting stock levels
        // For now, we'll just update the status
        
        stockRequest.Status = "Completed";
        stockRequest.CompletedById = 1; // TODO: Get from current user
        stockRequest.CompletedAt = DateTime.UtcNow;
        stockRequest.Notes = completeStockRequestDto.Notes ?? stockRequest.Notes;
        
        _dbContext.StockRequestHeaders.Update(stockRequest);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelStockRequestAsync(int id)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .FirstOrDefaultAsync(sr => sr.Id == id);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {id} not found.");
            
        if (stockRequest.Status == "Completed" || stockRequest.Status == "Cancelled")
            throw new InvalidOperationException("Completed or cancelled stock requests cannot be cancelled.");
            
        stockRequest.Status = "Cancelled";
        
        _dbContext.StockRequestHeaders.Update(stockRequest);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<StockRequestDetailDto>> GetStockRequestDetailsAsync(int stockRequestHeaderId)
    {
        var details = await _dbContext.StockRequestDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .Where(d => d.StockRequestHeaderId == stockRequestHeaderId)
            .OrderBy(d => d.Product.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockRequestDetailDto>>(details);
    }

    public async Task<StockRequestDetailDto?> GetStockRequestDetailByIdAsync(int id)
    {
        var detail = await _dbContext.StockRequestDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        return detail != null ? _mapper.Map<StockRequestDetailDto>(detail) : null;
    }

    public async Task<StockRequestDetailDto> CreateStockRequestDetailAsync(int stockRequestHeaderId, CreateStockRequestDetailDto createStockRequestDetailDto)
    {
        var stockRequest = await _dbContext.StockRequestHeaders
            .FirstOrDefaultAsync(sr => sr.Id == stockRequestHeaderId);
            
        if (stockRequest == null)
            throw new KeyNotFoundException($"StockRequest with ID {stockRequestHeaderId} not found.");
            
        if (stockRequest.Status != "Draft")
            throw new InvalidOperationException("Can only add details to stock requests in Draft status.");
            
        // Verify product exists
        var product = await _dbContext.Products.FindAsync(createStockRequestDetailDto.ProductId);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {createStockRequestDetailDto.ProductId} not found.");
            
        var detail = _mapper.Map<StockRequestDetail>(createStockRequestDetailDto);
        detail.StockRequestHeaderId = stockRequestHeaderId;
        
        _dbContext.StockRequestDetails.Add(detail);
        await _dbContext.SaveChangesAsync();
        
        var createdDetail = await _dbContext.StockRequestDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == detail.Id);
            
        return _mapper.Map<StockRequestDetailDto>(createdDetail);
    }

    public async Task UpdateStockRequestDetailAsync(UpdateStockRequestDetailDto updateStockRequestDetailDto)
    {
        var detail = await _dbContext.StockRequestDetails
            .Include(d => d.StockRequestHeader)
            .FirstOrDefaultAsync(d => d.Id == updateStockRequestDetailDto.Id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockRequestDetail with ID {updateStockRequestDetailDto.Id} not found.");
            
        if (detail.StockRequestHeader.Status != "Draft")
            throw new InvalidOperationException("Can only update details of stock requests in Draft status.");
            
        _mapper.Map(updateStockRequestDetailDto, detail);
        
        _dbContext.StockRequestDetails.Update(detail);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockRequestDetailAsync(int id)
    {
        var detail = await _dbContext.StockRequestDetails
            .Include(d => d.StockRequestHeader)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockRequestDetail with ID {id} not found.");
            
        if (detail.StockRequestHeader.Status != "Draft")
            throw new InvalidOperationException("Can only delete details of stock requests in Draft status.");
            
        _dbContext.StockRequestDetails.Remove(detail);
        await _dbContext.SaveChangesAsync();
    }

    private async Task<string> GenerateReferenceNumberAsync()
    {
        // Get the current date
        var today = DateTime.UtcNow;
        var year = today.Year.ToString().Substring(2); // Last 2 digits of year
        var month = today.Month.ToString().PadLeft(2, '0');
        var day = today.Day.ToString().PadLeft(2, '0');
        
        // Get the count of requests for today
        var todayRequests = await _dbContext.StockRequestHeaders
            .CountAsync(sr => sr.CreatedAt.Date == today.Date);
        
        // Generate the reference number
        var sequence = (todayRequests + 1).ToString().PadLeft(3, '0');
        return $"SR-{year}{month}{day}-{sequence}";
    }
}
