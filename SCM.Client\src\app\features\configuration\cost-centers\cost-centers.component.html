<div class="container">
  <!-- Store Filter Notification -->
  <div *ngIf="filteredStoreId" class="filter-notification">
    <div class="filter-message">
      <mat-icon>filter_list</mat-icon>
      <span>Showing cost centers for store: {{ filteredStoreName }}</span>
    </div>
    <button mat-button color="primary" (click)="clearStoreFilter()">
      <mat-icon>clear</mat-icon>
      Clear Filter
    </button>
  </div>

  <div class="row">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ isEditMode ? 'Edit Cost Center' : 'Add New Cost Center' }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="costCenterForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Name</mat-label>
                  <input matInput formControlName="name" placeholder="Enter cost center name">
                  <mat-error *ngIf="costCenterForm.get('name')?.hasError('required')">
                    Name is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Code</mat-label>
                  <input matInput formControlName="code" placeholder="Enter cost center code">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Store</mat-label>
                  <mat-select formControlName="storeId">
                    <mat-option *ngFor="let store of stores" [value]="store.id">
                      {{ store.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="costCenterForm.get('storeId')?.hasError('required')">
                    Store is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Type</mat-label>
                  <mat-select formControlName="typeId">
                    <mat-option [value]="null">None</mat-option>
                    <mat-option *ngFor="let type of costCenterTypes" [value]="type.id">
                      {{ type.name }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Abbreviation</mat-label>
                  <input matInput formControlName="abbreviation" placeholder="Enter abbreviation">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" placeholder="Enter description" rows="3"></textarea>
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-checkbox formControlName="autoTransfer">Auto Transfer</mat-checkbox>
              </div>
              <div class="col-md-6">
                <mat-checkbox formControlName="isSalesPoint">Sales Point</mat-checkbox>
              </div>
            </div>
            
            <div class="button-row">
              <button mat-raised-button color="primary" type="submit" [disabled]="costCenterForm.invalid || isSubmitting">
                <mat-icon>save</mat-icon>
                {{ isEditMode ? 'Update' : 'Save' }}
              </button>
              <button mat-raised-button type="button" (click)="resetForm()" [disabled]="isSubmitting">
                <mat-icon>refresh</mat-icon>
                Reset
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
  
  <div class="row mt-4">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Cost Centers</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="loading-shade" *ngIf="isLoading">
            <mat-spinner diameter="50"></mat-spinner>
          </div>
          
          <div class="table-container">
            <table mat-table [dataSource]="costCenters" matSort (matSortChange)="onSortChange($event)" class="full-width">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.name }}</td>
              </ng-container>
              
              <!-- Code Column -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Code</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.code }}</td>
              </ng-container>
              
              <!-- Store Column -->
              <ng-container matColumnDef="storeName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Store</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.storeName }}</td>
              </ng-container>
              
              <!-- Type Column -->
              <ng-container matColumnDef="typeName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.typeName }}</td>
              </ng-container>
              
              <!-- Sales Point Column -->
              <ng-container matColumnDef="isSalesPoint">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Sales Point</th>
                <td mat-cell *matCellDef="let costCenter">
                  <mat-icon color="primary" *ngIf="costCenter.isSalesPoint">check_circle</mat-icon>
                  <mat-icon color="warn" *ngIf="!costCenter.isSalesPoint">cancel</mat-icon>
                </td>
              </ng-container>
              
              <!-- Status Column -->
              <ng-container matColumnDef="isActive">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                <td mat-cell *matCellDef="let costCenter">
                  <button mat-icon-button [color]="costCenter.isActive ? 'primary' : 'warn'"
                          (click)="toggleStatus(costCenter)" matTooltip="Click to toggle status">
                    <mat-icon>{{ costCenter.isActive ? 'toggle_on' : 'toggle_off' }}</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let costCenter">
                  <button mat-icon-button color="primary" (click)="editCostCenter(costCenter)" matTooltip="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteCostCenter(costCenter.id)" matTooltip="Delete">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            
            <mat-paginator [length]="totalItems"
                          [pageSize]="pageSize"
                          [pageSizeOptions]="[5, 10, 25, 50]"
                          (page)="onPageChange($event)">
            </mat-paginator>
          </div>
          
          <div class="no-data-message" *ngIf="costCenters.length === 0 && !isLoading">
            No cost centers found. Please add a cost center.
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
