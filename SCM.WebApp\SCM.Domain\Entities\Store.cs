using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Store : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public int LocationId { get; set; }
    public bool IsSalesPoint { get; set; } = false;
    public string? LogoPath { get; set; }

    // Navigation properties
    public virtual Location Location { get; set; } = null!;
    public virtual ICollection<CostCenter> CostCenters { get; set; } = new List<CostCenter>();
}
