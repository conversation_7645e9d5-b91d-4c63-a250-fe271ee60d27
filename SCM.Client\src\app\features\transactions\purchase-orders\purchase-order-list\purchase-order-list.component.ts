import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDialogModule } from '@angular/material/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PurchaseOrderService } from '../../../../core/services/purchase-order.service';
import { SupplierService, Supplier } from '../../../../core/services/supplier.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductRequestService } from '../../../../core/services/product-request.service';
import { PurchaseOrderListItem } from '../../../../core/models/purchase-order.model';
import { TransactionHeader } from '../../../../core/models/transaction.model';
import { TransactionAdapter } from '../../../../core/adapters/transaction-adapter';
import { ProductRequestListItem } from '../../../../core/models/product-request.model';
import { ErrorService } from '../../../../core/services/error.service';
import { finalize, catchError, of } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { SelectProductRequestDialogComponent } from '../select-product-request-dialog/select-product-request-dialog.component';

@Component({
  selector: 'app-purchase-order-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatChipsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './purchase-order-list.component.html',
  styleUrls: ['./purchase-order-list.component.scss']
})
export class PurchaseOrderListComponent implements OnInit {
  purchaseOrders: PurchaseOrderListItem[] = [];
  filteredPurchaseOrders: PurchaseOrderListItem[] = [];
  suppliers: Supplier[] = [];
  costCenters: any[] = [];

  displayedColumns: string[] = [
    'documentNumber',
    'supplierName',
    'costCenterName',
    'orderDate',
    'expectedDeliveryDate',
    'status',
    'totalAmount',
    'actions'
  ];

  isLoading = false;
  searchTerm = '';
  selectedSupplier = '';
  selectedCostCenter = '';
  selectedStatus = '';

  statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'Draft', label: 'Draft' },
    { value: 'Pending', label: 'Pending' },
    { value: 'Approved', label: 'Approved' },
    { value: 'Rejected', label: 'Rejected' },
    { value: 'Completed', label: 'Completed' },
    { value: 'Cancelled', label: 'Cancelled' }
  ];

  constructor(
    private purchaseOrderService: PurchaseOrderService,
    private supplierService: SupplierService,
    private costCenterService: CostCenterService,
    private productRequestService: ProductRequestService,
    private errorService: ErrorService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadData();

    // Subscribe to router events to refresh data when navigating back to this component
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd && event.url === '/transactions/purchase-orders') {
        this.loadData();
      }
    });
  }

  loadData(): void {
    this.isLoading = true;

    // Load purchase orders
    this.purchaseOrderService.getAll()
      .pipe(
        finalize(() => this.isLoading = false),
        catchError(error => {
          this.errorService.handleError(error);
          return of([]);
        })
      )
      .subscribe((transactions: TransactionHeader[]) => {
        console.log('Transactions from API:', transactions);

        // Convert TransactionHeader[] to PurchaseOrderListItem[]
        this.purchaseOrders = transactions.map(transaction => {
          const purchaseOrderItem = TransactionAdapter.toPurchaseOrderListItem(transaction);
          console.log('Transaction:', transaction);
          console.log('Mapped to PurchaseOrderListItem:', purchaseOrderItem);
          return purchaseOrderItem;
        });

        this.filteredPurchaseOrders = [...this.purchaseOrders];
      });

    // Load suppliers
    this.supplierService.getAll()
      .pipe(
        catchError(error => {
          console.error('Error loading suppliers', error);
          return of([]);
        })
      )
      .subscribe(suppliers => {
        this.suppliers = suppliers;
      });

    // Load cost centers
    this.costCenterService.getAllCostCenters()
      .pipe(
        catchError(error => {
          console.error('Error loading cost centers', error);
          return of([]);
        })
      )
      .subscribe(costCenters => {
        this.costCenters = costCenters;
      });
  }

  applyFilter(): void {
    let filtered = [...this.purchaseOrders];

    // Apply search term filter
    if (this.searchTerm) {
      const searchTermLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(order =>
        order.documentNumber.toLowerCase().includes(searchTermLower) ||
        order.supplierName.toLowerCase().includes(searchTermLower) ||
        order.costCenterName.toLowerCase().includes(searchTermLower)
      );
    }

    // Apply supplier filter
    if (this.selectedSupplier) {
      filtered = filtered.filter(order =>
        order.supplierName === this.selectedSupplier
      );
    }

    // Apply cost center filter
    if (this.selectedCostCenter) {
      filtered = filtered.filter(order =>
        order.costCenterName === this.selectedCostCenter
      );
    }

    // Apply status filter
    if (this.selectedStatus) {
      filtered = filtered.filter(order =>
        order.status === this.selectedStatus
      );
    }

    this.filteredPurchaseOrders = filtered;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedSupplier = '';
    this.selectedCostCenter = '';
    this.selectedStatus = '';
    this.filteredPurchaseOrders = [...this.purchaseOrders];
  }

  createPurchaseOrder(): void {
    this.router.navigate(['/transactions/purchase-orders/new']);
  }

  createFromProductRequest(): void {
    this.isLoading = true;

    // Get approved product requests
    this.productRequestService.getByStatus('Approved')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (requests) => {
          if (requests.length === 0) {
            this.snackBar.open('No approved product requests found. Approve a product request first.', 'Close', { duration: 5000 });
            return;
          }

          // Ensure all requests have reference numbers
          const processedRequests = requests.map(request => {
            if (!request.referenceNumber && request.id) {
              request.referenceNumber = `PR-${request.id.toString().padStart(5, '0')}`;
            }
            return request;
          });

          console.log('Processed requests for dialog:', processedRequests);

          // Open dialog to select a product request
          const dialogRef = this.dialog.open(SelectProductRequestDialogComponent, {
            width: '800px',
            data: { productRequests: processedRequests }
          });

          dialogRef.afterClosed().subscribe(result => {
            if (result) {
              console.log('Selected product request:', result);
              // Navigate to create purchase order with the selected request ID
              this.router.navigate(['/transactions/purchase-orders/new'], {
                queryParams: { requestId: result.id }
              });
            }
          });
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  viewPurchaseOrder(order: PurchaseOrderListItem): void {
    this.router.navigate([`/transactions/purchase-orders/${order.id}`]);
  }

  editPurchaseOrder(order: PurchaseOrderListItem): void {
    this.router.navigate([`/transactions/purchase-orders/${order.id}/edit`]);
  }

  deletePurchaseOrder(order: PurchaseOrderListItem): void {
    if (confirm(`Are you sure you want to delete purchase order ${order.documentNumber}?`)) {
      this.isLoading = true;

      this.purchaseOrderService.cancel(order.id, 'Deleted by user')
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.errorService.showSuccess('Purchase order cancelled successfully');
            this.loadData();
          },
          error: (error: any) => this.errorService.handleError(error)
        });
    }
  }

  approvePurchaseOrder(order: PurchaseOrderListItem): void {
    this.isLoading = true;

    // First, get the transaction details to ensure we have the correct reference number
    this.purchaseOrderService.getById(order.id).subscribe({
      next: (purchaseOrder) => {
        console.log('Approving purchase order:', purchaseOrder);

        this.purchaseOrderService.approve(order.id, '')
          .pipe(finalize(() => this.isLoading = false))
          .subscribe({
            next: () => {
              this.snackBar.open(`Purchase order ${order.documentNumber} approved successfully.`, 'Close', { duration: 5000 });
              this.loadData();
            },
            error: (error) => this.errorService.handleError(error)
          });
      },
      error: (error) => {
        this.isLoading = false;
        this.errorService.handleError(error);
      }
    });
  }

  rejectPurchaseOrder(order: PurchaseOrderListItem): void {
    const reason = prompt('Please enter a reason for rejection:');
    if (reason) {
      this.isLoading = true;

      this.purchaseOrderService.reject(order.id, reason)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Purchase order rejected successfully', 'Close', { duration: 3000 });
            this.loadData();
          },
          error: (error) => this.errorService.handleError(error)
        });
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Draft': return 'status-draft';
      case 'Pending': return 'status-pending';
      case 'Submitted': return 'status-submitted';
      case 'Approved': return 'status-approved';
      case 'Rejected': return 'status-rejected';
      case 'Completed': return 'status-completed';
      case 'Cancelled': return 'status-cancelled';
      default: return '';
    }
  }
}
