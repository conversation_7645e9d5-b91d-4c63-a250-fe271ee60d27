using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface ISupplierService
{
    Task<IEnumerable<SupplierListDto>> GetAllSuppliersAsync();
    Task<SupplierDto?> GetSupplierByIdAsync(int id);
    Task<SupplierDto?> GetSupplierByNameAsync(string name);
    Task<IEnumerable<SupplierListDto>> SearchSuppliersAsync(string searchTerm);
    Task<SupplierDto> CreateSupplierAsync(CreateSupplierDto createSupplierDto);
    Task UpdateSupplierAsync(UpdateSupplierDto updateSupplierDto);
    Task DeleteSupplierAsync(int id);
    Task<bool> SupplierExistsAsync(string name, int? excludeId = null);
}
