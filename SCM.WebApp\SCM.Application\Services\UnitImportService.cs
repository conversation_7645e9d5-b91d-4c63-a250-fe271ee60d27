using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using SCM.Application.DTOs;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public interface IUnitImportService
{
    Task<UnitImportDto> ImportUnitsFromExcelAsync(Stream excelStream);
    Task<byte[]> GenerateTemplateAsync();
}

public class UnitImportService : IUnitImportService
{
    private readonly ApplicationDbContext _dbContext;

    public UnitImportService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public async Task<UnitImportDto> ImportUnitsFromExcelAsync(Stream excelStream)
    {
        var result = new UnitImportDto();
        
        try
        {
            using var package = new ExcelPackage(excelStream);
            var worksheet = package.Workbook.Worksheets[0];
            
            if (worksheet == null)
            {
                throw new InvalidOperationException("Excel file must contain at least one worksheet");
            }

            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount <= 1)
            {
                throw new InvalidOperationException("Excel file must contain data rows");
            }

            result.TotalRows = rowCount - 1; // Exclude header row

            // Load lookup data
            var unitGroups = await _dbContext.UnitGroups.ToDictionaryAsync(ug => ug.Name.ToLower(), ug => ug.Id);

            // Process each row
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var unit = await ProcessRowAsync(worksheet, row, unitGroups);
                    if (unit != null)
                    {
                        _dbContext.Units.Add(unit);
                        result.SuccessfulUnits.Add($"{unit.Name} ({unit.Abbreviation})");
                        result.SuccessCount++;
                    }
                }
                catch (Exception ex)
                {
                    result.Errors.Add(new UnitImportError
                    {
                        RowNumber = row,
                        UnitName = GetCellValue(worksheet, row, 1) ?? "",
                        UnitAbbreviation = GetCellValue(worksheet, row, 2) ?? "",
                        ErrorMessage = ex.Message
                    });
                    result.ErrorCount++;
                }
            }

            if (result.SuccessCount > 0)
            {
                await _dbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            result.Errors.Add(new UnitImportError
            {
                RowNumber = 0,
                ErrorMessage = $"File processing error: {ex.Message}"
            });
            result.ErrorCount++;
        }

        return result;
    }

    private async Task<Unit?> ProcessRowAsync(
        ExcelWorksheet worksheet, 
        int row,
        Dictionary<string, int> unitGroups)
    {
        var name = GetCellValue(worksheet, row, 1);
        var abbreviation = GetCellValue(worksheet, row, 2);

        if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(abbreviation))
        {
            throw new InvalidOperationException("Unit name and abbreviation are required");
        }

        // Check if unit already exists
        var existingUnit = await _dbContext.Units.FirstOrDefaultAsync(u => u.Name == name || u.Abbreviation == abbreviation);
        if (existingUnit != null)
        {
            throw new InvalidOperationException($"Unit with name '{name}' or abbreviation '{abbreviation}' already exists");
        }

        var unit = new Unit
        {
            Name = name,
            Abbreviation = abbreviation,
            ConversionFactor = GetDecimalValue(worksheet, row, 4) ?? 1,
            BaseConversionFactor = GetDecimalValue(worksheet, row, 5) ?? 1,
            IsBaseUnit = GetBooleanValue(worksheet, row, 6, false),
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        // Set unit group relationship
        var unitGroupName = GetCellValue(worksheet, row, 3);
        if (!string.IsNullOrWhiteSpace(unitGroupName) && unitGroups.TryGetValue(unitGroupName.ToLower(), out var unitGroupId))
        {
            unit.UnitGroupId = unitGroupId;
        }

        // If this is a base unit, ensure conversion factors are 1
        if (unit.IsBaseUnit == true)
        {
            unit.ConversionFactor = 1;
            unit.BaseConversionFactor = 1;
        }

        return unit;
    }

    public async Task<byte[]> GenerateTemplateAsync()
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Unit Import Template");

        // Add headers
        var headers = new[]
        {
            "Name*", "Abbreviation*", "Unit Group", "Conversion Factor", 
            "Base Conversion Factor", "Is Base Unit"
        };

        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
        }

        // Add sample data
        worksheet.Cells[2, 1].Value = "Kilogram";
        worksheet.Cells[2, 2].Value = "kg";
        worksheet.Cells[2, 3].Value = "Weight";
        worksheet.Cells[2, 4].Value = 1;
        worksheet.Cells[2, 5].Value = 1;
        worksheet.Cells[2, 6].Value = "TRUE";

        worksheet.Cells[3, 1].Value = "Gram";
        worksheet.Cells[3, 2].Value = "g";
        worksheet.Cells[3, 3].Value = "Weight";
        worksheet.Cells[3, 4].Value = 0.001;
        worksheet.Cells[3, 5].Value = 0.001;
        worksheet.Cells[3, 6].Value = "FALSE";

        // Auto-fit columns
        worksheet.Cells.AutoFitColumns();

        return package.GetAsByteArray();
    }

    private string? GetCellValue(ExcelWorksheet worksheet, int row, int col)
    {
        return worksheet.Cells[row, col].Value?.ToString()?.Trim();
    }

    private decimal? GetDecimalValue(ExcelWorksheet worksheet, int row, int col)
    {
        var value = GetCellValue(worksheet, row, col);
        if (string.IsNullOrWhiteSpace(value))
            return null;

        if (decimal.TryParse(value, out var result))
            return result;

        return null;
    }

    private bool GetBooleanValue(ExcelWorksheet worksheet, int row, int col, bool defaultValue)
    {
        var value = GetCellValue(worksheet, row, col);
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        return value.ToLower() is "true" or "yes" or "1";
    }
}
