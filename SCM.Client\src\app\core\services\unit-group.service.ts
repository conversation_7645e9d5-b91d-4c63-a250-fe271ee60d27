import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { UnitGroup } from '../models/unit.model';

export interface CreateUnitGroup {
  name: string;
  description?: string;
}

export interface UpdateUnitGroup {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class UnitGroupService {
  private readonly path = 'unitgroups';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<UnitGroup[]> {
    return this.apiService.get<UnitGroup[]>(this.path);
  }

  getById(id: number): Observable<UnitGroup> {
    return this.apiService.get<UnitGroup>(`${this.path}/${id}`);
  }

  getWithUnits(id: number): Observable<UnitGroup> {
    return this.apiService.get<UnitGroup>(`${this.path}/${id}/with-units`);
  }

  create(unitGroup: CreateUnitGroup): Observable<UnitGroup> {
    return this.apiService.post<UnitGroup, CreateUnitGroup>(this.path, unitGroup);
  }

  update(id: number, unitGroup: UpdateUnitGroup): Observable<void> {
    return this.apiService.put<void, UpdateUnitGroup>(`${this.path}/${id}`, unitGroup);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
