using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;
using System.Security.Cryptography;
using System.Text;

namespace SCM.Application.Services;

public class UserService : IUserService
{
    private readonly ApplicationDbContext _dbContext;

    public UserService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<UserListDto>> GetAllUsersAsync()
    {
        var users = await _dbContext.Users.ToListAsync();
        var roles = await _dbContext.Roles.ToListAsync();

        return users.Select(u => {
            var userRole = u.RoleId.HasValue ? roles.FirstOrDefault(r => r.Id == u.RoleId.Value) : null;
            return new UserListDto
            {
                Id = u.Id,
                Username = u.Username,
                Email = u.Email,
                FullName = $"{u.FirstName} {u.LastName}".Trim(),
                RoleName = userRole?.Name,
                LastLogin = u.LastLogin,
                IsActive = u.IsActive
            };
        });
    }

    public async Task<UserDto?> GetUserByIdAsync(int id)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserPermissions)
            .FirstOrDefaultAsync(u => u.Id == id);

        if (user == null)
            return null;

        // Get user's role separately if they have one
        Role? userRole = null;
        if (user.RoleId.HasValue)
        {
            userRole = await _dbContext.Roles
                .FirstOrDefaultAsync(r => r.Id == user.RoleId.Value);
        }

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Phone = user.Phone,
            RoleId = user.RoleId,
            RoleName = userRole?.Name,
            IsAdmin = user.IsAdmin,
            LastLogin = user.LastLogin,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            PermissionIds = user.UserPermissions.Select(up => up.PermissionId).ToList()
        };
    }

    public async Task<UserDto?> GetUserByUsernameAsync(string username)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserPermissions)
            .FirstOrDefaultAsync(u => u.Username == username);

        if (user == null)
            return null;

        // Get user's role separately if they have one
        Role? userRole = null;
        if (user.RoleId.HasValue)
        {
            userRole = await _dbContext.Roles
                .FirstOrDefaultAsync(r => r.Id == user.RoleId.Value);
        }

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Phone = user.Phone,
            RoleId = user.RoleId,
            RoleName = userRole?.Name,
            IsAdmin = user.IsAdmin,
            LastLogin = user.LastLogin,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            PermissionIds = user.UserPermissions.Select(up => up.PermissionId).ToList()
        };
    }

    public async Task<UserDto> CreateUserAsync(CreateUserDto createUserDto)
    {
        // Check if username already exists
        if (await _dbContext.Users.AnyAsync(u => u.Username == createUserDto.Username))
            throw new InvalidOperationException($"Username '{createUserDto.Username}' is already taken.");

        // Check if email already exists
        if (!string.IsNullOrEmpty(createUserDto.Email) && await _dbContext.Users.AnyAsync(u => u.Email == createUserDto.Email))
            throw new InvalidOperationException($"Email '{createUserDto.Email}' is already in use.");

        var user = new User
        {
            Username = createUserDto.Username,
            Email = createUserDto.Email,
            FirstName = createUserDto.FirstName,
            LastName = createUserDto.LastName,
            Phone = createUserDto.Phone,
            RoleId = createUserDto.RoleId,
            IsAdmin = createUserDto.IsAdmin,
            PasswordHash = HashPassword(createUserDto.Password),
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _dbContext.Users.Add(user);
        await _dbContext.SaveChangesAsync();

        // Add user permissions
        if (createUserDto.PermissionIds.Any())
        {
            foreach (var permissionId in createUserDto.PermissionIds)
            {
                _dbContext.UserPermissions.Add(new UserPermission
                {
                    UserId = user.Id,
                    PermissionId = permissionId
                });
            }
            await _dbContext.SaveChangesAsync();
        }

        return await GetUserByIdAsync(user.Id) ?? throw new Exception("Failed to retrieve created user.");
    }

    public async Task UpdateUserAsync(UpdateUserDto updateUserDto)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserPermissions)
            .FirstOrDefaultAsync(u => u.Id == updateUserDto.Id);

        if (user == null)
            throw new KeyNotFoundException($"User with ID {updateUserDto.Id} not found.");

        // Check if username is changed and already exists
        if (user.Username != updateUserDto.Username && await _dbContext.Users.AnyAsync(u => u.Username == updateUserDto.Username))
            throw new InvalidOperationException($"Username '{updateUserDto.Username}' is already taken.");

        // Check if email is changed and already exists
        if (!string.IsNullOrEmpty(updateUserDto.Email) && user.Email != updateUserDto.Email && 
            await _dbContext.Users.AnyAsync(u => u.Email == updateUserDto.Email))
            throw new InvalidOperationException($"Email '{updateUserDto.Email}' is already in use.");

        user.Username = updateUserDto.Username;
        user.Email = updateUserDto.Email;
        user.FirstName = updateUserDto.FirstName;
        user.LastName = updateUserDto.LastName;
        user.Phone = updateUserDto.Phone;
        user.RoleId = updateUserDto.RoleId;
        user.IsAdmin = updateUserDto.IsAdmin;
        user.IsActive = updateUserDto.IsActive;
        user.UpdatedAt = DateTime.UtcNow;

        _dbContext.Users.Update(user);

        // Update permissions
        // Remove existing permissions
        _dbContext.UserPermissions.RemoveRange(user.UserPermissions);

        // Add new permissions
        foreach (var permissionId in updateUserDto.PermissionIds)
        {
            _dbContext.UserPermissions.Add(new UserPermission
            {
                UserId = user.Id,
                PermissionId = permissionId
            });
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteUserAsync(int id)
    {
        var user = await _dbContext.Users.FindAsync(id);
        if (user == null)
            throw new KeyNotFoundException($"User with ID {id} not found.");

        // Instead of deleting, mark as inactive
        user.IsActive = false;
        user.UpdatedAt = DateTime.UtcNow;

        _dbContext.Users.Update(user);
        await _dbContext.SaveChangesAsync();
    }

    public async Task ChangePasswordAsync(ChangePasswordDto changePasswordDto)
    {
        var user = await _dbContext.Users.FindAsync(changePasswordDto.UserId);
        if (user == null)
            throw new KeyNotFoundException($"User with ID {changePasswordDto.UserId} not found.");

        // Verify current password
        if (!VerifyPassword(changePasswordDto.CurrentPassword, user.PasswordHash))
            throw new InvalidOperationException("Current password is incorrect.");

        // Update password
        user.PasswordHash = HashPassword(changePasswordDto.NewPassword);
        user.UpdatedAt = DateTime.UtcNow;
        user.MustChangePassword = false;

        _dbContext.Users.Update(user);
        await _dbContext.SaveChangesAsync();
    }

    public async Task ResetPasswordAsync(ResetPasswordDto resetPasswordDto)
    {
        var user = await _dbContext.Users.FindAsync(resetPasswordDto.UserId);
        if (user == null)
            throw new KeyNotFoundException($"User with ID {resetPasswordDto.UserId} not found.");

        // Reset password
        user.PasswordHash = HashPassword(resetPasswordDto.NewPassword);
        user.UpdatedAt = DateTime.UtcNow;
        user.MustChangePassword = true;

        _dbContext.Users.Update(user);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<bool> ValidatePasswordAsync(int userId, string password)
    {
        var user = await _dbContext.Users.FindAsync(userId);
        if (user == null)
            return false;

        return VerifyPassword(password, user.PasswordHash);
    }

    private string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
        return Convert.ToBase64String(hashedBytes);
    }

    private bool VerifyPassword(string password, string hash)
    {
        return HashPassword(password) == hash;
    }
}
