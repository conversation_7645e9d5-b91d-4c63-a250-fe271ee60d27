<div class="page-container">
  <div class="page-header">
    <h1>Recipes</h1>
    <button mat-raised-button color="primary" (click)="addRecipe()">
      <mat-icon>add</mat-icon> Add Recipe
    </button>
  </div>

  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name or ID">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Category</mat-label>
          <mat-select [(ngModel)]="selectedCategory" (selectionChange)="applyFilter()">
            <mat-option value="">All Categories</mat-option>
            <mat-option *ngFor="let category of categories" [value]="category">
              {{category}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2" *ngIf="!isLoading">
    <table mat-table [dataSource]="filteredRecipes" class="recipe-table" matSort>
      <!-- ID Column -->
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.id}}</td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.name}}</td>
      </ng-container>

      <!-- Product Column -->
      <ng-container matColumnDef="productName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Product</th>
        <td mat-cell *matCellDef="let recipe">
          <mat-chip-option selected disableRipple>{{recipe.productName}}</mat-chip-option>
        </td>
      </ng-container>

      <!-- Yield Column -->
      <ng-container matColumnDef="yield">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Yield</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.yield}} {{recipe.unitName}}</td>
      </ng-container>

      <!-- Ingredient Count Column -->
      <ng-container matColumnDef="ingredientCount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Ingredients</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.ingredientCount}}</td>
      </ng-container>

      <!-- Cost Column -->
      <ng-container matColumnDef="cost">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Cost</th>
        <td mat-cell *matCellDef="let recipe">{{recipe.cost | currency}}</td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="isActive">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let recipe" [ngClass]="{'status-active': recipe.isActive, 'status-inactive': !recipe.isActive}">
          {{recipe.isActive ? 'Active' : 'Inactive'}}
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let recipe">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="viewRecipeDetails(recipe)">
              <mat-icon>visibility</mat-icon>
              <span>View Details</span>
            </button>
            <button mat-menu-item (click)="editRecipe(recipe)">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item (click)="duplicateRecipe(recipe)">
              <mat-icon>content_copy</mat-icon>
              <span>Duplicate</span>
            </button>
            <button mat-menu-item (click)="toggleRecipeStatus(recipe)">
              <mat-icon>{{ recipe.isActive ? 'toggle_off' : 'toggle_on' }}</mat-icon>
              <span>{{ recipe.isActive ? 'Deactivate' : 'Activate' }}</span>
            </button>
            <button mat-menu-item (click)="deleteRecipe(recipe)">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
