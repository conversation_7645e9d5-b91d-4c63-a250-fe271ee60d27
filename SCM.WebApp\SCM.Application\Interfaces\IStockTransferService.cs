using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IStockTransferService
{
    Task<IEnumerable<StockTransferHeaderDto>> GetAllStockTransfersAsync();
    Task<StockTransferHeaderDto?> GetStockTransferByIdAsync(int id);
    Task<IEnumerable<StockTransferHeaderDto>> GetStockTransfersByFromCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<StockTransferHeaderDto>> GetStockTransfersByToCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<StockTransferHeaderDto>> GetStockTransfersByStatusAsync(string status);
    Task<StockTransferHeaderDto> CreateStockTransferAsync(CreateStockTransferHeaderDto createStockTransferHeaderDto);
    Task UpdateStockTransferAsync(UpdateStockTransferHeaderDto updateStockTransferHeaderDto);
    Task DeleteStockTransferAsync(int id);
    Task CompleteStockTransferAsync(CompleteStockTransferDto completeStockTransferDto);
    Task CancelStockTransferAsync(int id);
    Task<IEnumerable<StockTransferDetailDto>> GetStockTransferDetailsAsync(int stockTransferHeaderId);
    Task<StockTransferDetailDto?> GetStockTransferDetailByIdAsync(int id);
    Task<StockTransferDetailDto> CreateStockTransferDetailAsync(int stockTransferHeaderId, CreateStockTransferDetailDto createStockTransferDetailDto);
    Task UpdateStockTransferDetailAsync(UpdateStockTransferDetailDto updateStockTransferDetailDto);
    Task DeleteStockTransferDetailAsync(int id);
}
