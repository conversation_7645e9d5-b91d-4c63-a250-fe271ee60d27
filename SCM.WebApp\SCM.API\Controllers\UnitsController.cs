using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Application.Services;

namespace SCM.API.Controllers;

public class UnitsController : ApiControllerBase
{
    private readonly IUnitService _unitService;
    private readonly IUnitImportService _unitImportService;
    private readonly ILogger<UnitsController> _logger;

    public UnitsController(
        IUnitService unitService,
        IUnitImportService unitImportService,
        ILogger<UnitsController> logger)
    {
        _unitService = unitService;
        _unitImportService = unitImportService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<UnitDto>>> GetAll()
    {
        var units = await _unitService.GetAllUnitsAsync();
        return Ok(units);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<UnitDto>> GetById(int id)
    {
        var unit = await _unitService.GetUnitByIdAsync(id);
        if (unit == null)
            return NotFound();

        return Ok(unit);
    }

    [HttpGet("by-unit-group/{unitGroupId}")]
    public async Task<ActionResult<IEnumerable<UnitDto>>> GetByUnitGroupId(int unitGroupId)
    {
        var units = await _unitService.GetUnitsByUnitGroupIdAsync(unitGroupId);
        return Ok(units);
    }

    [HttpPost]
    public async Task<ActionResult<UnitDto>> Create(CreateUnitDto createUnitDto)
    {
        var unit = await _unitService.CreateUnitAsync(createUnitDto);
        return CreatedAtAction(nameof(GetById), new { id = unit.Id }, unit);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateUnitDto updateUnitDto)
    {
        if (id != updateUnitDto.Id)
            return BadRequest();

        try
        {
            await _unitService.UpdateUnitAsync(updateUnitDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _unitService.DeleteUnitAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("import")]
    public async Task<ActionResult<UnitImportDto>> ImportUnits(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest("Only Excel (.xlsx) files are supported");
            }

            using var stream = file.OpenReadStream();
            var result = await _unitImportService.ImportUnitsFromExcelAsync(stream);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing units");
            return StatusCode(500, "An error occurred while importing units");
        }
    }

    [HttpGet("import/template")]
    public async Task<IActionResult> DownloadTemplate()
    {
        try
        {
            var templateBytes = await _unitImportService.GenerateTemplateAsync();
            return File(templateBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "UnitImportTemplate.xlsx");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating import template");
            return StatusCode(500, "An error occurred while generating the import template");
        }
    }
}
