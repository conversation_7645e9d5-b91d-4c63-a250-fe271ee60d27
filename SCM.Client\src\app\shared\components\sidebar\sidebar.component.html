<div class="sidebar-container">
  <div class="sidebar-header">
    <h2>SCM System</h2>
  </div>

  <div class="sidebar-content">
    <!-- Business Unit Tree -->
    <div class="business-unit-section">
      <div class="section-header" (click)="toggleBusinessTree()">
        <mat-icon>domain</mat-icon>
        <span>Business Unit</span>
        <mat-icon class="expand-icon">
          {{ showBusinessTree ? 'expand_more' : 'chevron_right' }}
        </mat-icon>
      </div>
      
      <div class="tree-container" [class.hidden]="!showBusinessTree">
        <app-business-structure-tree
          [showIcons]="true"
          [expandByDefault]="false"
          (nodeSelected)="onNodeSelected($event)">
        </app-business-structure-tree>
      </div>
    </div>

    <mat-divider></mat-divider>

    <!-- Dashboard -->
    <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
      <mat-icon>dashboard</mat-icon>
      <span>Dashboard</span>
    </a>

    <!-- Products -->
    <a mat-list-item routerLink="/products" routerLinkActive="active">
      <mat-icon>inventory_2</mat-icon>
      <span>Products</span>
    </a>

    <!-- Inventory -->
    <a mat-list-item routerLink="/inventory" routerLinkActive="active">
      <mat-icon>inventory</mat-icon>
      <span>Inventory</span>
    </a>

    <!-- Transactions -->
    <a mat-list-item routerLink="/transactions" routerLinkActive="active">
      <mat-icon>receipt_long</mat-icon>
      <span>Transactions</span>
    </a>

    <!-- Reports -->
    <a mat-list-item routerLink="/reports" routerLinkActive="active">
      <mat-icon>bar_chart</mat-icon>
      <span>Reports</span>
    </a>

    <!-- Configuration -->
    <a mat-list-item routerLink="/configuration" routerLinkActive="active">
      <mat-icon>settings</mat-icon>
      <span>Configuration</span>
    </a>
  </div>
</div>
