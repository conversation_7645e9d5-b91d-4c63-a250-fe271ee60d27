namespace SCM.Application.DTOs;

public class StockAddDto
{
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? CostPrice { get; set; }
    public DateTime? TransactionDate { get; set; }
    public string Source { get; set; } = string.Empty;
    public int? TransactionId { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public string ReferenceType { get; set; } = string.Empty;
    public string? Notes { get; set; }
}
