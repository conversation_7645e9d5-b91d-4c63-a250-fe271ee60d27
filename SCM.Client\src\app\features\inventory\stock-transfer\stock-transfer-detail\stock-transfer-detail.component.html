<div class="container">
  <div class="header">
    <h1>{{ isEditMode ? 'Edit Stock Transfer' : 'New Stock Transfer' }}</h1>
    <div class="header-actions">
      <button mat-button color="warn" (click)="cancel()">
        <mat-icon>close</mat-icon> Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveStockTransfer()" [disabled]="transferForm.invalid || isLoading">
        <mat-icon>save</mat-icon> Save
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockTransfer?.status === 'Draft'" (click)="completeStockTransfer()" [disabled]="isLoading">
        <mat-icon>check_circle</mat-icon> Complete Transfer
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && (stockTransfer?.status === 'Draft' || stockTransfer?.status === 'Pending')" (click)="cancelStockTransfer()" [disabled]="isLoading">
        <mat-icon>cancel</mat-icon> Cancel Transfer
      </button>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <form [formGroup]="transferForm" *ngIf="!isLoading">
    <mat-card class="form-card">
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Transfer Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="transferDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="transferForm.get('transferDate')?.hasError('required')">
              Transfer date is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>From Cost Center</mat-label>
            <mat-select formControlName="fromCostCenterId">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{ costCenter.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="transferForm.get('fromCostCenterId')?.hasError('required')">
              From cost center is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>To Cost Center</mat-label>
            <mat-select formControlName="toCostCenterId">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{ costCenter.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="transferForm.get('toCostCenterId')?.hasError('required')">
              To cost center is required
            </mat-error>
            <mat-error *ngIf="transferForm.get('toCostCenterId')?.hasError('sameCostCenter')">
              From and To cost centers cannot be the same
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes</mat-label>
            <textarea matInput formControlName="notes" rows="3"></textarea>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <div class="section-header">
      <h2>Transfer Items</h2>
      <button mat-mini-fab color="primary" (click)="addDetail()" [disabled]="transferForm.disabled">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div formArrayName="details">
      <mat-card class="form-card" *ngFor="let detailForm of detailsFormArray.controls; let i = index" [formGroupName]="i">
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product</mat-label>
              <mat-select formControlName="productId" (selectionChange)="checkStockAvailability($event.value, i)">
                <mat-option *ngFor="let product of products" [value]="product.id">
                  {{ product.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="detailForm.get('productId')?.hasError('required')">
                Product is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Quantity</mat-label>
              <input matInput type="number" formControlName="quantity" min="0.01" step="0.01" (blur)="validateQuantity(i)">
              <mat-error *ngIf="detailForm.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="detailForm.get('quantity')?.hasError('min')">
                Quantity must be greater than 0
              </mat-error>
              <mat-error *ngIf="detailForm.get('quantity')?.hasError('noStock')">
                No stock available
              </mat-error>
              <mat-error *ngIf="detailForm.get('quantity')?.hasError('exceedsStock')">
                Quantity exceeds available stock
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Unit</mat-label>
              <mat-select formControlName="unitId">
                <mat-option *ngFor="let unit of units" [value]="unit.id">
                  {{ unit.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <input matInput formControlName="notes">
            </mat-form-field>
          </div>

          <div class="detail-actions" *ngIf="!transferForm.disabled">
            <button mat-icon-button color="warn" (click)="removeDetail(i)" matTooltip="Remove Item">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <div class="no-items" *ngIf="detailsFormArray.length === 0">
        <p>No items added to this transfer. Click the + button to add items.</p>
      </div>
    </div>

    <div class="form-actions">
      <button mat-button color="warn" (click)="cancel()">
        Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveStockTransfer()" [disabled]="transferForm.invalid || isLoading">
        Save
      </button>
      <button mat-raised-button color="accent" *ngIf="isEditMode && stockTransfer?.status === 'Draft'" (click)="completeStockTransfer()" [disabled]="isLoading">
        Complete Transfer
      </button>
      <button mat-raised-button color="warn" *ngIf="isEditMode && (stockTransfer?.status === 'Draft' || stockTransfer?.status === 'Pending')" (click)="cancelStockTransfer()" [disabled]="isLoading">
        Cancel Transfer
      </button>
    </div>
  </form>

  <div class="status-info" *ngIf="isEditMode && stockTransfer">
    <div class="status-badge" [ngClass]="'status-' + stockTransfer.status.toLowerCase()">
      {{ stockTransfer.status }}
    </div>
    <div class="status-details" *ngIf="stockTransfer.status !== 'Draft'">
      <p *ngIf="stockTransfer.status === 'Completed'">
        Completed by {{ stockTransfer.completedByName || 'Unknown' }} on {{ stockTransfer.completedAt | date:'medium' }}
      </p>
      <p *ngIf="stockTransfer.status === 'Cancelled'">
        Cancelled on {{ stockTransfer.updatedAt | date:'medium' }}
      </p>
    </div>
  </div>
</div>
