using Mapster;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SCM.Application.Services
{
    public class StoreService : IStoreService
    {
        private readonly IRepository<Store> _storeRepository;
        private readonly ApplicationDbContext _dbContext;
        private readonly IMapper _mapper;

        public StoreService(IRepository<Store> storeRepository, ApplicationDbContext dbContext, IMapper mapper)
        {
            _storeRepository = storeRepository ?? throw new ArgumentNullException(nameof(storeRepository));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public async Task<IEnumerable<StoreDto>> GetAllStoresAsync()
        {
            try
            {
                var stores = await _dbContext.Stores
                    .Include(s => s.Location)
                    .ToListAsync();

                var storeDtos = new List<StoreDto>();
                foreach (var store in stores)
                {
                    // Load company information if location exists
                    if (store.Location != null && store.Location.CompanyId > 0)
                    {
                        store.Location.Company = await _dbContext.Companies.FindAsync(store.Location.CompanyId);
                    }

                    var dto = new StoreDto
                    {
                        Id = store.Id,
                        Name = store.Name,
                        LocationId = store.LocationId,
                        LocationName = store.Location?.Name,
                        CompanyName = store.Location?.Company?.Name,
                        IsSalesPoint = store.IsSalesPoint,
                        LogoPath = store.LogoPath,
                        IsActive = store.IsActive,
                        CreatedAt = store.CreatedAt,
                        UpdatedAt = store.UpdatedAt,
                        CostCentersCount = store.CostCenters.Count
                    };
                    storeDtos.Add(dto);
                }

                return storeDtos;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetAllStoresAsync: {ex.Message}");
                return new List<StoreDto>();
            }
        }

        public async Task<StoreDto?> GetStoreByIdAsync(int id)
        {
            try
            {
                var store = await _dbContext.Stores
                    .Include(s => s.Location)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (store == null)
                    return null;

                // Load company information if location exists
                if (store.Location != null && store.Location.CompanyId > 0)
                {
                    store.Location.Company = await _dbContext.Companies.FindAsync(store.Location.CompanyId);
                }

                return new StoreDto
                {
                    Id = store.Id,
                    Name = store.Name,
                    LocationId = store.LocationId,
                    LocationName = store.Location?.Name,
                    CompanyName = store.Location?.Company?.Name,
                    IsSalesPoint = store.IsSalesPoint,
                    LogoPath = store.LogoPath,
                    IsActive = store.IsActive,
                    CreatedAt = store.CreatedAt,
                    UpdatedAt = store.UpdatedAt,
                    CostCentersCount = store.CostCenters.Count
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetStoreByIdAsync: {ex.Message}");
                return null;
            }
        }

        public async Task<IEnumerable<StoreDto>> GetStoresByLocationIdAsync(int locationId)
        {
            try
            {
                var stores = await _dbContext.Stores
                    .Include(s => s.Location)
                    .Where(s => s.LocationId == locationId)
                    .ToListAsync();

                // Load company information for each store's location
                foreach (var store in stores)
                {
                    if (store.Location != null && store.Location.CompanyId > 0)
                    {
                        store.Location.Company = await _dbContext.Companies.FindAsync(store.Location.CompanyId);
                    }
                }

                var storeDtos = new List<StoreDto>();
                foreach (var store in stores)
                {
                    var dto = new StoreDto
                    {
                        Id = store.Id,
                        Name = store.Name,
                        LocationId = store.LocationId,
                        LocationName = store.Location?.Name,
                        CompanyName = store.Location?.Company?.Name,
                        IsSalesPoint = store.IsSalesPoint,
                        LogoPath = store.LogoPath,
                        IsActive = store.IsActive,
                        CreatedAt = store.CreatedAt,
                        UpdatedAt = store.UpdatedAt,
                        CostCentersCount = store.CostCenters.Count
                    };
                    storeDtos.Add(dto);
                }

                return storeDtos;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetStoresByLocationIdAsync: {ex.Message}");
                return new List<StoreDto>();
            }
        }

        public async Task<IEnumerable<StoreDto>> GetStoresByCompanyIdAsync(int companyId)
        {
            try
            {
                var stores = await _dbContext.Stores
                    .Include(s => s.Location)
                    .Where(s => s.Location.CompanyId == companyId)
                    .ToListAsync();

                // Load company information for each store's location
                foreach (var store in stores)
                {
                    if (store.Location != null && store.Location.CompanyId > 0)
                    {
                        store.Location.Company = await _dbContext.Companies.FindAsync(store.Location.CompanyId);
                    }
                }

                var storeDtos = new List<StoreDto>();
                foreach (var store in stores)
                {
                    var dto = new StoreDto
                    {
                        Id = store.Id,
                        Name = store.Name,
                        LocationId = store.LocationId,
                        LocationName = store.Location?.Name,
                        CompanyName = store.Location?.Company?.Name,
                        IsSalesPoint = store.IsSalesPoint,
                        LogoPath = store.LogoPath,
                        IsActive = store.IsActive,
                        CreatedAt = store.CreatedAt,
                        UpdatedAt = store.UpdatedAt,
                        CostCentersCount = store.CostCenters.Count
                    };
                    storeDtos.Add(dto);
                }

                return storeDtos;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetStoresByCompanyIdAsync: {ex.Message}");
                return new List<StoreDto>();
            }
        }

        public async Task<StoreDto?> GetStoreWithLocationsAsync(int id)
        {
            try
            {
                var store = await _dbContext.Stores
                    .Include(s => s.Location)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (store == null)
                    return null;

                // Load company information if location exists
                if (store.Location != null && store.Location.CompanyId > 0)
                {
                    store.Location.Company = await _dbContext.Companies.FindAsync(store.Location.CompanyId);
                }

                return new StoreDto
                {
                    Id = store.Id,
                    Name = store.Name,
                    LocationId = store.LocationId,
                    LocationName = store.Location?.Name,
                    CompanyName = store.Location?.Company?.Name,
                    IsSalesPoint = store.IsSalesPoint,
                    LogoPath = store.LogoPath,
                    IsActive = store.IsActive,
                    CreatedAt = store.CreatedAt,
                    UpdatedAt = store.UpdatedAt,
                    CostCentersCount = store.CostCenters.Count
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetStoreWithLocationsAsync: {ex.Message}");
                return null;
            }
        }

        public async Task<StoreDto?> GetStoreWithCostCentersAsync(int id)
        {
            try
            {
                var store = await _dbContext.Stores
                    .Include(s => s.Location)
                    .Include(s => s.CostCenters)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (store == null)
                    return null;

                // Load company information if location exists
                if (store.Location != null && store.Location.CompanyId > 0)
                {
                    store.Location.Company = await _dbContext.Companies.FindAsync(store.Location.CompanyId);
                }

                var storeDto = new StoreDto
                {
                    Id = store.Id,
                    Name = store.Name,
                    LocationId = store.LocationId,
                    LocationName = store.Location?.Name,
                    CompanyName = store.Location?.Company?.Name,
                    IsSalesPoint = store.IsSalesPoint,
                    LogoPath = store.LogoPath,
                    IsActive = store.IsActive,
                    CreatedAt = store.CreatedAt,
                    UpdatedAt = store.UpdatedAt,
                    CostCentersCount = store.CostCenters.Count,
                    CostCenters = new List<CostCenterDto>()
                };

                foreach (var costCenter in store.CostCenters)
                {
                    storeDto.CostCenters.Add(new CostCenterDto
                    {
                        Id = costCenter.Id,
                        Name = costCenter.Name,
                        StoreId = costCenter.StoreId,
                        TypeId = costCenter.TypeId,
                        TypeName = costCenter.TypeName,
                        AutoTransfer = costCenter.AutoTransfer,
                        IsSalesPoint = costCenter.IsSalesPoint,
                        Abbreviation = costCenter.Abbreviation,
                        IsActive = costCenter.IsActive,
                        CreatedAt = costCenter.CreatedAt,
                        UpdatedAt = costCenter.UpdatedAt
                    });
                }

                return storeDto;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetStoreWithCostCentersAsync: {ex.Message}");
                return null;
            }
        }

        public async Task<StoreDto> CreateStoreAsync(CreateStoreDto createStoreDto)
        {
            try
            {
                var store = new Store
                {
                    Name = createStoreDto.Name,
                    LocationId = createStoreDto.LocationId,
                    IsSalesPoint = createStoreDto.IsSalesPoint,
                    LogoPath = createStoreDto.LogoPath,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                await _storeRepository.AddAsync(store);

                // Get the location name for the response
                var location = await _dbContext.Locations
                    .Include(l => l.Company)
                    .FirstOrDefaultAsync(l => l.Id == store.LocationId);

                return new StoreDto
                {
                    Id = store.Id,
                    Name = store.Name,
                    LocationId = store.LocationId,
                    LocationName = location?.Name,
                    CompanyName = location?.Company?.Name,
                    IsSalesPoint = store.IsSalesPoint,
                    LogoPath = store.LogoPath,
                    IsActive = store.IsActive,
                    CreatedAt = store.CreatedAt,
                    UpdatedAt = store.UpdatedAt,
                    CostCentersCount = 0
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CreateStoreAsync: {ex.Message}");
                throw; // Rethrow to let the controller handle it
            }
        }

        public async Task UpdateStoreAsync(UpdateStoreDto updateStoreDto)
        {
            try
            {
                var store = await _storeRepository.GetByIdAsync(updateStoreDto.Id);
                if (store == null)
                    throw new KeyNotFoundException($"Store with ID {updateStoreDto.Id} not found.");

                // Manually map properties to avoid null reference issues
                store.Name = updateStoreDto.Name;
                store.LocationId = updateStoreDto.LocationId;
                store.IsSalesPoint = updateStoreDto.IsSalesPoint;
                store.LogoPath = updateStoreDto.LogoPath;
                store.IsActive = updateStoreDto.IsActive;
                store.UpdatedAt = DateTime.UtcNow;

                await _storeRepository.UpdateAsync(store);
            }
            catch (KeyNotFoundException)
            {
                throw; // Rethrow to let the controller handle it
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in UpdateStoreAsync: {ex.Message}");
                throw; // Rethrow to let the controller handle it
            }
        }

        public async Task DeleteStoreAsync(int id)
        {
            try
            {
                var store = await _storeRepository.GetByIdAsync(id);
                if (store == null)
                    throw new KeyNotFoundException($"Store with ID {id} not found.");

                // Check if the store has any cost centers
                var hasCostCenters = await _dbContext.CostCenters.AnyAsync(cc => cc.StoreId == id);
                if (hasCostCenters)
                    throw new InvalidOperationException($"Cannot delete store with ID {id} because it has associated cost centers.");

                await _storeRepository.DeleteAsync(store);
            }
            catch (KeyNotFoundException)
            {
                throw; // Rethrow to let the controller handle it
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in DeleteStoreAsync: {ex.Message}");
                throw; // Rethrow to let the controller handle it
            }
        }
    }
}
