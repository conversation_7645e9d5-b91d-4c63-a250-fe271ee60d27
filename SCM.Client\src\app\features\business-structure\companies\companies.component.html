<div class="container">
  <div class="row">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ isEditMode ? 'Edit Company' : 'Add New Company' }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="companyForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Name</mat-label>
                  <input matInput formControlName="name" placeholder="Enter company name">
                  <mat-error *ngIf="companyForm.get('name')?.hasError('required')">
                    Name is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Code</mat-label>
                  <input matInput formControlName="code" placeholder="Enter company code">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-12">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" placeholder="Enter description" rows="3"></textarea>
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-12">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Address</mat-label>
                  <textarea matInput formControlName="address" placeholder="Enter address" rows="2"></textarea>
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>City</mat-label>
                  <input matInput formControlName="city" placeholder="Enter city">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>State/Province</mat-label>
                  <input matInput formControlName="state" placeholder="Enter state/province">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Country</mat-label>
                  <input matInput formControlName="country" placeholder="Enter country">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Postal Code</mat-label>
                  <input matInput formControlName="postalCode" placeholder="Enter postal code">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Phone</mat-label>
                  <input matInput formControlName="phone" placeholder="Enter phone number">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email</mat-label>
                  <input matInput formControlName="email" placeholder="Enter email" type="email">
                  <mat-error *ngIf="companyForm.get('email')?.hasError('email')">
                    Please enter a valid email address
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Website</mat-label>
                  <input matInput formControlName="website" placeholder="Enter website URL">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Tax Number</mat-label>
                  <input matInput formControlName="taxNumber" placeholder="Enter tax number">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-12">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Logo URL</mat-label>
                  <input matInput formControlName="logoUrl" placeholder="Enter logo URL">
                </mat-form-field>
              </div>
            </div>
            
            <div class="button-row">
              <button mat-raised-button color="primary" type="submit" [disabled]="companyForm.invalid || isSubmitting">
                <mat-icon>save</mat-icon>
                {{ isEditMode ? 'Update' : 'Save' }}
              </button>
              <button mat-raised-button type="button" (click)="resetForm()" [disabled]="isSubmitting">
                <mat-icon>refresh</mat-icon>
                Reset
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
  
  <div class="row mt-4">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Companies</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="loading-shade" *ngIf="isLoading">
            <mat-spinner diameter="50"></mat-spinner>
          </div>
          
          <div class="table-container">
            <table mat-table [dataSource]="companies" matSort (matSortChange)="onSortChange($event)" class="full-width">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                <td mat-cell *matCellDef="let company">{{ company.name }}</td>
              </ng-container>
              
              <!-- Code Column -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Code</th>
                <td mat-cell *matCellDef="let company">{{ company.code }}</td>
              </ng-container>
              
              <!-- City Column -->
              <ng-container matColumnDef="city">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>City</th>
                <td mat-cell *matCellDef="let company">{{ company.city }}</td>
              </ng-container>
              
              <!-- Country Column -->
              <ng-container matColumnDef="country">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Country</th>
                <td mat-cell *matCellDef="let company">{{ company.country }}</td>
              </ng-container>
              
              <!-- Phone Column -->
              <ng-container matColumnDef="phone">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Phone</th>
                <td mat-cell *matCellDef="let company">{{ company.phone }}</td>
              </ng-container>
              
              <!-- Status Column -->
              <ng-container matColumnDef="isActive">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                <td mat-cell *matCellDef="let company">
                  <button mat-icon-button [color]="company.isActive ? 'primary' : 'warn'"
                          (click)="toggleStatus(company)" matTooltip="Click to toggle status">
                    <mat-icon>{{ company.isActive ? 'toggle_on' : 'toggle_off' }}</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <!-- Locations Count Column -->
              <ng-container matColumnDef="locationsCount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Locations</th>
                <td mat-cell *matCellDef="let company">{{ company.locationsCount }}</td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let company">
                  <button mat-icon-button color="primary" (click)="viewCompany(company.id)" matTooltip="View">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button color="accent" (click)="editCompany(company)" matTooltip="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteCompany(company.id)" matTooltip="Delete">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
            
            <mat-paginator [length]="totalItems"
                          [pageSize]="pageSize"
                          [pageSizeOptions]="[5, 10, 25, 50]"
                          (page)="onPageChange($event)">
            </mat-paginator>
          </div>
          
          <div class="no-data-message" *ngIf="companies.length === 0 && !isLoading">
            No companies found. Please add a company.
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
