import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router, ActivatedRoute } from '@angular/router';

import { UnitService, CreateUnit, UpdateUnit } from '../../../core/services/unit.service';
import { UnitGroupService } from '../../../core/services/unit-group.service';
import { Unit, UnitGroup } from '../../../core/models/unit.model';
import { PermissionsService } from '../../../core/services/permissions.service';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { UnitImportDialogComponent } from '../unit-import-dialog/unit-import-dialog.component';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-units',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './units.component.html',
  styleUrls: ['./units.component.scss']
})
export class UnitsComponent implements OnInit {
  unitForm!: FormGroup;
  units: Unit[] = [];
  unitGroups: UnitGroup[] = [];
  dataSource = new MatTableDataSource<Unit>([]);
  displayedColumns: string[] = ['id', 'name', 'abbreviation', 'unitGroupName', 'conversionFactor', 'baseConversionFactor', 'isBaseUnit', 'isActive', 'actions'];
  isLoading = false;
  isSubmitting = false;
  isEditMode = false;
  selectedUnitId: number | null = null;
  filteredUnitGroupId: number | null = null;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private fb: FormBuilder,
    private unitService: UnitService,
    private unitGroupService: UnitGroupService,
    private permissionsService: PermissionsService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadUnitGroups();

    // Check if we're filtering by unit group
    this.route.queryParams.subscribe(params => {
      if (params['unitGroupId']) {
        this.filteredUnitGroupId = +params['unitGroupId'];
        this.loadUnitsByUnitGroup(this.filteredUnitGroupId);
      } else {
        this.loadUnits();
      }
    });
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  initForm(): void {
    this.unitForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(50)]],
      abbreviation: ['', [Validators.required, Validators.maxLength(10)]],
      unitGroupId: [null, Validators.required],
      conversionFactor: [1, [Validators.required, Validators.min(0.000001)]],
      baseConversionFactor: [1],
      isBaseUnit: [false],
      isActive: [true]
    });
  }

  loadUnits(): void {
    this.isLoading = true;
    this.unitService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (units) => {
          this.units = units;
          this.dataSource.data = units;
        },
        error: (error) => {
          console.error('Error loading units', error);
          this.snackBar.open('Error loading units. Please try again.', 'Close', {
            duration: 3000
          });
        }
      });
  }

  loadUnitsByUnitGroup(unitGroupId: number): void {
    this.isLoading = true;
    this.unitService.getByUnitGroupId(unitGroupId)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (units) => {
          this.units = units;
          this.dataSource.data = units;
        },
        error: (error) => {
          console.error('Error loading units', error);
          this.snackBar.open('Error loading units. Please try again.', 'Close', {
            duration: 3000
          });
        }
      });
  }

  loadUnitGroups(): void {
    this.unitGroupService.getAll().subscribe({
      next: (unitGroups) => {
        this.unitGroups = unitGroups;
      },
      error: (error) => {
        console.error('Error loading unit groups', error);
        this.snackBar.open('Error loading unit groups. Please try again.', 'Close', {
          duration: 3000
        });
      }
    });
  }

  resetForm(): void {
    this.unitForm.reset({
      name: '',
      abbreviation: '',
      unitGroupId: null,
      conversionFactor: 1,
      baseConversionFactor: 1,
      isBaseUnit: false,
      isActive: true
    });
    this.isEditMode = false;
    this.selectedUnitId = null;
  }

  saveUnit(): void {
    if (this.unitForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    if (this.isEditMode && this.selectedUnitId) {
      // Update existing unit
      const updateUnit: UpdateUnit = {
        id: this.selectedUnitId,
        ...this.unitForm.value
      };

      this.unitService.update(this.selectedUnitId, updateUnit)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Unit updated successfully', 'Close', {
              duration: 3000
            });
            this.resetForm();
            if (this.filteredUnitGroupId) {
              this.loadUnitsByUnitGroup(this.filteredUnitGroupId);
            } else {
              this.loadUnits();
            }
          },
          error: (error) => {
            console.error('Error updating unit', error);
            this.snackBar.open('Error updating unit. Please try again.', 'Close', {
              duration: 3000
            });
          }
        });
    } else {
      // Create new unit
      const createUnit: CreateUnit = this.unitForm.value;

      this.unitService.create(createUnit)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Unit created successfully', 'Close', {
              duration: 3000
            });
            this.resetForm();
            if (this.filteredUnitGroupId) {
              this.loadUnitsByUnitGroup(this.filteredUnitGroupId);
            } else {
              this.loadUnits();
            }
          },
          error: (error) => {
            console.error('Error creating unit', error);
            this.snackBar.open('Error creating unit. Please try again.', 'Close', {
              duration: 3000
            });
          }
        });
    }
  }

  editUnit(unit: Unit): void {
    this.isEditMode = true;
    this.selectedUnitId = unit.id;

    this.unitForm.patchValue({
      name: unit.name,
      abbreviation: unit.abbreviation,
      unitGroupId: unit.unitGroupId,
      conversionFactor: unit.conversionFactor,
      baseConversionFactor: unit.baseConversionFactor,
      isBaseUnit: unit.isBaseUnit,
      isActive: unit.isActive
    });
  }

  deleteUnit(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: {
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this unit? This action cannot be undone.'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.unitService.delete(id)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe({
            next: () => {
              this.snackBar.open('Unit deleted successfully', 'Close', {
                duration: 3000
              });
              if (this.filteredUnitGroupId) {
                this.loadUnitsByUnitGroup(this.filteredUnitGroupId);
              } else {
                this.loadUnits();
              }
            },
            error: (error) => {
              console.error('Error deleting unit', error);
              this.snackBar.open('Error deleting unit. It may be in use by one or more products.', 'Close', {
                duration: 3000
              });
            }
          });
      }
    });
  }

  clearFilter(): void {
    this.filteredUnitGroupId = null;
    this.router.navigate(['/configuration/units']);
    this.loadUnits();
  }

  canEdit(): boolean {
    return this.permissionsService.hasPermission('config.units.edit') ||
           this.permissionsService.hasPermission('admin');
  }

  canDelete(): boolean {
    return this.permissionsService.hasPermission('config.units.delete') ||
           this.permissionsService.hasPermission('admin');
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  openImportDialog(): void {
    const dialogRef = this.dialog.open(UnitImportDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.successCount > 0) {
        // Reload the units list if any units were imported successfully
        this.loadUnits();
      }
    });
  }
}
