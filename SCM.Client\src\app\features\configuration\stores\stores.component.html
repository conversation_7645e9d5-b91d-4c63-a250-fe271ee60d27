<div class="container">
  <div class="row">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ isEditMode ? 'Edit Store' : 'Add New Store' }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="storeForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Name</mat-label>
                  <input matInput formControlName="name" placeholder="Enter store name">
                  <mat-error *ngIf="storeForm.get('name')?.hasError('required')">
                    Name is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Code</mat-label>
                  <input matInput formControlName="code" placeholder="Enter store code">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Location</mat-label>
                  <mat-select formControlName="locationId">
                    <mat-option *ngFor="let location of locations" [value]="location.id">
                      {{ location.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="storeForm.get('locationId')?.hasError('required')">
                    Location is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" placeholder="Enter description"></textarea>
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Address</mat-label>
                  <textarea matInput formControlName="address" placeholder="Enter address"></textarea>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>City</mat-label>
                  <input matInput formControlName="city" placeholder="Enter city">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>State</mat-label>
                  <input matInput formControlName="state" placeholder="Enter state/province">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Country</mat-label>
                  <input matInput formControlName="country" placeholder="Enter country">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Postal Code</mat-label>
                  <input matInput formControlName="postalCode" placeholder="Enter postal code">
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Phone</mat-label>
                  <input matInput formControlName="phone" placeholder="Enter phone number">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email</mat-label>
                  <input matInput formControlName="email" placeholder="Enter email address">
                  <mat-error *ngIf="storeForm.get('email')?.hasError('email')">
                    Please enter a valid email address
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Contact Person</mat-label>
                  <input matInput formControlName="contactPerson" placeholder="Enter contact person">
                </mat-form-field>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <mat-checkbox formControlName="isSalesPoint">Is Sales Point</mat-checkbox>
              </div>
            </div>
            
            <div class="button-row">
              <button mat-raised-button color="primary" type="submit" [disabled]="storeForm.invalid || isSubmitting">
                <mat-icon>save</mat-icon>
                {{ isEditMode ? 'Update' : 'Save' }}
              </button>
              <button mat-raised-button type="button" (click)="resetForm()" [disabled]="isSubmitting">
                <mat-icon>refresh</mat-icon>
                Reset
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
  
  <div class="row mt-4">
    <div class="col-md-12">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Stores</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="loading-shade" *ngIf="isLoading">
            <mat-spinner diameter="50"></mat-spinner>
          </div>
          
          <div class="table-container">
            <table mat-table [dataSource]="stores" matSort (matSortChange)="onSortChange($event)" class="full-width">
              
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                <td mat-cell *matCellDef="let store">{{ store.name }}</td>
              </ng-container>
              
              <!-- Code Column -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Code</th>
                <td mat-cell *matCellDef="let store">{{ store.code }}</td>
              </ng-container>
              
              <!-- Location Column -->
              <ng-container matColumnDef="locationName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Location</th>
                <td mat-cell *matCellDef="let store">{{ store.locationName }}</td>
              </ng-container>
              
              <!-- City Column -->
              <ng-container matColumnDef="city">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>City</th>
                <td mat-cell *matCellDef="let store">{{ store.city }}</td>
              </ng-container>
              
              <!-- Country Column -->
              <ng-container matColumnDef="country">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Country</th>
                <td mat-cell *matCellDef="let store">{{ store.country }}</td>
              </ng-container>
              
              <!-- Is Sales Point Column -->
              <ng-container matColumnDef="isSalesPoint">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Sales Point</th>
                <td mat-cell *matCellDef="let store">
                  <mat-icon color="primary" *ngIf="store.isSalesPoint">check_circle</mat-icon>
                  <mat-icon color="warn" *ngIf="!store.isSalesPoint">cancel</mat-icon>
                </td>
              </ng-container>
              
              <!-- Status Column -->
              <ng-container matColumnDef="isActive">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                <td mat-cell *matCellDef="let store">
                  <button mat-icon-button [color]="store.isActive ? 'primary' : 'warn'" 
                          (click)="toggleStatus(store)" 
                          matTooltip="{{ store.isActive ? 'Active (Click to deactivate)' : 'Inactive (Click to activate)' }}">
                    <mat-icon>{{ store.isActive ? 'toggle_on' : 'toggle_off' }}</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <!-- Cost Centers Count Column -->
              <ng-container matColumnDef="costCentersCount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Cost Centers</th>
                <td mat-cell *matCellDef="let store">{{ store.costCentersCount }}</td>
              </ng-container>
              
              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let store">
                  <button mat-icon-button color="accent" (click)="manageCostCenters(store)" matTooltip="Manage Cost Centers">
                    <mat-icon>business</mat-icon>
                  </button>
                  <button mat-icon-button color="primary" (click)="editStore(store)" matTooltip="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteStore(store.id)" matTooltip="Delete">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              
              <!-- Row shown when there is no matching data -->
              <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="8">No stores found. Please add a store.</td>
              </tr>
            </table>
            
            <mat-paginator [length]="totalItems" 
                          [pageSize]="pageSize" 
                          [pageSizeOptions]="[5, 10, 25, 50]" 
                          (page)="onPageChange($event)">
            </mat-paginator>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
