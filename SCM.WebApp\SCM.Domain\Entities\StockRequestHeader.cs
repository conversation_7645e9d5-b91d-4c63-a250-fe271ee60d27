using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class StockRequestHeader : BaseEntity
{
    public string ReferenceNumber { get; set; } = string.Empty;
    public int FromCostCenterId { get; set; }
    public int ToCostCenterId { get; set; }
    public DateTime RequestDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft"; // Draft, Submitted, Approved, Rejected, Completed, Cancelled
    public int? CreatedById { get; set; }
    public int? ApprovedById { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public int? CompletedById { get; set; }
    public DateTime? CompletedAt { get; set; }
    
    // Navigation properties
    public virtual CostCenter FromCostCenter { get; set; } = null!;
    public virtual CostCenter ToCostCenter { get; set; } = null!;
    public virtual User? CreatedBy { get; set; }
    public virtual User? ApprovedBy { get; set; }
    public virtual User? CompletedBy { get; set; }
    public virtual ICollection<StockRequestDetail> Details { get; set; } = new List<StockRequestDetail>();
}
