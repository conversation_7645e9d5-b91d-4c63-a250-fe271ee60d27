using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using SCM.Application.DTOs;
using SCM.Application.Services;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;
using Xunit;

namespace SCM.Tests.Integration;

public class StockRequestToTransferWorkflowTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly StockRequestService _stockRequestService;
    private readonly StockTransferService _stockTransferService;
    private readonly StockService _stockService;

    public StockRequestToTransferWorkflowTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);

        // Setup AutoMapper
        var mapperConfig = new AutoMapper.MapperConfiguration(cfg =>
        {
            cfg.AddProfile<SCM.Application.Mappings.MappingProfile>();
        });
        var mapper = mapperConfig.CreateMapper();

        // Initialize services
        _stockService = new StockService(_context);
        _stockRequestService = new StockRequestService(_context, mapper);
        _stockTransferService = new StockTransferService(_context, mapper, _stockService);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        // Add test cost centers
        var fromCostCenter = new CostCenter
        {
            Id = 1,
            Name = "Main Store",
            Code = "MS001",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        var toCostCenter = new CostCenter
        {
            Id = 2,
            Name = "Kitchen",
            Code = "KT001",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.CostCenters.AddRange(fromCostCenter, toCostCenter);

        // Add test product
        var product = new Product
        {
            Id = 1,
            Name = "Rice",
            Code = "RICE001",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.Products.Add(product);

        // Add test unit
        var unit = new Unit
        {
            Id = 1,
            Name = "Kilogram",
            Symbol = "kg",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.Units.Add(unit);

        // Add test batch
        var batch = new Batch
        {
            Id = 1,
            BatchNumber = "B001",
            ProductId = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.Batches.Add(batch);

        // Add initial stock in the source cost center
        var stockOnHand = new StockOnHand
        {
            ProductId = 1,
            CostCenterId = 1,
            UnitId = 1,
            Quantity = 100m,
            CostPrice = 5.00m,
            LastUpdated = DateTime.UtcNow
        };

        _context.StockOnHand.Add(stockOnHand);

        // Add transaction stage types
        var requestStage = new TransactionStageType
        {
            Id = 1,
            Name = "Request",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        var transferStage = new TransactionStageType
        {
            Id = 2,
            Name = "Transfer",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.TransactionStageTypes.AddRange(requestStage, transferStage);

        _context.SaveChanges();
    }

    [Fact]
    public async Task CompleteWorkflow_CreateStockRequest_ApproveRequest_CreateTransfer_CompleteTransfer_ShouldUpdateStock()
    {
        // Arrange
        var createStockRequestDto = new CreateStockRequestHeaderDto
        {
            FromCostCenterId = 1,
            ToCostCenterId = 2,
            RequestDate = DateTime.UtcNow,
            Notes = "Test stock request",
            Details = new List<CreateStockRequestDetailDto>
            {
                new CreateStockRequestDetailDto
                {
                    ProductId = 1,
                    BatchId = 1,
                    UnitId = 1,
                    Quantity = 10m,
                    Notes = "Test detail"
                }
            }
        };

        // Act 1: Create stock request
        var stockRequest = await _stockRequestService.CreateStockRequestAsync(createStockRequestDto);
        Assert.NotNull(stockRequest);
        Assert.Equal("Draft", stockRequest.Status);

        // Act 2: Submit stock request
        await _stockRequestService.SubmitStockRequestAsync(new SubmitStockRequestDto { Id = stockRequest.Id });
        var submittedRequest = await _stockRequestService.GetStockRequestByIdAsync(stockRequest.Id);
        Assert.Equal("Submitted", submittedRequest?.Status);

        // Act 3: Approve stock request
        await _stockRequestService.ApproveStockRequestAsync(new ApproveStockRequestDto { Id = stockRequest.Id });
        var approvedRequest = await _stockRequestService.GetStockRequestByIdAsync(stockRequest.Id);
        Assert.Equal("Approved", approvedRequest?.Status);

        // Act 4: Create stock transfer from approved request
        var createStockTransferDto = new CreateStockTransferHeaderDto
        {
            FromCostCenterId = approvedRequest.FromCostCenterId,
            ToCostCenterId = approvedRequest.ToCostCenterId,
            TransferDate = DateTime.UtcNow,
            Notes = $"Transfer from approved request {approvedRequest.ReferenceNumber}",
            Details = approvedRequest.Details?.Select(d => new CreateStockTransferDetailDto
            {
                ProductId = d.ProductId,
                BatchId = d.BatchId,
                UnitId = d.UnitId,
                Quantity = d.Quantity,
                Notes = d.Notes
            }).ToList() ?? new List<CreateStockTransferDetailDto>()
        };

        var stockTransfer = await _stockTransferService.CreateStockTransferAsync(createStockTransferDto);
        Assert.NotNull(stockTransfer);
        Assert.Equal("Draft", stockTransfer.Status);

        // Act 5: Complete stock transfer
        await _stockTransferService.CompleteStockTransferAsync(new CompleteStockTransferDto { Id = stockTransfer.Id });
        var completedTransfer = await _stockTransferService.GetStockTransferByIdAsync(stockTransfer.Id);
        Assert.Equal("Completed", completedTransfer?.Status);

        // Assert: Verify stock has been updated
        var sourceStock = await _context.StockOnHand
            .FirstOrDefaultAsync(s => s.ProductId == 1 && s.CostCenterId == 1);
        var destinationStock = await _context.StockOnHand
            .FirstOrDefaultAsync(s => s.ProductId == 1 && s.CostCenterId == 2);

        Assert.NotNull(sourceStock);
        Assert.Equal(90m, sourceStock.Quantity); // 100 - 10

        Assert.NotNull(destinationStock);
        Assert.Equal(10m, destinationStock.Quantity); // 0 + 10

        // Assert: Verify stock transactions were created
        var stockTransactions = await _context.StockTransactions
            .Where(st => st.ProductId == 1)
            .ToListAsync();

        Assert.True(stockTransactions.Count >= 2); // At least one for out and one for in
        Assert.Contains(stockTransactions, st => st.Source == "Stock Transfer Out" && st.Quantity == -10m);
        Assert.Contains(stockTransactions, st => st.Source == "Stock Transfer In" && st.Quantity == 10m);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
