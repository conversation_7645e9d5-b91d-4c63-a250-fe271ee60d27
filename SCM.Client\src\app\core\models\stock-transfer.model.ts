export interface StockTransferHeader {
  id: number;
  referenceNumber: string;
  fromCostCenterId: number;
  fromCostCenterName: string;
  toCostCenterId: number;
  toCostCenterName: string;
  transferDate: Date;
  notes?: string;
  status: string;
  createdById?: number;
  createdByName?: string;
  completedById?: number;
  completedByName?: string;
  completedAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  details?: StockTransferDetail[];
}

export interface StockTransferDetail {
  id: number;
  stockTransferHeaderId: number;
  productId: number;
  productName: string;
  productCode: string;
  batchId?: number;
  batchNumber?: string;
  unitId?: number;
  unitName?: string;
  quantity: number;
  costPrice?: number;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateStockTransferHeader {
  fromCostCenterId: number;
  toCostCenterId: number;
  transferDate: Date;
  notes?: string;
  details: CreateStockTransferDetail[];
}

export interface CreateStockTransferDetail {
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  notes?: string;
}

export interface UpdateStockTransferHeader {
  id: number;
  fromCostCenterId: number;
  toCostCenterId: number;
  transferDate: Date;
  notes?: string;
  status: string;
}

export interface UpdateStockTransferDetail {
  id: number;
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  notes?: string;
}

export interface CompleteStockTransfer {
  id: number;
  notes?: string;
}
