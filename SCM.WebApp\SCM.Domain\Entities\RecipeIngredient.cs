using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class RecipeIngredient : BaseEntity
{
    public int RecipeId { get; set; }
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public int? UnitId { get; set; }
    public string? Notes { get; set; }
    public int Sequence { get; set; }
    public decimal? Cost { get; set; }
    public decimal? WastagePercentage { get; set; }

    // Navigation properties
    public virtual Recipe Recipe { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    public virtual Unit? Unit { get; set; }
}
