namespace SCM.Application.DTOs;

/// <summary>
/// Data Transfer Object for Transaction Stage Type
/// </summary>
public class TransactionStageTypeModel
{
    /// <summary>
    /// Gets or sets the stage type identifier.
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the name of the stage type.
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the description of the stage type.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the sequence number of the stage type.
    /// </summary>
    public int Sequence { get; set; }

    /// <summary>
    /// Gets or sets the date when this stage type was created.
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the date when this stage type was last updated.
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether this stage type is active.
    /// </summary>
    public bool IsActive { get; set; } = true;
}
