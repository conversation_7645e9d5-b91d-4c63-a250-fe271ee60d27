.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
  border-right: 1px solid #e0e0e0;
  width: 250px;
}

.sidebar-header {
  padding: 16px;
  background-color: #3f51b5;
  color: white;
  
  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
}

.business-unit-section {
  .section-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    background-color: #e3f2fd;
    
    &:hover {
      background-color: #bbdefb;
    }
    
    mat-icon {
      margin-right: 8px;
      color: #1976d2;
    }
    
    span {
      flex: 1;
      font-weight: 500;
    }
    
    .expand-icon {
      margin-right: 0;
    }
  }
  
  .tree-container {
    max-height: 400px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
    
    &.hidden {
      max-height: 0;
      overflow: hidden;
    }
  }
}

a[mat-list-item] {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  text-decoration: none;
  color: rgba(0, 0, 0, 0.87);
  
  mat-icon {
    margin-right: 16px;
    color: #616161;
  }
  
  &.active {
    background-color: rgba(63, 81, 181, 0.1);
    color: #3f51b5;
    
    mat-icon {
      color: #3f51b5;
    }
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}
