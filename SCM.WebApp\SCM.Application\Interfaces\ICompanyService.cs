using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface ICompanyService
{
    Task<IEnumerable<CompanyDto>> GetAllCompaniesAsync();
    Task<CompanyDto?> GetCompanyByIdAsync(int id);
    Task<CompanyDto?> GetCompanyWithLocationsAsync(int id);
    Task<CompanyDto> CreateCompanyAsync(CreateCompanyDto createCompanyDto);
    Task UpdateCompanyAsync(UpdateCompanyDto updateCompanyDto);
    Task DeleteCompanyAsync(int id);
}
