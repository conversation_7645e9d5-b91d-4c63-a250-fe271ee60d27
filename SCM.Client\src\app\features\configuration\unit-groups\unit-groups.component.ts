import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router, ActivatedRoute } from '@angular/router';

import { UnitGroupService, CreateUnitGroup, UpdateUnitGroup } from '../../../core/services/unit-group.service';
import { UnitGroup } from '../../../core/models/unit.model';
import { PermissionsService } from '../../../core/services/permissions.service';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-unit-groups',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './unit-groups.component.html',
  styleUrls: ['./unit-groups.component.scss']
})
export class UnitGroupsComponent implements OnInit {
  unitGroupForm!: FormGroup;
  unitGroups: UnitGroup[] = [];
  dataSource = new MatTableDataSource<UnitGroup>([]);
  displayedColumns: string[] = ['id', 'name', 'description', 'unitCount', 'isActive', 'actions'];
  isLoading = false;
  isSubmitting = false;
  isEditMode = false;
  selectedUnitGroupId: number | null = null;
  
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  
  constructor(
    private fb: FormBuilder,
    private unitGroupService: UnitGroupService,
    private permissionsService: PermissionsService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute
  ) {}
  
  ngOnInit(): void {
    this.initForm();
    this.loadUnitGroups();
  }
  
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }
  
  initForm(): void {
    this.unitGroupForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', Validators.maxLength(255)],
      isActive: [true]
    });
  }
  
  loadUnitGroups(): void {
    this.isLoading = true;
    this.unitGroupService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (unitGroups) => {
          this.unitGroups = unitGroups;
          this.dataSource.data = unitGroups;
        },
        error: (error) => {
          console.error('Error loading unit groups', error);
          this.snackBar.open('Error loading unit groups. Please try again.', 'Close', {
            duration: 3000
          });
        }
      });
  }
  
  resetForm(): void {
    this.unitGroupForm.reset({
      name: '',
      description: '',
      isActive: true
    });
    this.isEditMode = false;
    this.selectedUnitGroupId = null;
  }
  
  saveUnitGroup(): void {
    if (this.unitGroupForm.invalid) {
      return;
    }
    
    this.isSubmitting = true;
    
    if (this.isEditMode && this.selectedUnitGroupId) {
      // Update existing unit group
      const updateUnitGroup: UpdateUnitGroup = {
        id: this.selectedUnitGroupId,
        ...this.unitGroupForm.value
      };
      
      this.unitGroupService.update(this.selectedUnitGroupId, updateUnitGroup)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Unit group updated successfully', 'Close', {
              duration: 3000
            });
            this.resetForm();
            this.loadUnitGroups();
          },
          error: (error) => {
            console.error('Error updating unit group', error);
            this.snackBar.open('Error updating unit group. Please try again.', 'Close', {
              duration: 3000
            });
          }
        });
    } else {
      // Create new unit group
      const createUnitGroup: CreateUnitGroup = this.unitGroupForm.value;
      
      this.unitGroupService.create(createUnitGroup)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Unit group created successfully', 'Close', {
              duration: 3000
            });
            this.resetForm();
            this.loadUnitGroups();
          },
          error: (error) => {
            console.error('Error creating unit group', error);
            this.snackBar.open('Error creating unit group. Please try again.', 'Close', {
              duration: 3000
            });
          }
        });
    }
  }
  
  editUnitGroup(unitGroup: UnitGroup): void {
    this.isEditMode = true;
    this.selectedUnitGroupId = unitGroup.id;
    
    this.unitGroupForm.patchValue({
      name: unitGroup.name,
      description: unitGroup.description || '',
      isActive: unitGroup.isActive
    });
  }
  
  deleteUnitGroup(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: {
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this unit group? This action cannot be undone.'
      }
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.unitGroupService.delete(id)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe({
            next: () => {
              this.snackBar.open('Unit group deleted successfully', 'Close', {
                duration: 3000
              });
              this.loadUnitGroups();
            },
            error: (error) => {
              console.error('Error deleting unit group', error);
              this.snackBar.open('Error deleting unit group. It may be in use by one or more units.', 'Close', {
                duration: 3000
              });
            }
          });
      }
    });
  }
  
  viewUnits(unitGroupId: number): void {
    this.router.navigate(['/configuration/units'], { queryParams: { unitGroupId } });
  }
  
  canEdit(): boolean {
    return this.permissionsService.hasPermission('config.unitGroups.edit') || 
           this.permissionsService.hasPermission('admin');
  }
  
  canDelete(): boolean {
    return this.permissionsService.hasPermission('config.unitGroups.delete') || 
           this.permissionsService.hasPermission('admin');
  }
  
  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
}
