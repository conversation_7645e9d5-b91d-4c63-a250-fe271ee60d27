using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IProductRequestService
{
    Task<IEnumerable<ProductRequestListItem>> GetAllAsync();
    Task<ProductRequestHeader?> GetByIdAsync(int id);
    Task<IEnumerable<ProductRequestListItem>> GetByCostCenterAsync(int costCenterId);
    Task<IEnumerable<ProductRequestListItem>> GetByStatusAsync(string status);
    Task<ProductRequestHeader> CreateAsync(CreateProductRequestHeader createProductRequest);
    Task UpdateAsync(int id, UpdateProductRequestHeader updateProductRequest);
    Task DeleteAsync(int id);
    Task SubmitAsync(SubmitProductRequestDto submitDto);
    Task ApproveAsync(ApproveProductRequestDto approveDto);
    Task RejectAsync(RejectProductRequestDto rejectDto);
    Task CompleteAsync(CompleteProductRequestDto completeDto);
    Task CancelAsync(int id);
    Task<IEnumerable<ProductRequestDetail>> GetDetailsAsync(int productRequestId);
    Task<ProductRequestDetail> AddDetailAsync(int productRequestId, CreateProductRequestDetail createDetail);
    Task UpdateDetailAsync(int id, UpdateProductRequestDetail updateDetail);
    Task DeleteDetailAsync(int id);
}
