using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class StoresController : ApiControllerBase
{
    private readonly IStoreService _storeService;

    public StoresController(IStoreService storeService)
    {
        _storeService = storeService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<StoreDto>>> GetAll()
    {
        try
        {
            var stores = await _storeService.GetAllStoresAsync();
            return Ok(stores);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving stores. Please try again later.");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<StoreDto>> GetById(int id)
    {
        try
        {
            var store = await _storeService.GetStoreByIdAsync(id);
            if (store == null)
                return NotFound();

            return Ok(store);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving the store. Please try again later.");
        }
    }

    [HttpGet("location/{locationId}")]
    public async Task<ActionResult<IEnumerable<StoreDto>>> GetByLocationId(int locationId)
    {
        try
        {
            var stores = await _storeService.GetStoresByLocationIdAsync(locationId);
            return Ok(stores);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving stores. Please try again later.");
        }
    }

    [HttpGet("company/{companyId}")]
    public async Task<ActionResult<IEnumerable<StoreDto>>> GetByCompanyId(int companyId)
    {
        try
        {
            var stores = await _storeService.GetStoresByCompanyIdAsync(companyId);
            return Ok(stores);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving stores. Please try again later.");
        }
    }

    [HttpGet("{id}/cost-centers")]
    public async Task<ActionResult<StoreDto>> GetWithCostCenters(int id)
    {
        try
        {
            var store = await _storeService.GetStoreWithCostCentersAsync(id);
            if (store == null)
                return NotFound();

            return Ok(store);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving the store with cost centers. Please try again later.");
        }
    }

    [HttpPost]
    public async Task<ActionResult<StoreDto>> Create(CreateStoreDto createStoreDto)
    {
        try
        {
            var store = await _storeService.CreateStoreAsync(createStoreDto);
            return CreatedAtAction(nameof(GetById), new { id = store.Id }, store);
        }
        catch (Exception ex)
        {
            // Check if it's a SQL exception for unique constraint violation
            if (ex.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
            {
                // Check if it's specifically the store name constraint
                if (sqlEx.Message.Contains("UQ_Store_Name"))
                {
                    return BadRequest($"A store with the name '{createStoreDto.Name}' already exists. Please use a different name.");
                }
            }

            // For other exceptions, return a generic error
            return BadRequest("An error occurred while creating the store. Please try again.");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateStoreDto updateStoreDto)
    {
        if (id != updateStoreDto.Id)
            return BadRequest();

        try
        {
            await _storeService.UpdateStoreAsync(updateStoreDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _storeService.DeleteStoreAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPut("{id}/activate")]
    public async Task<IActionResult> Activate(int id)
    {
        try
        {
            var store = await _storeService.GetStoreByIdAsync(id);
            if (store == null)
                return NotFound();

            var updateDto = new UpdateStoreDto
            {
                Id = store.Id,
                Name = store.Name,
                LocationId = store.LocationId,
                IsSalesPoint = store.IsSalesPoint,
                LogoPath = store.LogoPath,
                IsActive = true
            };

            await _storeService.UpdateStoreAsync(updateDto);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("{id}/deactivate")]
    public async Task<IActionResult> Deactivate(int id)
    {
        try
        {
            var store = await _storeService.GetStoreByIdAsync(id);
            if (store == null)
                return NotFound();

            var updateDto = new UpdateStoreDto
            {
                Id = store.Id,
                Name = store.Name,
                LocationId = store.LocationId,
                IsSalesPoint = store.IsSalesPoint,
                LogoPath = store.LogoPath,
                IsActive = false
            };

            await _storeService.UpdateStoreAsync(updateDto);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
