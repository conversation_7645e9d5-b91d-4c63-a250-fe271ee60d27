.import-dialog {
  min-width: 600px;
  max-width: 800px;
}

.file-section {
  margin-bottom: 20px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  margin-bottom: 20px;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #1976d2;
  }

  .upload-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
  }

  p {
    margin-bottom: 16px;
    color: #666;
  }

  .selected-file {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #1976d2;
    font-weight: 500;
  }
}

.template-section {
  text-align: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;

  p {
    margin-bottom: 16px;
    color: #666;
  }
}

.progress-section {
  text-align: center;
  padding: 40px;

  p {
    margin-top: 16px;
    color: #666;
  }
}

.results-section {
  .result-summary {
    margin-bottom: 20px;

    .summary-stats {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        background-color: #f5f5f5;
        min-width: 120px;

        &.success {
          background-color: #e8f5e8;
          color: #2e7d32;
        }

        &.error {
          background-color: #ffebee;
          color: #c62828;
        }

        .stat-label {
          font-size: 12px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
  }

  .success-list {
    margin-bottom: 20px;

    .unit-list {
      max-height: 200px;
      overflow-y: auto;

      .unit-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .success-icon {
          color: #4caf50;
          font-size: 18px;
        }
      }
    }
  }

  .error-details {
    .error-table {
      width: 100%;
      max-height: 300px;
      overflow-y: auto;

      th {
        background-color: #f5f5f5;
        font-weight: 600;
      }

      td {
        padding: 12px 8px;
        border-bottom: 1px solid #eee;
      }

      tr:hover {
        background-color: #f9f9f9;
      }
    }
  }
}

mat-dialog-actions {
  padding: 16px 24px;
  gap: 12px;
}
