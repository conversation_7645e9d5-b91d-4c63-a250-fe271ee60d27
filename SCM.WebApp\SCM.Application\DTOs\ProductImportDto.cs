namespace SCM.Application.DTOs;

public class ProductImportDto
{
    public int TotalRows { get; set; }
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public List<ProductImportError> Errors { get; set; } = new List<ProductImportError>();
    public List<string> SuccessfulProducts { get; set; } = new List<string>();
}

public class ProductImportError
{
    public int RowNumber { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
}

public class ProductImportTemplate
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Brand { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Group { get; set; } = string.Empty;
    public string SubGroup { get; set; } = string.Empty;
    public decimal? CostPrice { get; set; }
    public decimal? SalesPrice { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public string Notes { get; set; } = string.Empty;
    public bool IsStockItem { get; set; } = true;
    public bool IsRecipe { get; set; } = false;
    public bool HasExpiry { get; set; } = false;
    public bool IsProduction { get; set; } = false;
    public bool IsSaleable { get; set; } = true;
    public string Tax { get; set; } = string.Empty;
    public string SalesUnit { get; set; } = string.Empty;
    public decimal? SalesUnitConversionFactor { get; set; }
    public bool AllowDiscount { get; set; } = true;
}
