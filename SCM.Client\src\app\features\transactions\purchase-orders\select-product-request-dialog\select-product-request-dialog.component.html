<h2 mat-dialog-title>Select Product Request</h2>
<div mat-dialog-content>
  <p>Select an approved product request to create a purchase order:</p>

  <mat-form-field appearance="outline" class="search-field">
    <mat-label>Search</mat-label>
    <input matInput (keyup)="onSearch($event)" [value]="searchTerm" placeholder="Search by reference number or cost center">
    <mat-icon matSuffix>search</mat-icon>
  </mat-form-field>

  <div class="table-container">
    <table mat-table [dataSource]="filteredRequests">
      <!-- Reference Number Column -->
      <ng-container matColumnDef="referenceNumber">
        <th mat-header-cell *matHeaderCellDef>Reference Number</th>
        <td mat-cell *matCellDef="let request">
          {{request.referenceNumber || ('PR-' + request.id.toString().padStart(5, '0'))}}
        </td>
      </ng-container>

      <!-- Cost Center Column -->
      <ng-container matColumnDef="costCenterName">
        <th mat-header-cell *matHeaderCellDef>Cost Center</th>
        <td mat-cell *matCellDef="let request">
          {{request.costCenterName || 'N/A'}}
        </td>
      </ng-container>

      <!-- Request Date Column -->
      <ng-container matColumnDef="requestDate">
        <th mat-header-cell *matHeaderCellDef>Request Date</th>
        <td mat-cell *matCellDef="let request">{{request.requestDate | date:'mediumDate'}}</td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let request">
          <button mat-raised-button color="primary" (click)="selectRequest(request)">
            Select
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <div *ngIf="filteredRequests.length === 0" class="no-data-message">
      No approved product requests found.
    </div>
  </div>
</div>
<div mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
</div>
