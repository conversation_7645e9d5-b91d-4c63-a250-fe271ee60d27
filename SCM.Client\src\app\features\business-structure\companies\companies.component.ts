import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { CompanyService } from '../../../core/services/company.service';
import { Company, CompanyList, CreateCompany, UpdateCompany } from '../../../core/models/company.model';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-companies',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTooltipModule
  ],
  templateUrl: './companies.component.html',
  styleUrls: ['./companies.component.scss']
})
export class CompaniesComponent implements OnInit {
  companyForm!: FormGroup;
  companies: CompanyList[] = [];
  displayedColumns: string[] = ['name', 'code', 'city', 'country', 'phone', 'isActive', 'locationsCount', 'actions'];
  isLoading = false;
  isSubmitting = false;
  isEditMode = false;
  selectedCompanyId: number | null = null;
  
  // Pagination
  totalItems = 0;
  pageSize = 10;
  pageIndex = 0;
  
  constructor(
    private fb: FormBuilder,
    private companyService: CompanyService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router
  ) {}
  
  ngOnInit(): void {
    this.initForm();
    this.loadCompanies();
  }
  
  initForm(): void {
    this.companyForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(150)]],
      code: ['', Validators.maxLength(50)],
      description: ['', Validators.maxLength(500)],
      address: ['', Validators.maxLength(200)],
      city: ['', Validators.maxLength(100)],
      state: ['', Validators.maxLength(100)],
      country: ['', Validators.maxLength(100)],
      postalCode: ['', Validators.maxLength(20)],
      phone: ['', Validators.maxLength(20)],
      email: ['', [Validators.email, Validators.maxLength(100)]],
      website: ['', Validators.maxLength(100)],
      taxNumber: ['', Validators.maxLength(50)],
      logoUrl: ['', Validators.maxLength(255)]
    });
  }
  
  loadCompanies(): void {
    this.isLoading = true;
    this.companyService.getAll()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (data) => {
          this.companies = data;
          this.totalItems = data.length;
        },
        error: (error) => {
          console.error('Error loading companies:', error);
          this.snackBar.open('Failed to load companies', 'Close', { duration: 3000 });
        }
      });
  }
  
  onSubmit(): void {
    if (this.companyForm.invalid) {
      return;
    }
    
    this.isSubmitting = true;
    
    if (this.isEditMode && this.selectedCompanyId) {
      const updateData: UpdateCompany = {
        ...this.companyForm.value,
        id: this.selectedCompanyId,
        isActive: true
      };
      
      this.companyService.update(this.selectedCompanyId, updateData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Company updated successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadCompanies();
          },
          error: (error) => {
            console.error('Error updating company:', error);
            this.snackBar.open('Failed to update company', 'Close', { duration: 3000 });
          }
        });
    } else {
      const createData: CreateCompany = this.companyForm.value;
      
      this.companyService.create(createData)
        .pipe(finalize(() => this.isSubmitting = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Company created successfully', 'Close', { duration: 3000 });
            this.resetForm();
            this.loadCompanies();
          },
          error: (error) => {
            console.error('Error creating company:', error);
            this.snackBar.open('Failed to create company', 'Close', { duration: 3000 });
          }
        });
    }
  }
  
  editCompany(company: CompanyList): void {
    this.isEditMode = true;
    this.selectedCompanyId = company.id;
    
    this.companyService.getById(company.id)
      .subscribe({
        next: (data) => {
          this.companyForm.patchValue({
            name: data.name,
            code: data.code,
            description: data.description,
            address: data.address,
            city: data.city,
            state: data.state,
            country: data.country,
            postalCode: data.postalCode,
            phone: data.phone,
            email: data.email,
            website: data.website,
            taxNumber: data.taxNumber,
            logoUrl: data.logoUrl
          });
        },
        error: (error) => {
          console.error('Error loading company details:', error);
          this.snackBar.open('Failed to load company details', 'Close', { duration: 3000 });
        }
      });
  }
  
  viewCompany(id: number): void {
    this.router.navigate(['/companies', id]);
  }
  
  deleteCompany(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: { title: 'Confirm Delete', message: 'Are you sure you want to delete this company?' }
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.companyService.delete(id)
          .subscribe({
            next: () => {
              this.snackBar.open('Company deleted successfully', 'Close', { duration: 3000 });
              this.loadCompanies();
            },
            error: (error) => {
              console.error('Error deleting company:', error);
              this.snackBar.open('Failed to delete company', 'Close', { duration: 3000 });
            }
          });
      }
    });
  }
  
  toggleStatus(company: CompanyList): void {
    const action = company.isActive ? 'deactivate' : 'activate';
    const method = company.isActive ? this.companyService.deactivate(company.id) : this.companyService.activate(company.id);
    
    method.subscribe({
      next: () => {
        this.snackBar.open(`Company ${action}d successfully`, 'Close', { duration: 3000 });
        this.loadCompanies();
      },
      error: (error) => {
        console.error(`Error ${action}ing company:`, error);
        this.snackBar.open(`Failed to ${action} company`, 'Close', { duration: 3000 });
      }
    });
  }
  
  resetForm(): void {
    this.companyForm.reset();
    this.isEditMode = false;
    this.selectedCompanyId = null;
  }
  
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
  }
  
  onSortChange(sort: Sort): void {
    // Implement sorting logic if needed
  }
}
