<div class="container">
  <div class="header">
    <h1>{{ isEditMode ? 'Edit Supplier' : 'New Supplier' }}</h1>
    <div class="header-actions">
      <button mat-button color="warn" (click)="cancel()">
        <mat-icon>close</mat-icon> Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveSupplier()" [disabled]="supplierForm.invalid || isLoading">
        <mat-icon>save</mat-icon> Save
      </button>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <form [formGroup]="supplierForm" *ngIf="!isLoading">
    <mat-card class="form-card">
      <mat-card-content>
        <mat-tab-group>
          <mat-tab label="Basic Information">
            <div class="tab-content">
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Supplier Name</mat-label>
                  <input matInput formControlName="name" placeholder="Enter supplier name">
                  <mat-error *ngIf="supplierForm.get('name')?.hasError('required')">
                    Supplier name is required
                  </mat-error>
                  <mat-error *ngIf="supplierForm.get('name')?.hasError('maxlength')">
                    Supplier name cannot exceed 150 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Contact Person</mat-label>
                  <input matInput formControlName="contactPerson" placeholder="Enter contact person name">
                  <mat-error *ngIf="supplierForm.get('contactPerson')?.hasError('maxlength')">
                    Contact person name cannot exceed 150 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Phone</mat-label>
                  <input matInput formControlName="phone" placeholder="Enter phone number">
                  <mat-error *ngIf="supplierForm.get('phone')?.hasError('maxlength')">
                    Phone number cannot exceed 50 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Email</mat-label>
                  <input matInput formControlName="email" placeholder="Enter email address">
                  <mat-error *ngIf="supplierForm.get('email')?.hasError('email')">
                    Please enter a valid email address
                  </mat-error>
                  <mat-error *ngIf="supplierForm.get('email')?.hasError('maxlength')">
                    Email cannot exceed 150 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Tax Number</mat-label>
                  <input matInput formControlName="taxNumber" placeholder="Enter tax number">
                  <mat-error *ngIf="supplierForm.get('taxNumber')?.hasError('maxlength')">
                    Tax number cannot exceed 50 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-checkbox formControlName="isActive">Active</mat-checkbox>
              </div>
            </div>
          </mat-tab>

          <mat-tab label="Address">
            <div class="tab-content">
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Address</mat-label>
                  <textarea matInput formControlName="address" rows="3" placeholder="Enter address"></textarea>
                  <mat-error *ngIf="supplierForm.get('address')?.hasError('maxlength')">
                    Address cannot exceed 255 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>City</mat-label>
                  <input matInput formControlName="city" placeholder="Enter city">
                  <mat-error *ngIf="supplierForm.get('city')?.hasError('maxlength')">
                    City cannot exceed 100 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>State/Province</mat-label>
                  <input matInput formControlName="state" placeholder="Enter state or province">
                  <mat-error *ngIf="supplierForm.get('state')?.hasError('maxlength')">
                    State cannot exceed 100 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Country</mat-label>
                  <input matInput formControlName="country" placeholder="Enter country">
                  <mat-error *ngIf="supplierForm.get('country')?.hasError('maxlength')">
                    Country cannot exceed 100 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Postal Code</mat-label>
                  <input matInput formControlName="postalCode" placeholder="Enter postal code">
                  <mat-error *ngIf="supplierForm.get('postalCode')?.hasError('maxlength')">
                    Postal code cannot exceed 20 characters
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </mat-tab>

          <mat-tab label="Banking & Notes">
            <div class="tab-content">
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Bank Name</mat-label>
                  <input matInput formControlName="bankName" placeholder="Enter bank name">
                  <mat-error *ngIf="supplierForm.get('bankName')?.hasError('maxlength')">
                    Bank name cannot exceed 100 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Bank Account</mat-label>
                  <input matInput formControlName="bankAccount" placeholder="Enter bank account number">
                  <mat-error *ngIf="supplierForm.get('bankAccount')?.hasError('maxlength')">
                    Bank account cannot exceed 50 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Notes</mat-label>
                  <textarea matInput formControlName="notes" rows="5" placeholder="Enter additional notes"></textarea>
                </mat-form-field>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>

    <div class="form-actions">
      <button mat-button color="warn" (click)="cancel()">
        Cancel
      </button>
      <button mat-raised-button color="primary" (click)="saveSupplier()" [disabled]="supplierForm.invalid || isLoading">
        Save
      </button>
    </div>
  </form>

  <div class="supplier-info" *ngIf="isEditMode && supplier">
    <div class="info-row">
      <span class="label">Created:</span>
      <span class="value">{{ supplier.createdAt | date:'medium' }}</span>
    </div>
    <div class="info-row" *ngIf="supplier.updatedAt">
      <span class="label">Last Updated:</span>
      <span class="value">{{ supplier.updatedAt | date:'medium' }}</span>
    </div>
  </div>
</div>
