using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class StockTransaction : BaseEntity
{
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public int BatchId { get; set; }
    public int UnitId { get; set; }
    public decimal Quantity { get; set; }
    public DateTime TransactionDate { get; set; }
    public string Source { get; set; } = string.Empty;
    public int? TransactionId { get; set; }
    
    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual CostCenter CostCenter { get; set; } = null!;
    public virtual Batch Batch { get; set; } = null!;
    public virtual Unit Unit { get; set; } = null!;
    public virtual TransactionHeader? Transaction { get; set; }
}
