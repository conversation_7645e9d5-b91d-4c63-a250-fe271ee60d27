import { Store } from './store.model';
import { CostCenter } from './cost-center.model';

export interface Location {
  id: number;
  name: string;
  code?: string;
  description?: string;
  companyId: number;
  companyName?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  stores?: Store[];
  costCenters?: CostCenter[];
}

export interface LocationList {
  id: number;
  name: string;
  code?: string;
  description?: string;
  companyId: number;
  companyName?: string;
  city?: string;
  country?: string;
  isActive: boolean;
  storesCount: number;
  costCentersCount: number;
}

export interface CreateLocation {
  name: string;
  code?: string;
  description?: string;
  companyId: number;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
}

export interface UpdateLocation {
  id: number;
  name: string;
  code?: string;
  description?: string;
  companyId: number;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
}
