using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class CostCenter : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string? Code { get; set; }
    public int StoreId { get; set; }
    public int? TypeId { get; set; }
    public string? TypeName { get; set; }
    public string? Description { get; set; }
    public bool AutoTransfer { get; set; } = false;
    public bool IsSalesPoint { get; set; } = false;
    public string? Abbreviation { get; set; }

    // Navigation properties
    public virtual Store Store { get; set; } = null!;
    public virtual CostCenterType? Type { get; set; }
    public virtual ICollection<StockOnHand> StockOnHand { get; set; } = new List<StockOnHand>();
    public virtual ICollection<ProductCostCenterLink> ProductLinks { get; set; } = new List<ProductCostCenterLink>();

    [System.ComponentModel.DataAnnotations.Schema.NotMapped]
    public virtual ICollection<TransactionHeader> SourceTransactionHeaders { get; set; } = new List<TransactionHeader>();

    [System.ComponentModel.DataAnnotations.Schema.NotMapped]
    public virtual ICollection<TransactionHeader> DestinationTransactionHeaders { get; set; } = new List<TransactionHeader>();

    public virtual ICollection<UserCostCenterAccess> UserAccesses { get; set; } = new List<UserCostCenterAccess>();
    public virtual ICollection<StockTakeHeader> StockTakeHeaders { get; set; } = new List<StockTakeHeader>();
}
