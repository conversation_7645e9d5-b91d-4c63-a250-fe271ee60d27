import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTreeModule, MatTreeNestedDataSource } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { NestedTreeControl } from '@angular/cdk/tree';
import { CompanyService } from '../../../core/services/company.service';
import { LocationService } from '../../../core/services/location.service';
import { StoreService } from '../../../core/services/store.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { ErrorService } from '../../../core/services/error.service';
import { fork<PERSON>oin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

export interface BusinessNode {
  id: number;
  name: string;
  type: 'company' | 'location' | 'store' | 'costCenter';
  isActive: boolean;
  children?: BusinessNode[];
  isLoading?: boolean;
  isExpanded?: boolean;
}

@Component({
  selector: 'app-business-structure-tree',
  standalone: true,
  imports: [
    CommonModule,
    MatTreeModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    RouterModule
  ],
  templateUrl: './business-structure-tree.component.html',
  styleUrls: ['./business-structure-tree.component.scss']
})
export class BusinessStructureTreeComponent implements OnInit {
  @Input() showIcons = true;
  @Input() expandByDefault = false;
  @Input() selectedNodeId: number | null = null;
  @Input() selectedNodeType: string | null = null;
  @Output() nodeSelected = new EventEmitter<BusinessNode>();

  treeControl = new NestedTreeControl<BusinessNode>(node => node.children);
  dataSource = new MatTreeNestedDataSource<BusinessNode>();
  isLoading = false;

  constructor(
    private companyService: CompanyService,
    private locationService: LocationService,
    private storeService: StoreService,
    private costCenterService: CostCenterService,
    private errorService: ErrorService
  ) {}

  ngOnInit(): void {
    this.loadBusinessStructure();
  }

  loadBusinessStructure(): void {
    this.isLoading = true;
    
    this.companyService.getAll().pipe(
      catchError(error => {
        this.errorService.handleError(error);
        return of([]);
      })
    ).subscribe(companies => {
      const treeData: BusinessNode[] = companies.map(company => ({
        id: company.id,
        name: company.name,
        type: 'company',
        isActive: company.isActive,
        children: [],
        isLoading: false,
        isExpanded: this.expandByDefault
      }));
      
      this.dataSource.data = treeData;
      this.isLoading = false;
      
      if (this.expandByDefault) {
        this.expandAll();
      }
      
      // If a node is selected, expand to it
      if (this.selectedNodeId && this.selectedNodeType) {
        this.expandToSelectedNode();
      }
    });
  }

  expandAll(): void {
    this.dataSource.data.forEach(node => {
      this.treeControl.expand(node);
      this.loadNodeChildren(node);
    });
  }

  expandToSelectedNode(): void {
    // Implementation will depend on your data structure
    // This is a placeholder for the logic to expand to a specific node
  }

  toggleNode(node: BusinessNode): void {
    if (this.treeControl.isExpanded(node)) {
      this.treeControl.collapse(node);
    } else {
      this.treeControl.expand(node);
      this.loadNodeChildren(node);
    }
  }

  loadNodeChildren(node: BusinessNode): void {
    if ((node.children && node.children.length > 0) || node.isLoading) {
      return; // Children already loaded or loading in progress
    }

    node.isLoading = true;
    
    switch (node.type) {
      case 'company':
        this.locationService.getByCompany(node.id).pipe(
          catchError(error => {
            this.errorService.handleError(error);
            return of([]);
          })
        ).subscribe(locations => {
          node.children = locations.map(location => ({
            id: location.id,
            name: location.name,
            type: 'location',
            isActive: location.isActive,
            children: [],
            isLoading: false,
            isExpanded: this.expandByDefault
          }));
          
          node.isLoading = false;
          this.dataSource.data = [...this.dataSource.data];
          
          if (this.expandByDefault) {
            node.children.forEach(childNode => {
              this.treeControl.expand(childNode);
              this.loadNodeChildren(childNode);
            });
          }
        });
        break;
        
      case 'location':
        this.storeService.getByLocation(node.id).pipe(
          catchError(error => {
            this.errorService.handleError(error);
            return of([]);
          })
        ).subscribe(stores => {
          node.children = stores.map(store => ({
            id: store.id,
            name: store.name,
            type: 'store',
            isActive: store.isActive,
            children: [],
            isLoading: false,
            isExpanded: this.expandByDefault
          }));
          
          node.isLoading = false;
          this.dataSource.data = [...this.dataSource.data];
          
          if (this.expandByDefault) {
            node.children.forEach(childNode => {
              this.treeControl.expand(childNode);
              this.loadNodeChildren(childNode);
            });
          }
        });
        break;
        
      case 'store':
        this.costCenterService.getByStore(node.id).pipe(
          catchError(error => {
            this.errorService.handleError(error);
            return of([]);
          })
        ).subscribe(costCenters => {
          node.children = costCenters.map(costCenter => ({
            id: costCenter.id,
            name: costCenter.name,
            type: 'costCenter',
            isActive: costCenter.isActive,
            children: [],
            isLoading: false
          }));
          
          node.isLoading = false;
          this.dataSource.data = [...this.dataSource.data];
        });
        break;
    }
  }

  selectNode(node: BusinessNode): void {
    this.nodeSelected.emit(node);
  }

  hasChild = (_: number, node: BusinessNode) => 
    node.type !== 'costCenter';

  getNodeIcon(node: BusinessNode): string {
    switch (node.type) {
      case 'company': return 'business';
      case 'location': return 'location_on';
      case 'store': return 'store';
      case 'costCenter': return 'account_balance';
      default: return 'folder';
    }
  }

  isSelected(node: BusinessNode): boolean {
    return this.selectedNodeId === node.id && this.selectedNodeType === node.type;
  }
}
