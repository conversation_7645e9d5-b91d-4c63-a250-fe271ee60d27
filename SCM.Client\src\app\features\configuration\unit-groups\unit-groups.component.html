<div class="unit-groups-container">
  <h1>Unit Groups Management</h1>
  
  <div class="unit-groups-content">
    <!-- Form Card -->
    <mat-card class="form-card">
      <mat-card-header>
        <mat-card-title>{{ isEditMode ? 'Edit Unit Group' : 'Add Unit Group' }}</mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="unitGroupForm" (ngSubmit)="saveUnitGroup()">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter unit group name" required>
              <mat-error *ngIf="unitGroupForm.get('name')?.hasError('required')">
                Name is required
              </mat-error>
              <mat-error *ngIf="unitGroupForm.get('name')?.hasError('maxlength')">
                Name cannot exceed 100 characters
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" placeholder="Enter description" rows="3"></textarea>
              <mat-error *ngIf="unitGroupForm.get('description')?.hasError('maxlength')">
                Description cannot exceed 255 characters
              </mat-error>
            </mat-form-field>
          </div>
          
          <div class="form-field-checkbox" *ngIf="isEditMode">
            <mat-checkbox formControlName="isActive">Active</mat-checkbox>
          </div>
          
          <div class="form-actions">
            <button type="submit" mat-raised-button color="primary" [disabled]="unitGroupForm.invalid || isSubmitting">
              <mat-icon>save</mat-icon>
              {{ isEditMode ? 'Update' : 'Save' }}
            </button>
            
            <button type="button" mat-button (click)="resetForm()" [disabled]="isSubmitting">
              <mat-icon>refresh</mat-icon>
              Reset
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
    
    <!-- Table Card -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>Unit Groups</mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="filter-container">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Filter</mat-label>
            <input matInput (keyup)="applyFilter($event)" placeholder="Search unit groups">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>
        
        <div class="spinner-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
        
        <table mat-table [dataSource]="dataSource" class="full-width" matSort>
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
            <td mat-cell *matCellDef="let unitGroup">{{ unitGroup.id }}</td>
          </ng-container>
          
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let unitGroup">{{ unitGroup.name }}</td>
          </ng-container>
          
          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef>Description</th>
            <td mat-cell *matCellDef="let unitGroup">{{ unitGroup.description }}</td>
          </ng-container>
          
          <!-- Unit Count Column -->
          <ng-container matColumnDef="unitCount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Units</th>
            <td mat-cell *matCellDef="let unitGroup">{{ unitGroup.unitCount }}</td>
          </ng-container>
          
          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let unitGroup">
              <span [ngClass]="unitGroup.isActive ? 'status-active' : 'status-inactive'">
                {{ unitGroup.isActive ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>
          
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let unitGroup">
              <button mat-icon-button color="primary" (click)="viewUnits(unitGroup.id)" 
                      matTooltip="View Units">
                <mat-icon>list</mat-icon>
              </button>
              <button mat-icon-button color="primary" (click)="editUnitGroup(unitGroup)" 
                      [disabled]="!canEdit()" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteUnitGroup(unitGroup.id)" 
                      [disabled]="!canDelete()" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>
          
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          
          <!-- Row shown when there is no matching data -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="6">No data matching the filter</td>
          </tr>
        </table>
        
        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" showFirstLastButtons></mat-paginator>
      </mat-card-content>
    </mat-card>
  </div>
</div>
