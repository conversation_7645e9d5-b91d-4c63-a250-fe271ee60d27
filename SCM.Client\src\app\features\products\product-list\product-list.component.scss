.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-container mat-form-field {
  flex: 1;
  min-width: 200px;
}

.table-container {
  overflow-x: auto;
  border-radius: 4px;
  min-height: 200px;
}

.product-table {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.6);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.empty-state mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: rgba(0, 0, 0, 0.3);
  margin-bottom: 16px;
}

.empty-state p {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.6);
}

.mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.low-stock {
  color: #ff9800;
  font-weight: 500;
}

.status-active {
  color: #4caf50;
  font-weight: 500;
}

.status-low {
  color: #ff9800;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-container mat-form-field {
    width: 100%;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-header button {
    margin-top: 10px;
  }
}
