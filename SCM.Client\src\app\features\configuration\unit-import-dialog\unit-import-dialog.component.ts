import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { UnitService } from '../../../core/services/unit.service';
import { finalize } from 'rxjs';

export interface UnitImportResult {
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: UnitImportError[];
  successfulUnits: string[];
}

export interface UnitImportError {
  rowNumber: number;
  unitName: string;
  unitAbbreviation: string;
  errorMessage: string;
}

@Component({
  selector: 'app-unit-import-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTableModule,
    MatCardModule,
    MatSnackBarModule
  ],
  templateUrl: './unit-import-dialog.component.html',
  styleUrls: ['./unit-import-dialog.component.scss']
})
export class UnitImportDialogComponent {
  selectedFile: File | null = null;
  isUploading = false;
  importResult: UnitImportResult | null = null;
  displayedColumns: string[] = ['rowNumber', 'unitName', 'unitAbbreviation', 'errorMessage'];

  constructor(
    private dialogRef: MatDialogRef<UnitImportDialogComponent>,
    private unitService: UnitService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      if (file.name.endsWith('.xlsx')) {
        this.selectedFile = file;
      } else {
        this.snackBar.open('Please select an Excel (.xlsx) file', 'Close', {
          duration: 3000
        });
      }
    }
  }

  downloadTemplate(): void {
    this.unitService.downloadImportTemplate()
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'UnitImportTemplate.xlsx';
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          console.error('Error downloading template', error);
          this.snackBar.open('Error downloading template', 'Close', {
            duration: 3000
          });
        }
      });
  }

  importUnits(): void {
    if (!this.selectedFile) {
      this.snackBar.open('Please select a file first', 'Close', {
        duration: 3000
      });
      return;
    }

    this.isUploading = true;
    this.importResult = null;

    this.unitService.importUnits(this.selectedFile)
      .pipe(
        finalize(() => {
          this.isUploading = false;
        })
      )
      .subscribe({
        next: (result) => {
          this.importResult = result;
          if (result.errorCount === 0) {
            this.snackBar.open(`Successfully imported ${result.successCount} units`, 'Close', {
              duration: 5000
            });
          } else {
            this.snackBar.open(`Import completed with ${result.errorCount} errors`, 'Close', {
              duration: 5000
            });
          }
        },
        error: (error) => {
          console.error('Error importing units', error);
          this.snackBar.open('Error importing units. Please try again.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  close(): void {
    this.dialogRef.close(this.importResult);
  }
}
