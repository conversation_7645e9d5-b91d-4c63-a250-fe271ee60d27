.credit-note-detail-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

mat-card {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  mat-spinner {
    margin-bottom: 20px;
  }
}

.form-section {
  margin-bottom: 30px;
  
  h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-weight: 500;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
  }
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  
  .form-field {
    flex: 1;
    min-width: 200px;
  }
  
  .full-width {
    width: 100%;
  }
}

.details-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.details-table {
  width: 100%;
  min-width: 800px;
  
  .table-form-field {
    width: 100%;
    
    .mat-mdc-form-field-wrapper {
      padding-bottom: 0;
    }
  }
  
  th {
    font-weight: 600;
    color: #333;
  }
  
  td {
    padding: 8px;
  }
}

.no-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  border: 2px dashed #ddd;
  border-radius: 8px;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.total-section {
  border-top: 1px solid #ddd;
  padding-top: 15px;
  
  .total-row {
    display: flex;
    justify-content: flex-end;
    font-size: 16px;
    color: #333;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  
  button {
    min-width: 120px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .credit-note-detail-container {
    padding: 10px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 15px;
    
    .form-field {
      min-width: unset;
    }
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .details-table {
    min-width: 600px;
  }
  
  .form-actions {
    flex-direction: column-reverse;
    
    button {
      width: 100%;
    }
  }
}

// Material form field customizations
.mat-mdc-form-field {
  .mat-mdc-form-field-wrapper {
    .mat-mdc-form-field-flex {
      .mat-mdc-form-field-infix {
        min-height: 40px;
      }
    }
  }
}

// Table form fields
.table-form-field {
  .mat-mdc-form-field-wrapper {
    padding-bottom: 0 !important;
  }
  
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
}
