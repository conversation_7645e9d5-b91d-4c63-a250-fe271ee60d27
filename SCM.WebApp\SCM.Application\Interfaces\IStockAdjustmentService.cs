using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IStockAdjustmentService
{
    Task<IEnumerable<StockAdjustmentHeaderDto>> GetAllStockAdjustmentsAsync();
    Task<StockAdjustmentHeaderDto?> GetStockAdjustmentByIdAsync(int id);
    Task<IEnumerable<StockAdjustmentHeaderDto>> GetStockAdjustmentsByCostCenterIdAsync(int costCenterId);
    Task<IEnumerable<StockAdjustmentHeaderDto>> GetStockAdjustmentsByStatusAsync(string status);
    Task<StockAdjustmentHeaderDto> CreateStockAdjustmentAsync(CreateStockAdjustmentHeaderDto createStockAdjustmentHeaderDto);
    Task UpdateStockAdjustmentAsync(UpdateStockAdjustmentHeaderDto updateStockAdjustmentHeaderDto);
    Task DeleteStockAdjustmentAsync(int id);
    Task CompleteStockAdjustmentAsync(CompleteStockAdjustmentDto completeStockAdjustmentDto);
    Task CancelStockAdjustmentAsync(int id);
    Task<IEnumerable<StockAdjustmentDetailDto>> GetStockAdjustmentDetailsAsync(int stockAdjustmentHeaderId);
    Task<StockAdjustmentDetailDto?> GetStockAdjustmentDetailByIdAsync(int id);
    Task<StockAdjustmentDetailDto> CreateStockAdjustmentDetailAsync(int stockAdjustmentHeaderId, CreateStockAdjustmentDetailDto createStockAdjustmentDetailDto);
    Task UpdateStockAdjustmentDetailAsync(UpdateStockAdjustmentDetailDto updateStockAdjustmentDetailDto);
    Task DeleteStockAdjustmentDetailAsync(int id);
}
