using System.ComponentModel.DataAnnotations;

namespace SCM.Application.DTOs;

public class GoodsReceiptHeaderDto
{
    public int Id { get; set; }
    public string DocumentNumber { get; set; } = string.Empty;
    public int? PurchaseOrderId { get; set; }
    public string? PurchaseOrderNumber { get; set; }
    public int SupplierId { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime ReceiptDate { get; set; }
    public string? DeliveryNoteNumber { get; set; }
    public string? InvoiceNumber { get; set; }
    public DateTime? InvoiceDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public decimal TotalAmount { get; set; }
    public int? ReceivedById { get; set; }
    public string? ReceivedByName { get; set; }
    public int? ApprovedById { get; set; }
    public string? ApprovedByName { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<GoodsReceiptDetailDto>? Details { get; set; }
}

public class GoodsReceiptListDto
{
    public int Id { get; set; }
    public string DocumentNumber { get; set; } = string.Empty;
    public string? PurchaseOrderNumber { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime ReceiptDate { get; set; }
    public string? InvoiceNumber { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string? ReceivedByName { get; set; }
}

public class GoodsReceiptDetailDto
{
    public int Id { get; set; }
    public int GoodsReceiptHeaderId { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int? PurchaseOrderDetailId { get; set; }
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal OrderedQuantity { get; set; }
    public decimal ReceivedQuantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateGoodsReceiptHeaderDto
{
    public int? PurchaseOrderId { get; set; }
    
    [Required]
    public int SupplierId { get; set; }
    
    [Required]
    public int CostCenterId { get; set; }
    
    [Required]
    public DateTime ReceiptDate { get; set; } = DateTime.UtcNow;
    
    public string? DeliveryNoteNumber { get; set; }
    
    public string? InvoiceNumber { get; set; }
    
    public DateTime? InvoiceDate { get; set; }
    
    public string? Notes { get; set; }
    
    [Required]
    public List<CreateGoodsReceiptDetailDto> Details { get; set; } = new List<CreateGoodsReceiptDetailDto>();
}

public class CreateGoodsReceiptDetailDto
{
    [Required]
    public int ProductId { get; set; }
    
    public int? PurchaseOrderDetailId { get; set; }
    
    public int? BatchId { get; set; }
    
    public int? UnitId { get; set; }
    
    public decimal OrderedQuantity { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Received quantity must be greater than 0")]
    public decimal ReceivedQuantity { get; set; }
    
    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Unit price must be greater than or equal to 0")]
    public decimal UnitPrice { get; set; }
    
    public DateTime? ExpiryDate { get; set; }
    
    public string? Notes { get; set; }
}

public class UpdateGoodsReceiptHeaderDto
{
    [Required]
    public int Id { get; set; }
    
    public int? PurchaseOrderId { get; set; }
    
    [Required]
    public int SupplierId { get; set; }
    
    [Required]
    public int CostCenterId { get; set; }
    
    [Required]
    public DateTime ReceiptDate { get; set; }
    
    public string? DeliveryNoteNumber { get; set; }
    
    public string? InvoiceNumber { get; set; }
    
    public DateTime? InvoiceDate { get; set; }
    
    public string? Status { get; set; }
    
    public string? Notes { get; set; }
}

public class UpdateGoodsReceiptDetailDto
{
    [Required]
    public int Id { get; set; }
    
    [Required]
    public int ProductId { get; set; }
    
    public int? BatchId { get; set; }
    
    public int? UnitId { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Received quantity must be greater than 0")]
    public decimal ReceivedQuantity { get; set; }
    
    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Unit price must be greater than or equal to 0")]
    public decimal UnitPrice { get; set; }
    
    public DateTime? ExpiryDate { get; set; }
    
    public string? Notes { get; set; }
}

public class CompleteGoodsReceiptDto
{
    [Required]
    public int Id { get; set; }
    
    public string? Notes { get; set; }
}
