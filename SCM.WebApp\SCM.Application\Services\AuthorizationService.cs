using Microsoft.EntityFrameworkCore;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;
using System.Text.Json;

namespace SCM.Application.Services;

public interface IUserPermissionService
{
    Task<bool> UserHasPermissionAsync(int userId, string permission);
    Task<bool> UserHasFullReceivePermissionAsync(int userId);
    Task<bool> UserCanApproveTransactionsAsync(int userId);
    Task<bool> UserCanCreateTransactionsAsync(int userId);
    Task<bool> UserCanEditTransactionsAsync(int userId);
    Task<bool> UserCanDeleteTransactionsAsync(int userId);
    Task<bool> UserCanViewTransactionsAsync(int userId);
}

public class UserPermissionService : IUserPermissionService
{
    private readonly ApplicationDbContext _dbContext;

    public UserPermissionService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<bool> UserHasPermissionAsync(int userId, string permission)
    {
        var user = await _dbContext.Users
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive);

        if (user == null)
            return false;

        // Get user's role separately if they have one
        Role? userRole = null;
        if (user.RoleId.HasValue)
        {
            userRole = await _dbContext.Roles
                .FirstOrDefaultAsync(r => r.Id == user.RoleId.Value);
        }

        // Admin users have all permissions
        if (userRole?.Name == "Admin")
            return true;

        // Check role permissions
        if (userRole?.Permissions != null)
        {
            try
            {
                var permissions = JsonSerializer.Deserialize<Dictionary<string, object>>(userRole.Permissions);
                return CheckPermissionInJson(permissions, permission);
            }
            catch (JsonException)
            {
                // If JSON parsing fails, fall back to basic checks
                return false;
            }
        }

        return false;
    }

    public async Task<bool> UserHasFullReceivePermissionAsync(int userId)
    {
        var user = await _dbContext.Users
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive);

        if (user == null)
            return false;

        // Get user's role separately if they have one
        Role? userRole = null;
        if (user.RoleId.HasValue)
        {
            userRole = await _dbContext.Roles
                .FirstOrDefaultAsync(r => r.Id == user.RoleId.Value);
        }

        // Admin users have all permissions
        if (userRole?.Name == "Admin")
            return true;

        // Check for FullReceivePermission in role permissions
        if (userRole?.Permissions != null)
        {
            try
            {
                var permissions = JsonSerializer.Deserialize<Dictionary<string, object>>(userRole.Permissions);
                
                // Check for FullReceivePermission flag
                if (permissions.ContainsKey("FullReceivePermission"))
                {
                    if (permissions["FullReceivePermission"] is JsonElement element && element.ValueKind == JsonValueKind.True)
                        return true;
                    if (permissions["FullReceivePermission"] is bool boolValue && boolValue)
                        return true;
                }

                // Check for transactions.create permission as fallback
                return CheckPermissionInJson(permissions, "transactions.create");
            }
            catch (JsonException)
            {
                return false;
            }
        }

        return false;
    }

    public async Task<bool> UserCanApproveTransactionsAsync(int userId)
    {
        return await UserHasPermissionAsync(userId, "canApprove") || 
               await UserHasPermissionAsync(userId, "transactions.approve");
    }

    public async Task<bool> UserCanCreateTransactionsAsync(int userId)
    {
        return await UserHasPermissionAsync(userId, "canCreate") || 
               await UserHasPermissionAsync(userId, "transactions.create");
    }

    public async Task<bool> UserCanEditTransactionsAsync(int userId)
    {
        return await UserHasPermissionAsync(userId, "canEdit") || 
               await UserHasPermissionAsync(userId, "transactions.edit");
    }

    public async Task<bool> UserCanDeleteTransactionsAsync(int userId)
    {
        return await UserHasPermissionAsync(userId, "canDelete") || 
               await UserHasPermissionAsync(userId, "transactions.delete");
    }

    public async Task<bool> UserCanViewTransactionsAsync(int userId)
    {
        return await UserHasPermissionAsync(userId, "canView") || 
               await UserHasPermissionAsync(userId, "transactions.view");
    }

    private bool CheckPermissionInJson(Dictionary<string, object> permissions, string permission)
    {
        // Handle simple boolean permissions (e.g., "canView": true)
        if (permissions.ContainsKey(permission))
        {
            var value = permissions[permission];
            if (value is JsonElement element && element.ValueKind == JsonValueKind.True)
                return true;
            if (value is bool boolValue && boolValue)
                return true;
        }

        // Handle nested permissions (e.g., "transactions": {"view": true})
        var parts = permission.Split('.');
        if (parts.Length == 2)
        {
            var category = parts[0];
            var action = parts[1];

            if (permissions.ContainsKey(category))
            {
                var categoryValue = permissions[category];
                if (categoryValue is JsonElement categoryElement && categoryElement.ValueKind == JsonValueKind.Object)
                {
                    if (categoryElement.TryGetProperty(action, out var actionElement) && 
                        actionElement.ValueKind == JsonValueKind.True)
                        return true;
                }
            }
        }

        return false;
    }
}
