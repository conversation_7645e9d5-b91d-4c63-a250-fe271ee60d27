import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTableDataSource } from '@angular/material/table';
import { Observable, forkJoin, map, of, startWith } from 'rxjs';
import { TransactionService } from '../../../core/services/transaction.service';
import { PurchaseOrderService } from '../../../core/services/purchase-order.service';
import { ProductService } from '../../../core/services/product.service';
import { SupplierService } from '../../../core/services/supplier.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { UnitService } from '../../../core/services/unit.service';
import { ErrorService } from '../../../core/services/error.service';
import { Product } from '../../../core/models/product.model';
import { SupplierListItem } from '../../../core/services/supplier.service';
import { CostCenterList } from '../../../core/models/cost-center.model';
import { Unit } from '../../../core/models/unit.model';
import { CreateReceiving, TransactionHeader } from '../../../core/models/transaction.model';
import { TransactionAdapter } from '../../../core/adapters/transaction-adapter';

@Component({
  selector: 'app-receiving',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    MatCheckboxModule
  ],
  templateUrl: './receiving.component.html',
  styleUrls: ['./receiving.component.scss']
})
export class ReceivingComponent implements OnInit {
  receivingForm!: FormGroup;
  isLoading = false;
  isEditMode = false;
  receivingId: number | null = null;
  purchaseOrderId: number | null = null;

  displayedColumns: string[] = [
    'select',
    'productCode',
    'productName',
    'unitName',
    'orderedQuantity',
    'receivedQuantity',
    'unitPrice',
    'batchNumber',
    'expiryDate',
    'total',
    'actions'
  ];

  products: Product[] = [];
  suppliers: SupplierListItem[] = [];
  costCenters: CostCenterList[] = [];
  units: Unit[] = [];
  filteredProducts: Observable<Product[]>[] = [];
  dataSource = new MatTableDataSource<any>([]);

  constructor(
    private fb: FormBuilder,
    private transactionService: TransactionService,
    private purchaseOrderService: PurchaseOrderService,
    private productService: ProductService,
    private supplierService: SupplierService,
    private costCenterService: CostCenterService,
    private unitService: UnitService,
    private errorService: ErrorService,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    console.log('Receiving component initialized');
    this.initForm();

    // Load reference data first, then handle routing
    this.loadReferenceData().then(() => {
      console.log('Reference data loaded');

      // Check if we're editing an existing receiving or creating from order
      this.route.paramMap.subscribe(params => {
        const id = params.get('id');
        const orderId = params.get('orderId');

        console.log('Route params:', { id, orderId });

        // Reset state for new receiving
        this.resetComponentState();

        if (id && id !== 'new') {
          this.isEditMode = true;
          this.receivingId = parseInt(id, 10);
          this.loadReceiving(this.receivingId);
        } else if (orderId) {
          // Creating from purchase order via route parameter
          this.purchaseOrderId = parseInt(orderId, 10);
          this.loadPurchaseOrder(this.purchaseOrderId);
        } else {
          // This is a new receiving - add an empty row
          console.log('Adding empty item for new receiving');
          this.addItem();
          console.log('Details length after addItem:', this.details.length);
        }
      });

      // Also check query params for orderId
      this.route.queryParams.subscribe(params => {
        if (params['orderId'] && !this.purchaseOrderId) {
          this.purchaseOrderId = +params['orderId'];
          this.loadPurchaseOrder(this.purchaseOrderId);
        }
      });
    }).catch(error => {
      console.error('Error loading reference data:', error);
    });
  }

  initForm(): void {
    this.receivingForm = this.fb.group({
      sourceCostCenterId: ['', Validators.required],
      supplierId: ['', Validators.required],
      transactionDate: [new Date(), Validators.required],
      referenceNumber: [''],
      invoiceNumber: [''],
      deliveryNoteNumber: [''],
      notes: [''],
      details: this.fb.array([])
    });
  }

  resetComponentState(): void {
    // Reset all component state for new receiving
    this.receivingId = null;
    this.purchaseOrderId = null;
    this.isEditMode = false;
    this.isLoading = false;

    // Clear the details array
    const detailsArray = this.details;
    while (detailsArray.length !== 0) {
      detailsArray.removeAt(0);
    }

    // Reset form to initial state
    this.receivingForm.reset({
      sourceCostCenterId: '',
      supplierId: '',
      transactionDate: new Date(),
      referenceNumber: '',
      invoiceNumber: '',
      deliveryNoteNumber: '',
      notes: ''
    });
  }

  get details(): FormArray {
    return this.receivingForm.get('details') as FormArray;
  }

  private updateDataSource(): void {
    this.dataSource.data = this.details.controls;
  }

  addItem(): void {
    console.log('addItem() called');
    const detailForm = this.fb.group({
      selected: [true],
      productId: ['', Validators.required],
      productCode: ['', Validators.required],
      productName: [''],
      unitId: ['', Validators.required],
      unitName: [''],
      orderedQuantity: [{ value: 0, disabled: true }],
      receivedQuantity: [0, [Validators.required, Validators.min(0)]],
      unitPrice: [0, [Validators.required, Validators.min(0)]],
      batchNumber: [''],
      expiryDate: [null],
      total: [{ value: 0, disabled: true }]
    });

    console.log('Created detail form:', detailForm.value);

    // Set up product autocomplete filtering
    const index = this.details.length;
    console.log('Setting up autocomplete for index:', index);
    this.setupProductAutocomplete(detailForm, index);

    // Auto-calculate total when quantity or unit price changes
    detailForm.get('receivedQuantity')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(detailForm);
    });

    detailForm.get('unitPrice')?.valueChanges.subscribe(() => {
      this.calculateItemTotal(detailForm);
    });

    this.details.push(detailForm);

    // Update the data source for the table
    this.updateDataSource();
  }

  removeItem(index: number): void {
    this.details.removeAt(index);
    this.filteredProducts.splice(index, 1);
    this.updateDataSource();
  }

  loadReferenceData(): Promise<void> {
    this.isLoading = true;

    return new Promise((resolve, reject) => {
      forkJoin({
        products: this.productService.getAll(),
        suppliers: this.supplierService.getAll(),
        costCenters: this.costCenterService.getAll(),
        units: this.unitService.getAll()
      }).subscribe({
        next: (data) => {
          this.products = data.products;
          this.suppliers = data.suppliers;
          this.costCenters = data.costCenters;
          this.units = data.units;
          this.isLoading = false;
          resolve();
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
          reject(error);
        }
      });
    });
  }

  loadPurchaseOrder(orderId: number): void {
    this.isLoading = true;
    // Use the purchase order service to get the purchase order with proper details
    this.purchaseOrderService.getById(orderId).subscribe({
      next: (transaction: TransactionHeader) => {
        // Convert to purchase order format
        const order = TransactionAdapter.toPurchaseOrder(transaction);
        console.log('Converted purchase order:', order);

        // Generate a proper reference number for the receiving
        const orderRef = order.documentNumber || `PO-${orderId}`;
        const receivingRef = `GR-${orderRef}`;

        // Set the form values
        this.receivingForm.patchValue({
          sourceCostCenterId: order.costCenterId,
          supplierId: order.supplierId,
          transactionDate: new Date(),
          referenceNumber: receivingRef,
          notes: `Receiving for Purchase Order: ${orderRef}`
        });

        // Clear existing items
        while (this.details.length) {
          this.details.removeAt(0);
        }
        this.filteredProducts = [];

        // Add items from the order
        console.log('Order details:', order.details);
        if (order.details && order.details.length > 0) {
          order.details.forEach((detail, index) => {
          console.log(`Detail ${index}:`, detail);

          const detailForm = this.fb.group({
            selected: [true],
            productId: [detail.productId, Validators.required],
            productCode: [detail.productCode],
            productName: [detail.productName],
            unitId: [detail.unitId, Validators.required],
            unitName: [detail.unitName],
            orderedQuantity: [{ value: detail.quantity, disabled: true }],
            receivedQuantity: [detail.quantity, [Validators.required, Validators.min(0)]],
            unitPrice: [detail.unitPrice || 0, [Validators.required, Validators.min(0)]],
            batchNumber: [''],
            expiryDate: [null],
            total: [{ value: 0, disabled: true }]
          });

          console.log('Created detail form:', detailForm.value);

          // Set up product autocomplete filtering
          const formIndex = this.details.length;
          this.setupProductAutocomplete(detailForm, formIndex);

          // Auto-calculate total when quantity or unit price changes
          detailForm.get('receivedQuantity')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          detailForm.get('unitPrice')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          this.details.push(detailForm);
          this.calculateItemTotal(detailForm);
          });
        }

        // Update the data source for the table
        this.updateDataSource();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorService.handleError(error);
        this.isLoading = false;
      }
    });
  }

  loadReceiving(id: number): void {
    this.isLoading = true;
    this.transactionService.getReceivingById(id).subscribe({
      next: (receiving) => {
        // Set the form values
        this.receivingForm.patchValue({
          sourceCostCenterId: receiving.sourceCostCenterId,
          supplierId: receiving.supplierId,
          transactionDate: new Date(receiving.transactionDate),
          referenceNumber: receiving.referenceNumber,
          invoiceNumber: '', // These fields are not stored in the transaction system yet
          deliveryNoteNumber: '', // These fields are not stored in the transaction system yet
          notes: receiving.notes
        });

        // Clear existing items
        while (this.details.length) {
          this.details.removeAt(0);
        }
        this.filteredProducts = [];

        // Add items from the receiving
        if (receiving.details && receiving.details.length > 0) {
          receiving.details.forEach((detail) => {
          const detailForm = this.fb.group({
            selected: [true],
            productId: [detail.productId, Validators.required],
            productCode: [detail.productCode],
            productName: [detail.productName],
            unitId: [detail.unitId, Validators.required],
            unitName: [detail.unitName],
            orderedQuantity: [{ value: 0, disabled: true }],
            receivedQuantity: [detail.quantity, [Validators.required, Validators.min(0)]],
            unitPrice: [detail.unitPrice || 0, [Validators.required, Validators.min(0)]],
            batchNumber: [detail.batchNumber || ''],
            expiryDate: [detail.expiryDate ? new Date(detail.expiryDate) : null],
            total: [{ value: 0, disabled: true }]
          });

          // Set up product autocomplete filtering
          const formIndex = this.details.length;
          this.setupProductAutocomplete(detailForm, formIndex);

          // Auto-calculate total when quantity or unit price changes
          detailForm.get('receivedQuantity')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          detailForm.get('unitPrice')?.valueChanges.subscribe(() => {
            this.calculateItemTotal(detailForm);
          });

          this.details.push(detailForm);
          this.calculateItemTotal(detailForm);
          });
        }

        // Update the data source for the table
        this.updateDataSource();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorService.handleError(error);
        this.isLoading = false;
      }
    });
  }

  setupProductAutocomplete(form: FormGroup, index: number): void {
    // Initialize the filtered products array for this index if it doesn't exist
    if (!this.filteredProducts[index]) {
      this.filteredProducts[index] = of([]);
    }

    // Get the current value of the productCode field
    const currentValue = form.get('productCode')?.value || '';

    // Set up the filter
    this.filteredProducts[index] = form.get('productCode')!.valueChanges.pipe(
      startWith(currentValue), // Use the current value instead of empty string
      map(value => {
        const filterValue = (value || '').toLowerCase();
        return this.products.filter(product =>
          product.code.toLowerCase().includes(filterValue) ||
          product.name.toLowerCase().includes(filterValue)
        );
      })
    );
  }

  selectProduct(index: number, product: Product): void {
    const detailForm = this.details.at(index) as FormGroup;

    detailForm.patchValue({
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      unitId: product.unitId,
      unitName: this.units.find(u => u.id === product.unitId)?.name || '',
      unitPrice: product.costPrice || 0
    });

    this.calculateItemTotal(detailForm);
  }

  calculateItemTotal(form: FormGroup): void {
    const quantity = form.get('receivedQuantity')?.value || 0;
    const unitPrice = form.get('unitPrice')?.value || 0;

    const total = quantity * unitPrice;
    form.get('total')?.setValue(total, { emitEvent: false });
  }

  calculateReceivingTotal(): number {
    let total = 0;
    for (let i = 0; i < this.details.length; i++) {
      const detailForm = this.details.at(i) as FormGroup;
      if (detailForm.get('selected')?.value) {
        const quantity = detailForm.get('receivedQuantity')?.value || 0;
        const unitPrice = detailForm.get('unitPrice')?.value || 0;
        total += quantity * unitPrice;
      }
    }
    return total;
  }

  saveReceiving(): void {
    if (this.receivingForm.invalid) {
      this.markFormGroupTouched(this.receivingForm);
      this.snackBar.open('Please fix the errors in the form before saving.', 'Close', { duration: 3000 });
      return;
    }

    // Check if any items are selected
    const selectedItems = this.details.controls.filter(control => control.get('selected')?.value);
    if (selectedItems.length === 0) {
      this.snackBar.open('Please select at least one item to receive.', 'Close', { duration: 3000 });
      return;
    }

    this.isLoading = true;

    const formValue = this.receivingForm.getRawValue();

    // Make sure details exist and are properly formatted
    if (!formValue.details || !Array.isArray(formValue.details)) {
      this.snackBar.open('Error: Details are missing or invalid', 'Close', { duration: 3000 });
      this.isLoading = false;
      return;
    }

    const receiving: CreateReceiving = {
      sourceCostCenterId: formValue.sourceCostCenterId,
      supplierId: formValue.supplierId,
      transactionDate: formValue.transactionDate,
      referenceNumber: formValue.referenceNumber || '', // Let backend generate reference number if empty
      invoiceNumber: formValue.invoiceNumber,
      deliveryNoteNumber: formValue.deliveryNoteNumber,
      notes: formValue.notes,
      details: formValue.details
        .filter((detail: any) => detail.selected)
        .map((detail: any) => ({
          productId: detail.productId,
          unitId: detail.unitId,
          quantity: detail.receivedQuantity,
          unitPrice: detail.unitPrice,
          notes: detail.notes || ''
        }))
    };


    if (this.isEditMode && this.receivingId) {
      // Update existing receiving - only send header data for updates
      const updateReceiving = {
        id: this.receivingId,
        sourceCostCenterId: formValue.sourceCostCenterId,
        supplierId: formValue.supplierId,
        transactionDate: formValue.transactionDate,
        invoiceNumber: formValue.invoiceNumber,
        deliveryNoteNumber: formValue.deliveryNoteNumber,
        notes: formValue.notes
      };


      this.transactionService.updateReceiving(this.receivingId, updateReceiving).subscribe({
        next: () => {
          this.snackBar.open('Receiving updated successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          // Stay on the same page after saving
          this.loadReceiving(this.receivingId!);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    } else if (this.purchaseOrderId) {
      // Create from purchase order
      this.transactionService.createReceivingFromOrder(this.purchaseOrderId, receiving).subscribe({
        next: (response) => {
          this.snackBar.open('Receiving created successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          // Navigate to edit mode for the newly created receiving
          this.receivingId = response.id;
          this.isEditMode = true;
          this.router.navigate(['/transactions/receiving', response.id, 'edit'], { replaceUrl: true });
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    } else {
      // Create new receiving
      this.transactionService.createReceiving(receiving).subscribe({
        next: (response) => {
          this.snackBar.open('Receiving created successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          // Navigate to edit mode for the newly created receiving
          this.receivingId = response.id;
          this.isEditMode = true;
          this.router.navigate(['/transactions/receiving', response.id, 'edit'], { replaceUrl: true });
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    }
  }

  completeReceiving(): void {
    if (!this.receivingId) {
      this.snackBar.open('Please save the receiving before completing.', 'Close', { duration: 3000 });
      return;
    }

    if (confirm('Are you sure you want to complete this receiving? This will update inventory levels and cannot be undone.')) {
      this.isLoading = true;
      this.transactionService.completeReceiving(this.receivingId).subscribe({
        next: () => {
          this.snackBar.open('Receiving completed successfully. Inventory has been updated.', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/transactions/receiving']);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    }
  }

  cancelReceiving(): void {
    if (!this.receivingId) {
      this.router.navigate(['/transactions/receiving']);
      return;
    }

    if (confirm('Are you sure you want to cancel this receiving?')) {
      this.isLoading = true;
      this.transactionService.cancelReceiving(this.receivingId).subscribe({
        next: () => {
          this.snackBar.open('Receiving cancelled successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/transactions/receiving']);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        for (let i = 0; i < control.length; i++) {
          const arrayControl = control.at(i);
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        }
      }
    });
  }

  submitReceiving(): void {
    if (this.receivingForm.invalid) {
      this.markFormGroupTouched(this.receivingForm);
      this.snackBar.open('Please fix the errors in the form before submitting.', 'Close', { duration: 3000 });
      return;
    }

    // Check if any items are selected
    const selectedItems = this.details.controls.filter(control => control.get('selected')?.value);
    if (selectedItems.length === 0) {
      this.snackBar.open('Please select at least one item to receive.', 'Close', { duration: 3000 });
      return;
    }

    if (!this.receivingId) {
      this.snackBar.open('Please save the receiving before submitting.', 'Close', { duration: 3000 });
      return;
    }

    if (confirm('Are you sure you want to submit this receiving?')) {
      this.isLoading = true;
      this.transactionService.submitReceiving(this.receivingId).subscribe({
        next: () => {
          this.snackBar.open('Receiving submitted successfully.', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/transactions/receiving']);
        },
        error: (error) => {
          this.errorService.handleError(error);
          this.isLoading = false;
        }
      });
    }
  }

  canSubmit(): boolean {
    return this.isEditMode &&
           this.receivingId !== null &&
           this.receivingForm.valid &&
           this.details.length > 0 &&
           this.details.controls.some(control => control.get('selected')?.value);
  }

  canComplete(): boolean {
    return this.isEditMode &&
           this.receivingId !== null &&
           this.receivingForm.valid &&
           this.details.length > 0 &&
           this.details.controls.some(control => control.get('selected')?.value);
  }

  goBack(): void {
    this.router.navigate(['/transactions/receiving']);
  }
}