namespace SCM.Application.DTOs;

public class StockRequestHeaderDto
{
    public int Id { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public int FromCostCenterId { get; set; }
    public string FromCostCenterName { get; set; } = string.Empty;
    public int ToCostCenterId { get; set; }
    public string ToCostCenterName { get; set; } = string.Empty;
    public DateTime RequestDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = string.Empty;
    public int? CreatedById { get; set; }
    public string? CreatedByName { get; set; }
    public int? ApprovedById { get; set; }
    public string? ApprovedByName { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public int? CompletedById { get; set; }
    public string? CompletedByName { get; set; }
    public DateTime? CompletedAt { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<StockRequestDetailDto>? Details { get; set; }
}

public class CreateStockRequestHeaderDto
{
    public int FromCostCenterId { get; set; }
    public int ToCostCenterId { get; set; }
    public DateTime RequestDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    public List<CreateStockRequestDetailDto> Details { get; set; } = new List<CreateStockRequestDetailDto>();
}

public class UpdateStockRequestHeaderDto
{
    public int Id { get; set; }
    public int FromCostCenterId { get; set; }
    public int ToCostCenterId { get; set; }
    public DateTime RequestDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft";
}

public class SubmitStockRequestDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class ApproveStockRequestDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class RejectStockRequestDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class CompleteStockRequestDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class StockRequestDetailDto
{
    public int Id { get; set; }
    public int StockRequestHeaderId { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal Quantity { get; set; }
    public decimal? Price { get; set; }
    public decimal? Total { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateStockRequestDetailDto
{
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? Price { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? Notes { get; set; }
}

public class UpdateStockRequestDetailDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? Price { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? Notes { get; set; }
}
