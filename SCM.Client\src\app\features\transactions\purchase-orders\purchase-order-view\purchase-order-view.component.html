<div class="page-container">
  <div class="page-header">
    <h1>
      Purchase Order: {{purchaseOrder?.documentNumber}}
      <span *ngIf="purchaseOrder?.status" class="status-chip" [ngClass]="'status-' + purchaseOrder?.status?.toLowerCase()">
        {{purchaseOrder?.status}}
      </span>
    </h1>
    <div class="header-actions">
      <button mat-button (click)="goBack()">Back</button>
      <ng-container *ngIf="purchaseOrder?.status === 'Draft'">
        <button mat-raised-button color="primary" (click)="submitPurchaseOrder()" [disabled]="isLoading">
          <mat-icon>send</mat-icon>
          Submit
        </button>
        <button mat-raised-button color="warn" (click)="cancelPurchaseOrder()" [disabled]="isLoading">
          <mat-icon>cancel</mat-icon>
          Cancel
        </button>
      </ng-container>
      <ng-container *ngIf="purchaseOrder?.status === 'Pending'">
        <button mat-raised-button color="primary" (click)="approvePurchaseOrder()" [disabled]="isLoading">
          <mat-icon>check</mat-icon>
          Approve
        </button>
        <button mat-raised-button color="warn" (click)="rejectPurchaseOrder()" [disabled]="isLoading">
          <mat-icon>close</mat-icon>
          Reject
        </button>
      </ng-container>
      <ng-container *ngIf="purchaseOrder?.status === 'Approved' || purchaseOrder?.status === 'Partially Received'">
        <button mat-raised-button color="accent" (click)="createReceiving()" [disabled]="isLoading">
          <mat-icon>inventory</mat-icon>
          Receive
        </button>
        <button mat-raised-button color="warn" (click)="cancelPurchaseOrder()" [disabled]="isLoading" *ngIf="purchaseOrder?.status === 'Approved'">
          <mat-icon>cancel</mat-icon>
          Cancel
        </button>
      </ng-container>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div *ngIf="!isLoading && purchaseOrder">
    <!-- Purchase Order Information -->
    <mat-card>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-row">
            <div class="info-label">Supplier:</div>
            <div class="info-value">{{purchaseOrder.supplierName || 'N/A'}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Cost Center:</div>
            <div class="info-value">{{purchaseOrder.costCenterName || 'N/A'}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Order Date:</div>
            <div class="info-value">{{purchaseOrder.orderDate | date:'mediumDate'}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Expected Delivery Date:</div>
            <div class="info-value">{{purchaseOrder.expectedDeliveryDate ? (purchaseOrder.expectedDeliveryDate | date:'mediumDate') : 'N/A'}}</div>
          </div>
          <div class="info-row" *ngIf="purchaseOrder.notes">
            <div class="info-label">Notes:</div>
            <div class="info-value notes-text">{{purchaseOrder.notes}}</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Order Items -->
    <div class="section-header">
      <h2>Order Items</h2>
    </div>

    <div class="table-container mat-elevation-z2">
      <div *ngIf="!purchaseOrder.details || purchaseOrder.details.length === 0" class="no-data-message">
        No items added to this purchase order.
      </div>

      <table mat-table [dataSource]="purchaseOrder.details" *ngIf="purchaseOrder.details && purchaseOrder.details.length > 0">
        <!-- Product Column -->
        <ng-container matColumnDef="productName">
          <th mat-header-cell *matHeaderCellDef>Product</th>
          <td mat-cell *matCellDef="let detail">{{detail.productName || 'Unknown Product'}}</td>
        </ng-container>

        <!-- Quantity Column -->
        <ng-container matColumnDef="quantity">
          <th mat-header-cell *matHeaderCellDef>Quantity</th>
          <td mat-cell *matCellDef="let detail">{{detail.quantity}}</td>
        </ng-container>

        <!-- Unit Price Column -->
        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef>Unit Price</th>
          <td mat-cell *matCellDef="let detail">{{detail.unitPrice | currency}}</td>
        </ng-container>

        <!-- Total Price Column -->
        <ng-container matColumnDef="totalPrice">
          <th mat-header-cell *matHeaderCellDef>Total Price</th>
          <td mat-cell *matCellDef="let detail">{{detail.totalPrice | currency}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <div class="order-total" *ngIf="purchaseOrder.details && purchaseOrder.details.length > 0">
      <span>Total:</span>
      <span class="total-amount">{{purchaseOrder.totalAmount | currency}}</span>
    </div>
  </div>
</div>
