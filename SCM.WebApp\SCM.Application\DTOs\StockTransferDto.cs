namespace SCM.Application.DTOs;

public class StockTransferHeaderDto
{
    public int Id { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public int FromCostCenterId { get; set; }
    public string FromCostCenterName { get; set; } = string.Empty;
    public int ToCostCenterId { get; set; }
    public string ToCostCenterName { get; set; } = string.Empty;
    public DateTime TransferDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = string.Empty;
    public int? CreatedById { get; set; }
    public string? CreatedByName { get; set; }
    public int? CompletedById { get; set; }
    public string? CompletedByName { get; set; }
    public DateTime? CompletedAt { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<StockTransferDetailDto>? Details { get; set; }
}

public class CreateStockTransferHeaderDto
{
    public int FromCostCenterId { get; set; }
    public int ToCostCenterId { get; set; }
    public DateTime TransferDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    public List<CreateStockTransferDetailDto> Details { get; set; } = new List<CreateStockTransferDetailDto>();
}

public class UpdateStockTransferHeaderDto
{
    public int Id { get; set; }
    public int FromCostCenterId { get; set; }
    public int ToCostCenterId { get; set; }
    public DateTime TransferDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft";
}

public class CompleteStockTransferDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class StockTransferDetailDto
{
    public int Id { get; set; }
    public int StockTransferHeaderId { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal Quantity { get; set; }
    public decimal? CostPrice { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateStockTransferDetailDto
{
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public string? Notes { get; set; }
}

public class UpdateStockTransferDetailDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public string? Notes { get; set; }
}
