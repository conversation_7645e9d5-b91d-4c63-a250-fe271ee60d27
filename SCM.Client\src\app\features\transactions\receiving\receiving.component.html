<div class="page-container">
  <div class="page-header">
    <h1>{{ isEditMode ? 'Edit Receiving' : 'New Receiving' }}</h1>
    <div class="header-actions">
      <button mat-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back
      </button>
      <button mat-raised-button color="primary" (click)="saveReceiving()" [disabled]="isLoading">
        <mat-icon>save</mat-icon> Save
      </button>
      <button mat-raised-button color="accent" (click)="submitReceiving()" [disabled]="isLoading || !canSubmit()">
        <mat-icon>send</mat-icon> Submit
      </button>
      <button mat-raised-button color="accent" (click)="completeReceiving()" [disabled]="isLoading || !canComplete()">
        <mat-icon>check_circle</mat-icon> Complete
      </button>
      <button mat-button color="warn" (click)="cancelReceiving()" [disabled]="isLoading">
        <mat-icon>cancel</mat-icon> Cancel
      </button>
    </div>
  </div>

  <div class="spinner-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <form [formGroup]="receivingForm" class="receiving-form">
    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Cost Center</mat-label>
        <mat-select formControlName="sourceCostCenterId" required>
          <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
            {{ costCenter.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="receivingForm.get('sourceCostCenterId')?.hasError('required')">
          Cost center is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Supplier</mat-label>
        <mat-select formControlName="supplierId" required>
          <mat-option *ngFor="let supplier of suppliers" [value]="supplier.id">
            {{ supplier.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="receivingForm.get('supplierId')?.hasError('required')">
          Supplier is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Receiving Date</mat-label>
        <input matInput [matDatepicker]="receivingDatePicker" formControlName="transactionDate" required>
        <mat-datepicker-toggle matSuffix [for]="receivingDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #receivingDatePicker></mat-datepicker>
        <mat-error *ngIf="receivingForm.get('transactionDate')?.hasError('required')">
          Receiving date is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Reference Number</mat-label>
        <input matInput formControlName="referenceNumber" placeholder="Enter reference number">
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Invoice Number</mat-label>
        <input matInput formControlName="invoiceNumber" placeholder="Enter invoice number">
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Delivery Note Number</mat-label>
        <input matInput formControlName="deliveryNoteNumber" placeholder="Enter delivery note number">
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field full-width">
        <mat-label>Notes</mat-label>
        <textarea matInput formControlName="notes" rows="3" placeholder="Enter notes"></textarea>
      </mat-form-field>
    </div>

    <div class="section-header">
      <h2>Receiving Items</h2>
      <button mat-mini-fab color="primary" (click)="addItem()" type="button">
        <mat-icon>add</mat-icon>
      </button>
    </div>



    <div class="table-container mat-elevation-z2">
      <table mat-table [dataSource]="dataSource" class="receiving-items-table">
        <!-- Select Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>Select</th>
          <td mat-cell *matCellDef="let item">
            <mat-checkbox [formControl]="item.get('selected')"></mat-checkbox>
          </td>
        </ng-container>

        <!-- Product Code Column -->
        <ng-container matColumnDef="productCode">
          <th mat-header-cell *matHeaderCellDef>Product Code</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productCode')"
                     [matAutocomplete]="auto" placeholder="Enter product code"
                     [readonly]="purchaseOrderId !== null || isEditMode">
              <mat-autocomplete #auto="matAutocomplete">
                <mat-option *ngFor="let product of filteredProducts[i] | async"
                           [value]="product.code"
                           (click)="selectProduct(i, product)">
                  {{product.code}} - {{product.name}}
                </mat-option>
              </mat-autocomplete>
              <mat-error *ngIf="item.get('productCode')?.hasError('required')">
                Product code is required
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Product Name Column -->
        <ng-container matColumnDef="productName">
          <th mat-header-cell *matHeaderCellDef>Product Name</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('productName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Unit Column -->
        <ng-container matColumnDef="unitName">
          <th mat-header-cell *matHeaderCellDef>Unit</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('unitName')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Ordered Quantity Column -->
        <ng-container matColumnDef="orderedQuantity">
          <th mat-header-cell *matHeaderCellDef>Ordered Qty</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('orderedQuantity')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Received Quantity Column -->
        <ng-container matColumnDef="receivedQuantity">
          <th mat-header-cell *matHeaderCellDef>Received Qty</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('receivedQuantity')" min="0" step="0.01">
              <mat-error *ngIf="item.get('receivedQuantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="item.get('receivedQuantity')?.hasError('min')">
                Quantity must be greater than or equal to 0
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Unit Price Column -->
        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef>Unit Price</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput type="number" [formControl]="item.get('unitPrice')" min="0" step="0.01">
              <mat-error *ngIf="item.get('unitPrice')?.hasError('required')">
                Unit price is required
              </mat-error>
              <mat-error *ngIf="item.get('unitPrice')?.hasError('min')">
                Unit price must be greater than or equal to 0
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Batch Number Column -->
        <ng-container matColumnDef="batchNumber">
          <th mat-header-cell *matHeaderCellDef>Batch #</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('batchNumber')">
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Expiry Date Column -->
        <ng-container matColumnDef="expiryDate">
          <th mat-header-cell *matHeaderCellDef>Expiry Date</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [matDatepicker]="expiryDatePicker" [formControl]="item.get('expiryDate')">
              <mat-datepicker-toggle matSuffix [for]="expiryDatePicker"></mat-datepicker-toggle>
              <mat-datepicker #expiryDatePicker></mat-datepicker>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Total Column -->
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef>Total</th>
          <td mat-cell *matCellDef="let item">
            <mat-form-field appearance="outline" class="table-form-field">
              <input matInput [formControl]="item.get('total')" readonly>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let item; let i = index">
            <button mat-icon-button color="warn" (click)="removeItem(i)" type="button" *ngIf="!purchaseOrderId">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <div class="receiving-summary">
      <div class="receiving-total">
        <span>Total Amount:</span>
        <span class="total-value">{{ calculateReceivingTotal() | currency }}</span>
      </div>
    </div>
  </form>
</div>
