import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { StockRequestService } from '../../../../core/services/stock-request.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductService } from '../../../../core/services/product.service';
import { StockService } from '../../../../core/services/stock.service';
import { UnitService } from '../../../../core/services/unit.service';
import {
  StockRequestHeader,
  StockRequestDetail,
  CreateStockRequestHeader,
  CreateStockRequestDetail
} from '../../../../core/models/stock-request.model';
import { CostCenter } from '../../../../core/models/cost-center.model';
import { Product } from '../../../../core/models/product.model';
import { StockOnHand } from '../../../../core/models/stock.model';
import { Unit } from '../../../../core/models/unit.model';
import { Observable, forkJoin, of, map, startWith, switchMap, finalize } from 'rxjs';

@Component({
  selector: 'app-stock-request-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatAutocompleteModule,
    MatTooltipModule,
    MatDividerModule
  ],
  templateUrl: './stock-request-detail.component.html',
  styleUrls: ['./stock-request-detail.component.scss']
})
export class StockRequestDetailComponent implements OnInit {
  requestForm!: FormGroup;
  isLoading = false;
  isEditMode = false;
  stockRequestId: number | null = null;
  stockRequest: StockRequestHeader | null = null;

  costCenters: CostCenter[] = [];
  products: Product[] = [];
  units: Unit[] = [];

  displayedColumns: string[] = [
    'productName',
    'quantity',
    'unitName',
    'price',
    'total',
    'deliveryDate',
    'actions'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private stockRequestService: StockRequestService,
    private costCenterService: CostCenterService,
    private productService: ProductService,
    private stockService: StockService,
    private unitService: UnitService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadData();

    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.stockRequestId = +id;
        this.isEditMode = true;
        this.loadStockRequest(this.stockRequestId);
      }
    });
  }

  get detailsFormArray(): FormArray {
    return this.requestForm.get('details') as FormArray;
  }

  initForm(): void {
    this.requestForm = this.fb.group({
      fromCostCenterId: ['', Validators.required],
      toCostCenterId: ['', Validators.required],
      requestDate: [new Date(), Validators.required],
      notes: [''],
      details: this.fb.array([])
    });

    // Add validation to prevent selecting the same cost center
    this.requestForm.get('fromCostCenterId')?.valueChanges.subscribe(value => {
      const toCostCenterId = this.requestForm.get('toCostCenterId')?.value;
      if (value && toCostCenterId && value === toCostCenterId) {
        this.requestForm.get('toCostCenterId')?.setErrors({ sameCostCenter: true });
      } else {
        const errors = this.requestForm.get('toCostCenterId')?.errors;
        if (errors) {
          delete errors['sameCostCenter'];
          this.requestForm.get('toCostCenterId')?.setErrors(Object.keys(errors).length ? errors : null);
        }
      }
    });

    this.requestForm.get('toCostCenterId')?.valueChanges.subscribe(value => {
      const fromCostCenterId = this.requestForm.get('fromCostCenterId')?.value;
      if (value && fromCostCenterId && value === fromCostCenterId) {
        this.requestForm.get('toCostCenterId')?.setErrors({ sameCostCenter: true });
      } else {
        const errors = this.requestForm.get('toCostCenterId')?.errors;
        if (errors) {
          delete errors['sameCostCenter'];
          this.requestForm.get('toCostCenterId')?.setErrors(Object.keys(errors).length ? errors : null);
        }
      }
    });
  }

  loadData(): void {
    this.isLoading = true;

    forkJoin({
      costCenters: this.costCenterService.getAllCostCenters(),
      products: this.productService.getAllProducts(),
      units: this.unitService.getAllUnits()
    })
    .pipe(finalize(() => this.isLoading = false))
    .subscribe({
      next: (data) => {
        this.costCenters = data.costCenters;
        this.products = data.products;
        this.units = data.units;

        // Add an empty detail if creating a new request
        if (!this.isEditMode) {
          this.addDetail();
        }
      },
      error: (error) => {
        console.error('Error loading data', error);
        this.snackBar.open('Error loading data. Please try again.', 'Close', {
          duration: 5000
        });
      }
    });
  }

  loadStockRequest(id: number): void {
    this.isLoading = true;

    this.stockRequestService.getStockRequestById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (stockRequest) => {
          this.stockRequest = stockRequest;

          // Patch form values
          this.requestForm.patchValue({
            fromCostCenterId: stockRequest.fromCostCenterId,
            toCostCenterId: stockRequest.toCostCenterId,
            requestDate: new Date(stockRequest.requestDate),
            notes: stockRequest.notes
          });

          // Clear existing details
          while (this.detailsFormArray.length) {
            this.detailsFormArray.removeAt(0);
          }

          // Add details
          if (stockRequest.details && stockRequest.details.length > 0) {
            stockRequest.details.forEach(detail => {
              this.addDetail(detail);
            });
          } else {
            this.addDetail();
          }

          // Disable form if not in Draft status
          if (stockRequest.status !== 'Draft') {
            this.requestForm.disable();
          }
        },
        error: (error) => {
          console.error('Error loading stock request', error);
          this.snackBar.open('Error loading stock request. Please try again.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  addDetail(detail?: StockRequestDetail): void {
    const detailForm = this.fb.group({
      id: [detail?.id || 0],
      productId: [detail?.productId || '', Validators.required],
      batchId: [detail?.batchId || null],
      unitId: [detail?.unitId || null],
      quantity: [detail?.quantity || 0, [Validators.required, Validators.min(0.01)]],
      price: [detail?.price || null],
      deliveryDate: [detail?.deliveryDate ? new Date(detail.deliveryDate) : null],
      notes: [detail?.notes || '']
    });

    // Add listeners for price and quantity changes to calculate total
    detailForm.get('price')?.valueChanges.subscribe(() => this.calculateTotal(detailForm));
    detailForm.get('quantity')?.valueChanges.subscribe(() => this.calculateTotal(detailForm));

    this.detailsFormArray.push(detailForm);
  }

  removeDetail(index: number): void {
    this.detailsFormArray.removeAt(index);
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.name : '';
  }

  getUnitName(unitId: number): string {
    const unit = this.units.find(u => u.id === unitId);
    return unit ? unit.name : '';
  }

  getCostCenterName(costCenterId: number): string {
    const costCenter = this.costCenters.find(cc => cc.id === costCenterId);
    return costCenter ? costCenter.name : '';
  }

  calculateTotal(detailForm: FormGroup): void {
    const price = detailForm.get('price')?.value || 0;
    const quantity = detailForm.get('quantity')?.value || 0;
    const total = price * quantity;

    // We don't store the total in the form, but we can calculate it when needed
    // This is just to update the UI
  }

  getTotal(index: number): number {
    const detailForm = this.detailsFormArray.at(index) as FormGroup;
    const price = detailForm.get('price')?.value || 0;
    const quantity = detailForm.get('quantity')?.value || 0;
    return price * quantity;
  }

  onProductSelected(productId: number, index: number): void {
    if (productId) {
      const product = this.products.find(p => p.id === productId);
      const detailForm = this.detailsFormArray.at(index) as FormGroup;

      if (product && product.unitId) {
        detailForm.get('unitId')?.setValue(product.unitId);
      }

      // You could also set the price based on the product's default price
      if (product && product.costPrice) {
        detailForm.get('price')?.setValue(product.costPrice);
      }
    }
  }

  saveStockRequest(): void {
    if (this.requestForm.invalid) {
      this.markFormGroupTouched(this.requestForm);
      this.snackBar.open('Please fix the errors in the form before saving.', 'Close', {
        duration: 5000
      });
      return;
    }

    this.isLoading = true;

    if (this.isEditMode && this.stockRequestId) {
      // Update existing stock request
      const updateData = {
        id: this.stockRequestId,
        ...this.requestForm.value,
        status: 'Draft'
      };

      this.stockRequestService.updateStockRequest(this.stockRequestId, updateData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request updated successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-requests']);
          },
          error: (error) => {
            console.error('Error updating stock request', error);
            this.snackBar.open('Error updating stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    } else {
      // Create new stock request
      const createData: CreateStockRequestHeader = {
        ...this.requestForm.value
      };

      this.stockRequestService.createStockRequest(createData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (result) => {
            this.snackBar.open('Stock request created successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-requests']);
          },
          error: (error) => {
            console.error('Error creating stock request', error);
            this.snackBar.open('Error creating stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  submitStockRequest(): void {
    if (!this.stockRequestId) return;

    if (confirm('Are you sure you want to submit this stock request?')) {
      this.isLoading = true;

      this.stockRequestService.submitStockRequest(this.stockRequestId, { id: this.stockRequestId })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request submitted successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-requests']);
          },
          error: (error) => {
            console.error('Error submitting stock request', error);
            this.snackBar.open('Error submitting stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  approveStockRequest(): void {
    if (!this.stockRequestId) return;

    if (confirm('Are you sure you want to approve this stock request?')) {
      this.isLoading = true;

      this.stockRequestService.approveStockRequest(this.stockRequestId, { id: this.stockRequestId })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request approved successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-requests']);
          },
          error: (error) => {
            console.error('Error approving stock request', error);
            this.snackBar.open('Error approving stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  rejectStockRequest(): void {
    if (!this.stockRequestId) return;

    if (confirm('Are you sure you want to reject this stock request?')) {
      this.isLoading = true;

      this.stockRequestService.rejectStockRequest(this.stockRequestId, { id: this.stockRequestId })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request rejected successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-requests']);
          },
          error: (error) => {
            console.error('Error rejecting stock request', error);
            this.snackBar.open('Error rejecting stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  completeStockRequest(): void {
    if (!this.stockRequestId) return;

    if (confirm('Are you sure you want to complete this stock request? This will update inventory levels.')) {
      this.isLoading = true;

      this.stockRequestService.completeStockRequest(this.stockRequestId, { id: this.stockRequestId })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request completed successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-requests']);
          },
          error: (error) => {
            console.error('Error completing stock request', error);
            this.snackBar.open('Error completing stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancelStockRequest(): void {
    if (!this.stockRequestId) return;

    if (confirm('Are you sure you want to cancel this stock request?')) {
      this.isLoading = true;

      this.stockRequestService.cancelStockRequest(this.stockRequestId)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            this.snackBar.open('Stock request cancelled successfully', 'Close', {
              duration: 3000
            });
            this.router.navigate(['/inventory/stock-requests']);
          },
          error: (error) => {
            console.error('Error cancelling stock request', error);
            this.snackBar.open('Error cancelling stock request: ' + (error.error || 'Unknown error'), 'Close', {
              duration: 5000
            });
          }
        });
    }
  }

  cancel(): void {
    this.router.navigate(['/inventory/stock-requests']);
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        for (let i = 0; i < control.length; i++) {
          const arrayControl = control.at(i);
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        }
      }
    });
  }
}
