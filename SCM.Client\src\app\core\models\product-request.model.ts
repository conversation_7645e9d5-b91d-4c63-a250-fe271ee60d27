export interface ProductRequestHeader {
  id: number;
  referenceNumber: string;
  costCenterId: number;
  costCenterName?: string;
  requestDate: Date;
  notes?: string;
  status: string; // Draft, Submitted, Approved, Rejected, Completed, Cancelled
  createdById?: number;
  createdByName?: string;
  approvedById?: number;
  approvedByName?: string;
  approvedAt?: Date;
  completedById?: number;
  completedByName?: string;
  completedAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  details?: ProductRequestDetail[];
}

export interface ProductRequestDetail {
  id: number;
  productRequestHeaderId: number;
  productId: number;
  productName?: string;
  productCode?: string;
  batchId?: number;
  batchNumber?: string;
  unitId?: number;
  unitName?: string;
  quantity: number;
  price?: number;
  total?: number;
  deliveryDate?: Date;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateProductRequestHeader {
  costCenterId: number;
  requestDate: Date;
  notes?: string;
  details: CreateProductRequestDetail[];
}

export interface CreateProductRequestDetail {
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  price?: number;
  deliveryDate?: Date;
  notes?: string;
}

export interface UpdateProductRequestHeader {
  id: number;
  costCenterId: number;
  requestDate: Date;
  notes?: string;
  status?: string;
}

export interface UpdateProductRequestDetail {
  id: number;
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  price?: number;
  deliveryDate?: Date;
  notes?: string;
}

export interface SubmitProductRequestDto {
  id: number;
  notes?: string;
}

export interface ApproveProductRequestDto {
  id: number;
  notes?: string;
}

export interface RejectProductRequestDto {
  id: number;
  reason: string;
}

export interface CompleteProductRequestDto {
  id: number;
  notes?: string;
}

export interface ProductRequestListItem {
  id: number;
  referenceNumber: string;
  costCenterId: number;
  costCenterName: string;
  requestDate: Date;
  status: string;
  createdByName?: string;
  createdAt: Date;
}
