.container {
  padding: 20px;
}

.filter-notification {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #e3f2fd;
  padding: 10px 16px;
  border-radius: 4px;
  margin-bottom: 20px;

  .filter-message {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1976d2;
    font-weight: 500;
  }
}

.row {
  margin-bottom: 20px;
}

.full-width {
  width: 100%;
}

.button-row {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-container {
  position: relative;
  min-height: 200px;
  overflow: auto;
}

.no-data-message {
  text-align: center;
  padding: 20px;
  font-style: italic;
  color: rgba(0, 0, 0, 0.54);
}

.mat-column-actions {
  width: 120px;
  text-align: center;
}

.mat-column-isActive,
.mat-column-isSalesPoint {
  width: 100px;
  text-align: center;
}

mat-card {
  margin-bottom: 20px;
}

mat-checkbox {
  margin: 10px 0;
  display: block;
}
