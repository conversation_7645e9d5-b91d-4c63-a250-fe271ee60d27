import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, map, startWith, of, forkJoin } from 'rxjs';
import { ProductService } from '../../../core/services/product.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { UnitService } from '../../../core/services/unit.service';
import { ErrorService } from '../../../core/services/error.service';
import { TransactionService } from '../../../core/services/transaction.service';
import { Product } from '../../../core/models/product.model';
import { CostCenter } from '../../../core/models/cost-center.model';
import { Unit } from '../../../core/models/unit.model';
import { CreateProductRequest, TransactionHeader } from '../../../core/models/transaction.model';

@Component({
  selector: 'app-product-request',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
    MatTabsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './product-request.component.html',
  styleUrls: ['./product-request.component.scss']
})
export class ProductRequestComponent implements OnInit {
  productRequestForm!: FormGroup;
  productRequestNo: string = '';
  currentDate: Date = new Date();
  isLoading = false;
  isEditMode = false;
  isViewMode = false;
  productRequestId: number | null = null;
  status: string = 'Draft';

  // For view mode display
  requestItems: any[] = [];

  displayedColumns: string[] = [
    'productCode',
    'productName',
    'unitName',
    'quantity',
    'price',
    'total',
    'deliveryDate',
    'actions'
  ];

  viewDisplayedColumns: string[] = [
    'productName',
    'unitName',
    'quantity',
    'price',
    'total'
  ];

  costCenters: CostCenter[] = [];
  products: Product[] = [];
  units: Unit[] = [];
  filteredProducts: Observable<Product[]> = of([]);

  constructor(
    private fb: FormBuilder,
    private productService: ProductService,
    private costCenterService: CostCenterService,
    private unitService: UnitService,
    private transactionService: TransactionService,
    private snackBar: MatSnackBar,
    private errorService: ErrorService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadReferenceData();
    this.generateRequestNumber();

    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      const mode = params.get('mode');

      if (id) {
        this.productRequestId = parseInt(id, 10);

        if (mode === 'view') {
          this.isViewMode = true;
          this.isEditMode = false;
        } else if (mode === 'edit') {
          this.isEditMode = true;
          this.isViewMode = false;
        }

        this.loadProductRequest(this.productRequestId);
      }
    });

    this.setupProductFilterObservable();
  }

  generateRequestNumber(): void {
    // Generate a random request number with PR prefix
    const randomNum = Math.floor(Math.random() * 100000);
    this.productRequestNo = 'PR-' + randomNum.toString().padStart(5, '0');
    console.log('Generated request number:', this.productRequestNo);

    // Make sure the reference number is visible in the form
    setTimeout(() => {
      const requestNumberElement = document.getElementById('requestNumberField');
      if (requestNumberElement) {
        (requestNumberElement as HTMLInputElement).value = this.productRequestNo;
      }
    }, 100);
  }

  initForm(): void {
    this.productRequestForm = this.fb.group({
      costCenterId: ['', Validators.required],
      requestDate: [this.currentDate, Validators.required],
      notes: [''],
      details: this.fb.array([])
    });

    // Add at least one empty row
    this.addItem();
  }

  loadReferenceData(): void {
    this.isLoading = true;

    forkJoin({
      costCenters: this.costCenterService.getAllCostCenters(),
      products: this.productService.getAll(),
      units: this.unitService.getAll()
    }).subscribe({
      next: (result) => {
        this.costCenters = result.costCenters;
        this.products = result.products;
        this.units = result.units;

        console.log('Loaded products:', this.products);
        console.log('Loaded units:', this.units);

        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.errorService.handleError(error);
      }
    });
  }

  loadProductRequest(id: number): void {
    this.isLoading = true;

    this.transactionService.getById(id).subscribe({
      next: (transaction: TransactionHeader) => {
        console.log('Loaded transaction:', transaction);
        console.log('Transaction details:', transaction.details);

        // Set the reference number
        this.productRequestNo = transaction.referenceNumber || `PR-${transaction.id.toString().padStart(5, '0')}`;

        // Set the status
        this.status = transaction.status || 'Draft';

        // Update the reference number in the DOM
        setTimeout(() => {
          const requestNumberElement = document.getElementById('requestNumberField');
          if (requestNumberElement) {
            (requestNumberElement as HTMLInputElement).value = this.productRequestNo;
          }
        }, 100);

        // Clear the form array first
        while (this.items.length) {
          this.items.removeAt(0);
        }

        // Clear the request items array
        this.requestItems = [];

        // Patch the header values
        this.productRequestForm.patchValue({
          costCenterId: transaction.sourceCostCenterId,
          requestDate: new Date(transaction.transactionDate),
          notes: transaction.notes
        });

        // Add details
        if (transaction.details && transaction.details.length > 0) {
          // Populate the requestItems array for view mode
          this.requestItems = transaction.details.map(detail => ({
            productName: detail.productName,
            unitName: detail.unitName,
            quantity: detail.quantity,
            price: detail.unitPrice,
            total: detail.totalAmount
          }));

          // Populate the form array for edit mode
          transaction.details.forEach(detail => {
            const formGroup = this.fb.group({
              id: [detail.id],
              productId: [detail.productId, Validators.required],
              productCode: [detail.productCode],
              productName: [detail.productName],
              unitId: [detail.unitId],
              unitName: [detail.unitName],
              quantity: [detail.quantity, [Validators.required, Validators.min(0.01)]],
              price: [detail.unitPrice],
              total: [detail.totalAmount],
              deliveryDate: [null], // Transaction doesn't have delivery date at detail level
              notes: [detail.notes]
            });

            this.items.push(formGroup);
          });
        }

        // If in view mode, disable all form controls
        if (this.isViewMode) {
          this.productRequestForm.disable();
        } else if (this.isEditMode) {
          // Make sure the form is enabled in edit mode
          this.productRequestForm.enable();
        }

        // Set up product filtering for each row
        this.setupProductFilterObservable();

        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.errorService.handleError(error);
      }
    });
  }

  setupProductFilterObservable(): void {
    // We need to set up filtering for each row individually
    for (let i = 0; i < this.items.length; i++) {
      this.setupProductFilterForRow(i);
    }
  }

  setupProductFilterForRow(index: number): void {
    const itemGroup = this.items.at(index);
    const productCodeControl = itemGroup.get('productCode');

    if (productCodeControl) {
      const filteredProducts = productCodeControl.valueChanges.pipe(
        startWith(''),
        map(value => {
          // Filter by the string value
          const filterValue = value ? value.toLowerCase() : '';
          return this.products.filter(product =>
            product.code.toLowerCase().includes(filterValue) ||
            product.name.toLowerCase().includes(filterValue)
          );
        })
      );

      // Store the observable in the component instance
      this.filteredProducts = filteredProducts;

      console.log('Set up product filter for row', index);
    }
  }

  get items(): FormArray {
    return this.productRequestForm.get('details') as FormArray;
  }

  addItem(): void {
    const itemGroup = this.fb.group({
      productId: ['', Validators.required],
      productCode: [''],
      productName: [''],
      unitId: ['', Validators.required],
      unitName: [''],
      quantity: [1, [Validators.required, Validators.min(0.01)]],
      price: [0],
      total: [0],
      deliveryDate: [null],
      notes: ['']
    });

    this.items.push(itemGroup);

    // Set up product filtering for the new row
    const index = this.items.length - 1;
    this.setupProductFilterForRow(index);

    // Set up quantity and price change listeners
    const quantityControl = itemGroup.get('quantity');
    const priceControl = itemGroup.get('price');

    if (quantityControl) {
      quantityControl.valueChanges.subscribe(() => {
        this.calculateTotal(index);
      });
    }

    if (priceControl) {
      priceControl.valueChanges.subscribe(() => {
        this.calculateTotal(index);
      });
    }
  }

  removeItem(index: number): void {
    this.items.removeAt(index);
  }

  selectProduct(index: number, product: Product): void {
    const itemGroup = this.items.at(index);

    // Find the unit name from the unit ID
    const unit = this.units.find(u => u.id === product.unitId);

    // Update the form values
    itemGroup.patchValue({
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      unitId: product.unitId,
      price: product.costPrice || 0
    });

    console.log('Selected product:', product);
    console.log('Selected unit:', unit);
    console.log('Updated form group:', itemGroup.value);

    // Calculate the total
    this.calculateTotal(index);
  }

  onUnitChange(index: number): void {
    const itemGroup = this.items.at(index);
    const unitId = itemGroup.get('unitId')?.value;
    const unit = this.units.find(u => u.id === unitId);

    if (unit) {
      // Update the unitName field
      itemGroup.patchValue({
        unitName: unit.name
      });

      console.log(`Selected unit for row ${index}:`, unit);
      console.log('Updated form group:', itemGroup.value);

      // Recalculate total in case unit affects pricing
      this.calculateTotal(index);
    }
  }

  displayProductFn(productCode: string): string {
    return productCode || '';
  }

  calculateTotal(index: number): void {
    const itemGroup = this.items.at(index);
    const quantity = parseFloat(itemGroup.get('quantity')?.value) || 0;
    const price = parseFloat(itemGroup.get('price')?.value) || 0;

    // Calculate total and round to 2 decimal places
    const total = Math.round((quantity * price) * 100) / 100;

    itemGroup.patchValue({ total });

    console.log(`Calculated total for row ${index}: ${quantity} * ${price} = ${total}`);
  }

  save(): void {
    if (this.productRequestForm.valid) {
      this.isLoading = true;

      const formValue = this.productRequestForm.value;
      const productRequest: CreateProductRequest = {
        sourceCostCenterId: formValue.costCenterId,
        transactionDate: formValue.requestDate,
        notes: formValue.notes,
        referenceNumber: this.productRequestNo, // Add the reference number
        details: formValue.details.map((detail: any) => ({
          productId: detail.productId,
          unitId: detail.unitId,
          quantity: detail.quantity,
          unitPrice: detail.price,
          notes: detail.notes
        }))
      };

      if (this.isEditMode && this.productRequestId) {
        this.transactionService.updateProductRequest(this.productRequestId, {
          id: this.productRequestId,
          sourceCostCenterId: formValue.costCenterId,
          transactionDate: formValue.requestDate,
          notes: formValue.notes
        }).subscribe({
          next: () => {
            this.isLoading = false;
            this.snackBar.open('Product Request updated successfully', 'Close', { duration: 3000 });
          },
          error: (error: any) => {
            this.isLoading = false;
            this.errorService.handleError(error);
          }
        });
      } else {
        this.transactionService.createProductRequest(productRequest).subscribe({
          next: () => {
            this.isLoading = false;
            this.snackBar.open('Product Request saved successfully', 'Close', { duration: 3000 });
            this.router.navigate(['/transactions/product-requests']);
          },
          error: (error: any) => {
            this.isLoading = false;
            this.errorService.handleError(error);
          }
        });
      }
    } else {
      this.snackBar.open('Please fill all required fields', 'Close', { duration: 3000 });
      this.markFormGroupTouched(this.productRequestForm);
    }
  }

  submit(): void {
    if (!this.productRequestForm.valid) {
      this.snackBar.open('Please fill all required fields', 'Close', { duration: 3000 });
      this.markFormGroupTouched(this.productRequestForm);
      return;
    }

    this.isLoading = true;

    if (this.isEditMode && this.productRequestId) {
      // If editing an existing request, submit it directly
      this.transactionService.submitProductRequest(this.productRequestId,
        this.productRequestForm.get('notes')?.value).subscribe({
        next: () => {
          this.isLoading = false;
          this.snackBar.open('Product Request submitted successfully. It will now be reviewed for approval.', 'Close', { duration: 5000 });
          this.router.navigate(['/transactions/product-requests']);
        },
        error: (error: any) => {
          this.isLoading = false;
          this.errorService.handleError(error);
        }
      });
    } else {
      // If creating a new request, save it first then submit
      const formValue = this.productRequestForm.value;
      const productRequest: CreateProductRequest = {
        sourceCostCenterId: formValue.costCenterId,
        transactionDate: formValue.requestDate,
        notes: formValue.notes,
        referenceNumber: this.productRequestNo, // Add the reference number
        details: formValue.details.map((detail: any) => ({
          productId: detail.productId,
          unitId: detail.unitId,
          quantity: detail.quantity,
          unitPrice: detail.price,
          notes: detail.notes
        }))
      };

      this.transactionService.createProductRequest(productRequest).subscribe({
        next: (response: TransactionHeader) => {
          // After saving, submit the request
          this.transactionService.submitProductRequest(response.id, formValue.notes).subscribe({
            next: () => {
              this.isLoading = false;
              this.snackBar.open('Product Request created and submitted successfully. It will now be reviewed for approval.', 'Close', { duration: 5000 });
              this.router.navigate(['/transactions/product-requests']);
            },
            error: (submitError: any) => {
              this.isLoading = false;
              this.errorService.handleError(submitError);
            }
          });
        },
        error: (error: any) => {
          this.isLoading = false;
          this.errorService.handleError(error);
        }
      });
    }
  }

  discard(): void {
    this.router.navigate(['/transactions/product-requests']);
  }

  open(): void {
    // Implementation for opening an existing product request
    // This would typically show a dialog to select from existing requests
  }

  canSubmit(): boolean {
    return !this.isViewMode && this.productRequestForm.valid && this.items.length > 0;
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/transactions/product-requests']);
  }

  editProductRequest(): void {
    if (this.productRequestId) {
      this.router.navigate([`/transactions/product-requests/${this.productRequestId}/edit`]);
    }
  }

  createPurchaseOrder(): void {
    if (this.productRequestId) {
      this.router.navigate(['/transactions/purchase-orders/new'], {
        queryParams: { requestId: this.productRequestId }
      });

      this.snackBar.open('Creating purchase order from approved product request. You will be able to add a supplier and modify details.', 'Close', { duration: 5000 });
    }
  }

  getCostCenterName(costCenterId: number): string {
    const costCenter = this.costCenters.find(cc => cc.id === costCenterId);
    return costCenter ? costCenter.name : 'N/A';
  }

  calculateGrandTotal(): number {
    let total = 0;

    if (this.isViewMode && this.requestItems.length > 0) {
      // Use requestItems for view mode
      total = this.requestItems.reduce((sum, item) => sum + (item.total || 0), 0);
    } else {
      // Use form controls for edit mode
      for (let i = 0; i < this.items.length; i++) {
        const itemTotal = parseFloat(this.items.at(i).get('total')?.value) || 0;
        total += itemTotal;
      }
    }

    return total;
  }
}
