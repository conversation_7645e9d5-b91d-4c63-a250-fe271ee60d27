<div class="container">
  <div class="loading-shade" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <div *ngIf="location">
    <mat-card>
      <mat-card-header>
        <div mat-card-avatar>
          <mat-icon color="primary">location_on</mat-icon>
        </div>
        <mat-card-title>{{ location.name }}</mat-card-title>
        <mat-card-subtitle *ngIf="location.code">Code: {{ location.code }}</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="tab-content">
          <h3>Location Details</h3>
          <mat-list>
            <mat-list-item *ngIf="location.companyName">
              <span matListItemTitle>Company</span>
              <span matListItemLine>
                <a (click)="goToCompany()" class="link">{{ location.companyName }}</a>
              </span>
            </mat-list-item>

            <mat-divider *ngIf="location.companyName"></mat-divider>

            <mat-list-item *ngIf="location.description">
              <span matListItemTitle>Description</span>
              <span matListItemLine>{{ location.description }}</span>
            </mat-list-item>

            <mat-divider *ngIf="location.description"></mat-divider>

            <mat-list-item *ngIf="location.address">
              <span matListItemTitle>Address</span>
              <span matListItemLine>{{ location.address }}</span>
            </mat-list-item>

            <mat-divider *ngIf="location.address"></mat-divider>

            <mat-list-item *ngIf="location.city || location.state || location.country">
              <span matListItemTitle>Location</span>
              <span matListItemLine>
                {{ location.city }}{{ location.city && (location.state || location.country) ? ', ' : '' }}
                {{ location.state }}{{ location.state && location.country ? ', ' : '' }}
                {{ location.country }}
              </span>
            </mat-list-item>

            <mat-divider *ngIf="location.city || location.state || location.country"></mat-divider>

            <mat-list-item *ngIf="location.postalCode">
              <span matListItemTitle>Postal Code</span>
              <span matListItemLine>{{ location.postalCode }}</span>
            </mat-list-item>

            <mat-divider *ngIf="location.postalCode"></mat-divider>

            <mat-list-item *ngIf="location.phone">
              <span matListItemTitle>Phone</span>
              <span matListItemLine>{{ location.phone }}</span>
            </mat-list-item>

            <mat-divider *ngIf="location.phone"></mat-divider>

            <mat-list-item *ngIf="location.email">
              <span matListItemTitle>Email</span>
              <span matListItemLine>{{ location.email }}</span>
            </mat-list-item>

            <mat-divider *ngIf="location.email"></mat-divider>

            <mat-list-item>
              <span matListItemTitle>Status</span>
              <span matListItemLine>{{ location.isActive ? 'Active' : 'Inactive' }}</span>
            </mat-list-item>

            <mat-divider></mat-divider>

            <mat-list-item>
              <span matListItemTitle>Created At</span>
              <span matListItemLine>{{ location.createdAt | date:'medium' }}</span>
            </mat-list-item>

            <mat-divider></mat-divider>

            <mat-list-item *ngIf="location.updatedAt">
              <span matListItemTitle>Last Updated</span>
              <span matListItemLine>{{ location.updatedAt | date:'medium' }}</span>
            </mat-list-item>
          </mat-list>
        </div>

        <div class="tab-content mt-4">
          <h3>Stores</h3>
          <div class="action-bar">
            <button mat-raised-button color="primary" (click)="addStore()">
              <mat-icon>add</mat-icon> Add Store
            </button>
          </div>

          <div *ngIf="stores.length > 0">
            <table mat-table [dataSource]="stores" class="full-width">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let store">{{ store.name }}</td>
              </ng-container>

              <!-- Code Column -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef>Code</th>
                <td mat-cell *matCellDef="let store">{{ store.code }}</td>
              </ng-container>

              <!-- City Column -->
              <ng-container matColumnDef="city">
                <th mat-header-cell *matHeaderCellDef>City</th>
                <td mat-cell *matCellDef="let store">{{ store.city }}</td>
              </ng-container>

              <!-- Country Column -->
              <ng-container matColumnDef="country">
                <th mat-header-cell *matHeaderCellDef>Country</th>
                <td mat-cell *matCellDef="let store">{{ store.country }}</td>
              </ng-container>

              <!-- Sales Point Column -->
              <ng-container matColumnDef="isSalesPoint">
                <th mat-header-cell *matHeaderCellDef>Sales Point</th>
                <td mat-cell *matCellDef="let store">
                  <mat-icon [color]="store.isSalesPoint ? 'primary' : 'warn'">
                    {{ store.isSalesPoint ? 'check_circle' : 'cancel' }}
                  </mat-icon>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="isActive">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let store">
                  <mat-icon [color]="store.isActive ? 'primary' : 'warn'">
                    {{ store.isActive ? 'check_circle' : 'cancel' }}
                  </mat-icon>
                </td>
              </ng-container>

              <!-- Cost Centers Count Column -->
              <ng-container matColumnDef="costCentersCount">
                <th mat-header-cell *matHeaderCellDef>Cost Centers</th>
                <td mat-cell *matCellDef="let store">{{ store.costCentersCount }}</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let store">
                  <button mat-icon-button color="primary" (click)="viewStore(store.id)" matTooltip="View">
                    <mat-icon>visibility</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="storeColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: storeColumns;"></tr>
            </table>
          </div>

          <div class="no-data-message" *ngIf="stores.length === 0">
            No stores found for this location. Click "Add Store" to create one.
          </div>
        </div>

        <div class="tab-content mt-4">
          <h3>Cost Centers</h3>
          <div *ngIf="costCenters.length > 0">
            <table mat-table [dataSource]="costCenters" class="full-width">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.name }}</td>
              </ng-container>

              <!-- Code Column -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef>Code</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.code }}</td>
              </ng-container>

              <!-- Store Column -->
              <ng-container matColumnDef="storeName">
                <th mat-header-cell *matHeaderCellDef>Store</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.storeName }}</td>
              </ng-container>

              <!-- Type Column -->
              <ng-container matColumnDef="typeName">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let costCenter">{{ costCenter.typeName }}</td>
              </ng-container>

              <!-- Sales Point Column -->
              <ng-container matColumnDef="isSalesPoint">
                <th mat-header-cell *matHeaderCellDef>Sales Point</th>
                <td mat-cell *matCellDef="let costCenter">
                  <mat-icon [color]="costCenter.isSalesPoint ? 'primary' : 'warn'">
                    {{ costCenter.isSalesPoint ? 'check_circle' : 'cancel' }}
                  </mat-icon>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="isActive">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let costCenter">
                  <mat-icon [color]="costCenter.isActive ? 'primary' : 'warn'">
                    {{ costCenter.isActive ? 'check_circle' : 'cancel' }}
                  </mat-icon>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let costCenter">
                  <button mat-icon-button color="primary" (click)="viewCostCenter(costCenter.id)" matTooltip="View">
                    <mat-icon>visibility</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="costCenterColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: costCenterColumns;"></tr>
            </table>
          </div>

          <div class="no-data-message" *ngIf="costCenters.length === 0">
            No cost centers found for this location.
          </div>
        </div>
      </mat-card-content>

      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="editLocation()">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
