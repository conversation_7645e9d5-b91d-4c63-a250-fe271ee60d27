namespace SCM.Application.DTOs;

public class StockAdjustmentHeaderDto
{
    public int Id { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public DateTime AdjustmentDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = string.Empty;
    public int? CreatedById { get; set; }
    public string? CreatedByName { get; set; }
    public int? CompletedById { get; set; }
    public string? CompletedByName { get; set; }
    public DateTime? CompletedAt { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<StockAdjustmentDetailDto>? Details { get; set; }
}

public class CreateStockAdjustmentHeaderDto
{
    public int CostCenterId { get; set; }
    public DateTime AdjustmentDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
    public List<CreateStockAdjustmentDetailDto> Details { get; set; } = new List<CreateStockAdjustmentDetailDto>();
}

public class UpdateStockAdjustmentHeaderDto
{
    public int Id { get; set; }
    public int CostCenterId { get; set; }
    public DateTime AdjustmentDate { get; set; }
    public string? Notes { get; set; }
    public string Status { get; set; } = "Draft";
}

public class CompleteStockAdjustmentDto
{
    public int Id { get; set; }
    public string? Notes { get; set; }
}

public class StockAdjustmentDetailDto
{
    public int Id { get; set; }
    public int StockAdjustmentHeaderId { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public int? BatchId { get; set; }
    public string? BatchNumber { get; set; }
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal CurrentQuantity { get; set; }
    public decimal AdjustmentQuantity { get; set; }
    public decimal NewQuantity { get; set; }
    public decimal? CostPrice { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateStockAdjustmentDetailDto
{
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal CurrentQuantity { get; set; }
    public decimal AdjustmentQuantity { get; set; }
    public decimal NewQuantity { get; set; }
    public decimal? CostPrice { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class UpdateStockAdjustmentDetailDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public int? BatchId { get; set; }
    public int? UnitId { get; set; }
    public decimal CurrentQuantity { get; set; }
    public decimal AdjustmentQuantity { get; set; }
    public decimal NewQuantity { get; set; }
    public decimal? CostPrice { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}
