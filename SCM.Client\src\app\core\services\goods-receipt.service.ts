import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  GoodsReceipt,
  GoodsReceiptListItem,
  GoodsReceiptDetail,
  CreateGoodsReceipt,
  CreateGoodsReceiptDetail,
  UpdateGoodsReceipt,
  UpdateGoodsReceiptDetail,
  CompleteGoodsReceipt
} from '../models/goods-receipt.model';
import { PurchaseOrder } from '../models/purchase-order.model';

@Injectable({
  providedIn: 'root'
})
export class GoodsReceiptService {
  private readonly apiUrl = `${environment.apiUrl}/goodsreceipts`;

  constructor(private http: HttpClient) { }

  getAllGoodsReceipts(): Observable<GoodsReceiptListItem[]> {
    return this.http.get<GoodsReceiptListItem[]>(this.apiUrl);
  }

  getGoodsReceiptById(id: number): Observable<GoodsReceipt> {
    return this.http.get<GoodsReceipt>(`${this.apiUrl}/${id}`);
  }

  getGoodsReceiptsByPurchaseOrderId(purchaseOrderId: number): Observable<GoodsReceiptListItem[]> {
    return this.http.get<GoodsReceiptListItem[]>(`${this.apiUrl}/purchase-order/${purchaseOrderId}`);
  }

  getGoodsReceiptsBySupplierId(supplierId: number): Observable<GoodsReceiptListItem[]> {
    return this.http.get<GoodsReceiptListItem[]>(`${this.apiUrl}/supplier/${supplierId}`);
  }

  getGoodsReceiptsByCostCenterId(costCenterId: number): Observable<GoodsReceiptListItem[]> {
    return this.http.get<GoodsReceiptListItem[]>(`${this.apiUrl}/cost-center/${costCenterId}`);
  }

  getGoodsReceiptsByStatus(status: string): Observable<GoodsReceiptListItem[]> {
    return this.http.get<GoodsReceiptListItem[]>(`${this.apiUrl}/status/${status}`);
  }

  getPurchaseOrderForReceiving(purchaseOrderId: number): Observable<PurchaseOrder> {
    return this.http.get<PurchaseOrder>(`${this.apiUrl}/purchase-order/${purchaseOrderId}/for-receiving`);
  }

  createGoodsReceipt(goodsReceipt: CreateGoodsReceipt): Observable<GoodsReceipt> {
    return this.http.post<GoodsReceipt>(this.apiUrl, goodsReceipt);
  }

  updateGoodsReceipt(id: number, goodsReceipt: UpdateGoodsReceipt): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, goodsReceipt);
  }

  deleteGoodsReceipt(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  completeGoodsReceipt(id: number, completeData: CompleteGoodsReceipt): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/complete`, completeData);
  }

  cancelGoodsReceipt(id: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/cancel`, {});
  }

  getGoodsReceiptDetails(goodsReceiptHeaderId: number): Observable<GoodsReceiptDetail[]> {
    return this.http.get<GoodsReceiptDetail[]>(`${this.apiUrl}/${goodsReceiptHeaderId}/details`);
  }

  getGoodsReceiptDetailById(id: number): Observable<GoodsReceiptDetail> {
    return this.http.get<GoodsReceiptDetail>(`${this.apiUrl}/details/${id}`);
  }

  createGoodsReceiptDetail(goodsReceiptHeaderId: number, detail: CreateGoodsReceiptDetail): Observable<GoodsReceiptDetail> {
    return this.http.post<GoodsReceiptDetail>(`${this.apiUrl}/${goodsReceiptHeaderId}/details`, detail);
  }

  updateGoodsReceiptDetail(id: number, detail: UpdateGoodsReceiptDetail): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/details/${id}`, detail);
  }

  deleteGoodsReceiptDetail(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/details/${id}`);
  }
}
