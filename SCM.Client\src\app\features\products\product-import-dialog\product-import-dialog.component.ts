import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ProductService } from '../../../core/services/product.service';
import { finalize } from 'rxjs';

export interface ProductImportResult {
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: ProductImportError[];
  successfulProducts: string[];
}

export interface ProductImportError {
  rowNumber: number;
  productCode: string;
  productName: string;
  errorMessage: string;
}

@Component({
  selector: 'app-product-import-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTableModule,
    MatCardModule,
    MatSnackBarModule
  ],
  templateUrl: './product-import-dialog.component.html',
  styleUrls: ['./product-import-dialog.component.scss']
})
export class ProductImportDialogComponent {
  selectedFile: File | null = null;
  isUploading = false;
  importResult: ProductImportResult | null = null;
  displayedColumns: string[] = ['rowNumber', 'productCode', 'productName', 'errorMessage'];

  constructor(
    private dialogRef: MatDialogRef<ProductImportDialogComponent>,
    private productService: ProductService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      if (file.name.endsWith('.xlsx')) {
        this.selectedFile = file;
      } else {
        this.snackBar.open('Please select an Excel (.xlsx) file', 'Close', {
          duration: 3000
        });
      }
    }
  }

  downloadTemplate(): void {
    this.productService.downloadImportTemplate()
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'ProductImportTemplate.xlsx';
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          console.error('Error downloading template', error);
          this.snackBar.open('Error downloading template', 'Close', {
            duration: 3000
          });
        }
      });
  }

  importProducts(): void {
    if (!this.selectedFile) {
      this.snackBar.open('Please select a file first', 'Close', {
        duration: 3000
      });
      return;
    }

    this.isUploading = true;
    this.importResult = null;

    this.productService.importProducts(this.selectedFile)
      .pipe(
        finalize(() => {
          this.isUploading = false;
        })
      )
      .subscribe({
        next: (result) => {
          this.importResult = result;
          if (result.errorCount === 0) {
            this.snackBar.open(`Successfully imported ${result.successCount} products`, 'Close', {
              duration: 5000
            });
          } else {
            this.snackBar.open(`Import completed with ${result.errorCount} errors`, 'Close', {
              duration: 5000
            });
          }
        },
        error: (error) => {
          console.error('Error importing products', error);
          this.snackBar.open('Error importing products. Please try again.', 'Close', {
            duration: 5000
          });
        }
      });
  }

  close(): void {
    this.dialogRef.close(this.importResult);
  }
}
