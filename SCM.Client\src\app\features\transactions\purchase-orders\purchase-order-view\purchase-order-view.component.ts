import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { PurchaseOrderService } from '../../../../core/services/purchase-order.service';
import { ErrorService } from '../../../../core/services/error.service';
import { PurchaseOrder } from '../../../../core/models/purchase-order.model';
import { TransactionHeader } from '../../../../core/models/transaction.model';
import { TransactionAdapter } from '../../../../core/adapters/transaction-adapter';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-purchase-order-view',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatChipsModule
  ],
  templateUrl: './purchase-order-view.component.html',
  styleUrls: ['./purchase-order-view.component.scss']
})
export class PurchaseOrderViewComponent implements OnInit {
  purchaseOrder: PurchaseOrder | null = null;
  purchaseOrderId: number | null = null;
  isLoading = false;
  
  displayedColumns: string[] = [
    'productName',
    'quantity',
    'unitPrice',
    'totalPrice'
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private purchaseOrderService: PurchaseOrderService,
    private errorService: ErrorService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.purchaseOrderId = +id;
        this.loadPurchaseOrder(this.purchaseOrderId);
      } else {
        this.router.navigate(['/transactions/purchase-orders']);
      }
    });
  }

  loadPurchaseOrder(id: number): void {
    this.isLoading = true;
    console.log('Loading purchase order with ID:', id);

    this.purchaseOrderService.getById(id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (transaction: TransactionHeader) => {
          console.log('Received transaction data:', transaction);
          
          // Convert TransactionHeader to PurchaseOrder
          const purchaseOrder = TransactionAdapter.toPurchaseOrder(transaction);
          console.log('Converted to purchase order:', purchaseOrder);
          
          this.purchaseOrder = purchaseOrder;
        },
        error: (error) => {
          console.error('Error loading purchase order:', error);
          this.errorService.handleError(error);
          this.router.navigate(['/transactions/purchase-orders']);
        }
      });
  }

  submitPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const notes = prompt('Add any notes for submission (optional):');
    // If user clicks cancel, return without submitting
    if (notes === null) return;

    this.isLoading = true;

    this.purchaseOrderService.submit(this.purchaseOrderId, notes)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order submitted successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  approvePurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const notes = prompt('Add any approval notes (optional):');
    // If user clicks cancel, return without approving
    if (notes === null) return;

    this.isLoading = true;

    this.purchaseOrderService.approve(this.purchaseOrderId, notes)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order approved successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  rejectPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const reason = prompt('Please enter a reason for rejection:');
    if (reason === null) return; // User cancelled

    this.isLoading = true;

    this.purchaseOrderService.reject(this.purchaseOrderId, reason)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order rejected successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  cancelPurchaseOrder(): void {
    if (!this.purchaseOrderId) return;

    const reason = prompt('Please enter a reason for cancellation:');
    if (reason === null) return; // User cancelled

    this.isLoading = true;

    this.purchaseOrderService.cancel(this.purchaseOrderId, reason)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: () => {
          this.errorService.showSuccess('Purchase order cancelled successfully');
          this.loadPurchaseOrder(this.purchaseOrderId!);
        },
        error: (error) => this.errorService.handleError(error)
      });
  }

  createReceiving(): void {
    if (this.purchaseOrderId) {
      this.router.navigate(['/transactions/receiving/new'], {
        queryParams: { orderId: this.purchaseOrderId }
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/transactions/purchase-orders']);
  }
}
