using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface IUserService
{
    Task<IEnumerable<UserListDto>> GetAllUsersAsync();
    Task<UserDto?> GetUserByIdAsync(int id);
    Task<UserDto?> GetUserByUsernameAsync(string username);
    Task<UserDto> CreateUserAsync(CreateUserDto createUserDto);
    Task UpdateUserAsync(UpdateUserDto updateUserDto);
    Task DeleteUserAsync(int id);
    Task ChangePasswordAsync(ChangePasswordDto changePasswordDto);
    Task ResetPasswordAsync(ResetPasswordDto resetPasswordDto);
    Task<bool> ValidatePasswordAsync(int userId, string password);
}
