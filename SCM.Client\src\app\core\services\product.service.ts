import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Product, CreateProduct, UpdateProduct } from '../models/product.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private readonly path = 'products';
  private readonly apiUrl = environment.apiUrl;

  constructor(private apiService: ApiService, private http: HttpClient) { }

  getAll(): Observable<Product[]> {
    return this.apiService.get<Product[]>(this.path);
  }

  getAllProducts(): Observable<Product[]> {
    return this.getAll();
  }

  getById(id: number): Observable<Product> {
    return this.apiService.get<Product>(`${this.path}/${id}`);
  }

  getByCode(code: string): Observable<Product> {
    return this.apiService.get<Product>(`${this.path}/code/${code}`);
  }

  getByDepartmentId(departmentId: number): Observable<Product[]> {
    return this.apiService.get<Product[]>(`${this.path}/department/${departmentId}`);
  }

  getByGroupId(groupId: number): Observable<Product[]> {
    return this.apiService.get<Product[]>(`${this.path}/group/${groupId}`);
  }

  getBySubGroupId(subGroupId: number): Observable<Product[]> {
    return this.apiService.get<Product[]>(`${this.path}/subgroup/${subGroupId}`);
  }

  search(term: string): Observable<Product[]> {
    const params = new HttpParams().set('term', term);
    return this.apiService.get<Product[]>(`${this.path}/search`, params);
  }

  create(product: CreateProduct): Observable<Product> {
    return this.apiService.post<Product, CreateProduct>(this.path, product);
  }

  update(id: number, product: UpdateProduct): Observable<void> {
    return this.apiService.put<void, UpdateProduct>(`${this.path}/${id}`, product);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  importProducts(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<any>(`${this.apiUrl}/${this.path}/import`, formData);
  }

  downloadImportTemplate(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${this.path}/import/template`, {
      responseType: 'blob'
    });
  }
}
