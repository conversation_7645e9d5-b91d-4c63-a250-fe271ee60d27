using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class StockTransfersController : ApiControllerBase
{
    private readonly IStockTransferService _stockTransferService;

    public StockTransfersController(IStockTransferService stockTransferService)
    {
        _stockTransferService = stockTransferService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<StockTransferHeaderDto>>> GetAll()
    {
        var stockTransfers = await _stockTransferService.GetAllStockTransfersAsync();
        return Ok(stockTransfers);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<StockTransferHeaderDto>> GetById(int id)
    {
        var stockTransfer = await _stockTransferService.GetStockTransferByIdAsync(id);
        if (stockTransfer == null)
            return NotFound();

        return Ok(stockTransfer);
    }

    [HttpGet("from-cost-center/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockTransferHeaderDto>>> GetByFromCostCenterId(int costCenterId)
    {
        var stockTransfers = await _stockTransferService.GetStockTransfersByFromCostCenterIdAsync(costCenterId);
        return Ok(stockTransfers);
    }

    [HttpGet("to-cost-center/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockTransferHeaderDto>>> GetByCostCenterId(int costCenterId)
    {
        var stockTransfers = await _stockTransferService.GetStockTransfersByToCostCenterIdAsync(costCenterId);
        return Ok(stockTransfers);
    }

    [HttpGet("status/{status}")]
    public async Task<ActionResult<IEnumerable<StockTransferHeaderDto>>> GetByStatus(string status)
    {
        var stockTransfers = await _stockTransferService.GetStockTransfersByStatusAsync(status);
        return Ok(stockTransfers);
    }

    [HttpPost]
    public async Task<ActionResult<StockTransferHeaderDto>> Create(CreateStockTransferHeaderDto createStockTransferHeaderDto)
    {
        var stockTransfer = await _stockTransferService.CreateStockTransferAsync(createStockTransferHeaderDto);
        return CreatedAtAction(nameof(GetById), new { id = stockTransfer.Id }, stockTransfer);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateStockTransferHeaderDto updateStockTransferHeaderDto)
    {
        if (id != updateStockTransferHeaderDto.Id)
            return BadRequest();

        try
        {
            await _stockTransferService.UpdateStockTransferAsync(updateStockTransferHeaderDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _stockTransferService.DeleteStockTransferAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/complete")]
    public async Task<IActionResult> Complete(int id, CompleteStockTransferDto completeStockTransferDto)
    {
        if (id != completeStockTransferDto.Id)
            return BadRequest();

        try
        {
            await _stockTransferService.CompleteStockTransferAsync(completeStockTransferDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpPost("{id}/cancel")]
    public async Task<IActionResult> Cancel(int id)
    {
        try
        {
            await _stockTransferService.CancelStockTransferAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpGet("{stockTransferHeaderId}/details")]
    public async Task<ActionResult<IEnumerable<StockTransferDetailDto>>> GetDetails(int stockTransferHeaderId)
    {
        var details = await _stockTransferService.GetStockTransferDetailsAsync(stockTransferHeaderId);
        return Ok(details);
    }

    [HttpGet("details/{id}")]
    public async Task<ActionResult<StockTransferDetailDto>> GetDetailById(int id)
    {
        var detail = await _stockTransferService.GetStockTransferDetailByIdAsync(id);
        if (detail == null)
            return NotFound();

        return Ok(detail);
    }

    [HttpPost("{stockTransferHeaderId}/details")]
    public async Task<ActionResult<StockTransferDetailDto>> CreateDetail(int stockTransferHeaderId, CreateStockTransferDetailDto createStockTransferDetailDto)
    {
        try
        {
            var detail = await _stockTransferService.CreateStockTransferDetailAsync(stockTransferHeaderId, createStockTransferDetailDto);
            return CreatedAtAction(nameof(GetDetailById), new { id = detail.Id }, detail);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("details/{id}")]
    public async Task<IActionResult> UpdateDetail(int id, UpdateStockTransferDetailDto updateStockTransferDetailDto)
    {
        if (id != updateStockTransferDetailDto.Id)
            return BadRequest();

        try
        {
            await _stockTransferService.UpdateStockTransferDetailAsync(updateStockTransferDetailDto);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }

    [HttpDelete("details/{id}")]
    public async Task<IActionResult> DeleteDetail(int id)
    {
        try
        {
            await _stockTransferService.DeleteStockTransferDetailAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }

        return NoContent();
    }
}
