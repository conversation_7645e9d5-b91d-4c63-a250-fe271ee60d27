export interface StockRequestHeader {
  id: number;
  referenceNumber: string;
  fromCostCenterId: number;
  fromCostCenterName: string;
  toCostCenterId: number;
  toCostCenterName: string;
  requestDate: Date;
  notes?: string;
  status: string;
  createdById?: number;
  createdByName?: string;
  approvedById?: number;
  approvedByName?: string;
  approvedAt?: Date;
  completedById?: number;
  completedByName?: string;
  completedAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  details?: StockRequestDetail[];
}

export interface StockRequestDetail {
  id: number;
  stockRequestHeaderId: number;
  productId: number;
  productName: string;
  productCode: string;
  batchId?: number;
  batchNumber?: string;
  unitId?: number;
  unitName?: string;
  quantity: number;
  price?: number;
  total?: number;
  deliveryDate?: Date;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateStockRequestHeader {
  fromCostCenterId: number;
  toCostCenterId: number;
  requestDate: Date;
  notes?: string;
  details: CreateStockRequestDetail[];
}

export interface CreateStockRequestDetail {
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  price?: number;
  deliveryDate?: Date;
  notes?: string;
}

export interface UpdateStockRequestHeader {
  id: number;
  fromCostCenterId: number;
  toCostCenterId: number;
  requestDate: Date;
  notes?: string;
  status: string;
}

export interface UpdateStockRequestDetail {
  id: number;
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  price?: number;
  deliveryDate?: Date;
  notes?: string;
}

export interface SubmitStockRequest {
  id: number;
  notes?: string;
}

export interface ApproveStockRequest {
  id: number;
  notes?: string;
}

export interface RejectStockRequest {
  id: number;
  notes?: string;
}

export interface CompleteStockRequest {
  id: number;
  notes?: string;
}
