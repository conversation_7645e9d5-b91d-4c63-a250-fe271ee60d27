import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { environment } from '../../../environments/environment';

import { Unit } from '../models/unit.model';

// Re-export Unit type for backward compatibility
export { Unit } from '../models/unit.model';

export interface CreateUnit {
  name: string;
  abbreviation: string;
  unitGroupId: number;
  conversionFactor: number;
  baseConversionFactor?: number;
  isBaseUnit: boolean;
}

export interface UpdateUnit {
  id: number;
  name: string;
  abbreviation: string;
  unitGroupId: number;
  conversionFactor: number;
  baseConversionFactor?: number;
  isBaseUnit: boolean;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class UnitService {
  private readonly path = 'units';
  private readonly apiUrl = environment.apiUrl;

  constructor(private apiService: ApiService, private http: HttpClient) { }

  getAll(): Observable<Unit[]> {
    return this.apiService.get<Unit[]>(this.path);
  }

  getAllUnits(): Observable<Unit[]> {
    return this.getAll();
  }

  getById(id: number): Observable<Unit> {
    return this.apiService.get<Unit>(`${this.path}/${id}`);
  }

  getByUnitGroupId(unitGroupId: number): Observable<Unit[]> {
    return this.apiService.get<Unit[]>(`${this.path}/by-unit-group/${unitGroupId}`);
  }

  create(unit: CreateUnit): Observable<Unit> {
    return this.apiService.post<Unit, CreateUnit>(this.path, unit);
  }

  update(id: number, unit: UpdateUnit): Observable<void> {
    return this.apiService.put<void, UpdateUnit>(`${this.path}/${id}`, unit);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  importUnits(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<any>(`${this.apiUrl}/${this.path}/import`, formData);
  }

  downloadImportTemplate(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${this.path}/import/template`, {
      responseType: 'blob'
    });
  }
}
